module.exports = {

"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getEmbeddedExplorerHTML": (()=>getEmbeddedExplorerHTML),
    "getEmbeddedSandboxHTML": (()=>getEmbeddedSandboxHTML)
});
function getConfigStringForHtml(config) {
    return JSON.stringify(config).replace('<', '\\u003c').replace('>', '\\u003e').replace('&', '\\u0026').replace("'", '\\u0027');
}
const getEmbeddedExplorerHTML = (explorerCdnVersion, config, apolloServerVersion, nonce)=>{
    const productionLandingPageEmbedConfigOrDefault = {
        displayOptions: {},
        persistExplorerState: false,
        runTelemetry: true,
        ...typeof config.embed === 'boolean' ? {} : config.embed
    };
    const embeddedExplorerParams = {
        graphRef: config.graphRef,
        target: '#embeddableExplorer',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                headers: config.headers,
                variables: config.variables
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            displayOptions: {
                ...productionLandingPageEmbedConfigOrDefault.displayOptions
            }
        },
        persistExplorerState: productionLandingPageEmbedConfigOrDefault.persistExplorerState,
        includeCookies: config.includeCookies,
        runtime: apolloServerVersion,
        runTelemetry: productionLandingPageEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Explorer cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableExplorer {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableExplorer"></div>
<script nonce="${nonce}" src="https://embeddable-explorer.cdn.apollographql.com/${encodeURIComponent(explorerCdnVersion)}/embeddable-explorer.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var endpointUrl = window.location.href;
  var embeddedExplorerConfig = ${getConfigStringForHtml(embeddedExplorerParams)};
  new window.EmbeddedExplorer({
    ...embeddedExplorerConfig,
    endpointUrl,
  });
</script>
`;
};
const getEmbeddedSandboxHTML = (sandboxCdnVersion, config, apolloServerVersion, nonce)=>{
    const localDevelopmentEmbedConfigOrDefault = {
        runTelemetry: true,
        endpointIsEditable: false,
        initialState: {},
        ...typeof config.embed === 'boolean' ? {} : config.embed ?? {}
    };
    const embeddedSandboxConfig = {
        target: '#embeddableSandbox',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                variables: config.variables,
                headers: config.headers
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            includeCookies: config.includeCookies,
            ...localDevelopmentEmbedConfigOrDefault.initialState
        },
        hideCookieToggle: false,
        endpointIsEditable: localDevelopmentEmbedConfigOrDefault.endpointIsEditable,
        runtime: apolloServerVersion,
        runTelemetry: localDevelopmentEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Sandbox cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableSandbox {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableSandbox"></div>
<script nonce="${nonce}" src="https://embeddable-sandbox.cdn.apollographql.com/${encodeURIComponent(sandboxCdnVersion)}/embeddable-sandbox.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var initialEndpoint = window.location.href;
  var embeddedSandboxConfig = ${getConfigStringForHtml(embeddedSandboxConfig)};
  new window.EmbeddedSandbox(
    {
      ...embeddedSandboxConfig,
      initialEndpoint,
    }
  );
</script>
`;
}; //# sourceMappingURL=getEmbeddedHTML.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/generated/packageVersion.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "packageVersion": (()=>packageVersion)
});
const packageVersion = "4.12.2"; //# sourceMappingURL=packageVersion.js.map
}}),
"[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "v4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript)");
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApolloServerPluginLandingPageLocalDefault": (()=>ApolloServerPluginLandingPageLocalDefault),
    "ApolloServerPluginLandingPageProductionDefault": (()=>ApolloServerPluginLandingPageProductionDefault),
    "DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION": (()=>DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION),
    "DEFAULT_EMBEDDED_EXPLORER_VERSION": (()=>DEFAULT_EMBEDDED_EXPLORER_VERSION),
    "DEFAULT_EMBEDDED_SANDBOX_VERSION": (()=>DEFAULT_EMBEDDED_SANDBOX_VERSION)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$generated$2f$packageVersion$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/generated/packageVersion.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm-node/v4.js [app-route] (ecmascript) <export default as v4>");
;
;
;
;
function ApolloServerPluginLandingPageLocalDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = {
        embed: true,
        ...options
    };
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: false,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
function ApolloServerPluginLandingPageProductionDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = options;
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: true,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
function encodeConfig(config) {
    return JSON.stringify(encodeURIComponent(JSON.stringify(config)));
}
const getNonEmbeddedLandingPageHTML = (cdnVersion, config, apolloServerVersion, nonce)=>{
    const encodedConfig = encodeConfig(config);
    return `
 <div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>The full landing page cannot be loaded; it appears that you might be offline.</p>
</div>
<script nonce="${nonce}">window.landingPage = ${encodedConfig};</script>
<script nonce="${nonce}" src="https://apollo-server-landing-page.cdn.apollographql.com/${encodeURIComponent(cdnVersion)}/static/js/main.js?runtime=${apolloServerVersion}"></script>`;
};
const DEFAULT_EMBEDDED_EXPLORER_VERSION = 'v3';
const DEFAULT_EMBEDDED_SANDBOX_VERSION = 'v2';
const DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION = '_latest';
function ApolloServerPluginLandingPageDefault(maybeVersion, config) {
    const explorerVersion = maybeVersion ?? DEFAULT_EMBEDDED_EXPLORER_VERSION;
    const sandboxVersion = maybeVersion ?? DEFAULT_EMBEDDED_SANDBOX_VERSION;
    const apolloServerLandingPageVersion = maybeVersion ?? DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION;
    const apolloServerVersion = `@apollo/server@${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$generated$2f$packageVersion$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["packageVersion"]}`;
    const scriptSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com'
    ].join(' ');
    const styleSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com',
        'https://fonts.googleapis.com'
    ].join(' ');
    const iframeSafeList = [
        'https://explorer.embed.apollographql.com',
        'https://sandbox.embed.apollographql.com',
        'https://embed.apollo.local:3000'
    ].join(' ');
    return {
        __internal_installed_implicitly__: false,
        async serverWillStart (server) {
            if (config.precomputedNonce) {
                server.logger.warn("The `precomputedNonce` landing page configuration option is deprecated. Removing this option is strictly an improvement to Apollo Server's landing page Content Security Policy (CSP) implementation for preventing XSS attacks.");
            }
            return {
                async renderLandingPage () {
                    const encodedASLandingPageVersion = encodeURIComponent(apolloServerLandingPageVersion);
                    async function html() {
                        const nonce = config.precomputedNonce ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2d$node$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])()).digest('hex');
                        const scriptCsp = `script-src 'self' 'nonce-${nonce}' ${scriptSafeList}`;
                        const styleCsp = `style-src 'nonce-${nonce}' ${styleSafeList}`;
                        const imageCsp = `img-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const manifestCsp = `manifest-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const frameCsp = `frame-src ${iframeSafeList}`;
                        return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Security-Policy" content="${scriptCsp}; ${styleCsp}; ${imageCsp}; ${manifestCsp}; ${frameCsp}" />
    <link
      rel="icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro&display=swap"
      rel="stylesheet"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Apollo server landing page" />
    <link
      rel="apple-touch-icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <link
      rel="manifest"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/manifest.json"
    />
    <title>Apollo Server</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="react-root">
      <style nonce=${nonce}>
        body {
          margin: 0;
          overflow-x: hidden;
          overflow-y: hidden;
        }
        .fallback {
          opacity: 0;
          animation: fadeIn 1s 1s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
          padding: 1em;
        }
        @keyframes fadeIn {
          0% {opacity:0;}
          100% {opacity:1; }
        }
      </style>
    ${config.embed ? 'graphRef' in config && config.graphRef ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEmbeddedExplorerHTML"])(explorerVersion, config, apolloServerVersion, nonce) : !('graphRef' in config) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$plugin$2f$landingPage$2f$default$2f$getEmbeddedHTML$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getEmbeddedSandboxHTML"])(sandboxVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce)}
    </div>
  </body>
</html>
          `;
                    }
                    return {
                        html
                    };
                }
            };
        }
    };
} //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=node_modules_e9bdf046._.js.map