const CHUNK_PUBLIC_PATH = "server/app/api/graphql/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_180e0abd._.js");
runtime.loadChunk("server/chunks/node_modules_next_dist_70e42d6e._.js");
runtime.loadChunk("server/chunks/node_modules_graphql_88585241._.js");
runtime.loadChunk("server/chunks/node_modules_1727a4bc._.js");
runtime.loadChunk("server/chunks/node_modules_86178e31._.js");
runtime.loadChunk("server/chunks/node_modules_@apollo_server_dist_87fceaeb._.js");
runtime.loadChunk("server/chunks/node_modules_@apollo_usage-reporting-protobuf_generated_cjs_protobuf_0f628d06.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_convex_dist_esm_c1d5010b._.js");
runtime.loadChunk("server/chunks/node_modules_5f707f1e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__79cdc10b._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/graphql/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
