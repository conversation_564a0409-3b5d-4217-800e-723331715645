{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "getEmbeddedHTML.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/getEmbeddedHTML.ts"], "names": [], "mappings": ";;;;AAcA,SAAS,sBAAsB,CAAC,MAAc;IAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC7B,CAAC;AAEM,MAAM,uBAAuB,GAAG,CACrC,kBAA0B,EAC1B,MAAqE,EACrE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IA2BF,MAAM,yCAAyC,GAAG;QAChD,cAAc,EAAE,CAAA,CAAE;QAClB,oBAAoB,EAAE,KAAK;QAC3B,YAAY,EAAE,IAAI;QAClB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;KAC3D,CAAC;IACF,MAAM,sBAAsB,GAGF;QACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,qBAAqB;QAC7B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE;gBACd,GAAG,yCAAyC,CAAC,cAAc;aAC5D;SACF;QACD,oBAAoB,EAClB,yCAAyC,CAAC,oBAAoB;QAChE,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,yCAAyC,CAAC,YAAY;QACpE,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAEF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,yDAAA,EAA4D,kBAAkB,CAChG,kBAAkB,CACnB,CAAA,mDAAA,EAAsD,kBAAkB,CACvE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;iCAEW,sBAAsB,CACnD,sBAAsB,CACvB,CAAA;;;;;;CAMF,CAAC;AACF,CAAC,CAAC;AAEK,MAAM,sBAAsB,GAAG,CACpC,iBAAyB,EACzB,MAAgE,EAChE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,oCAAoC,GAAG;QAC3C,YAAY,EAAE,IAAI;QAClB,kBAAkB,EAAE,KAAK;QACzB,YAAY,EAAE,CAAA,CAAE;QAChB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,AAAC,MAAM,CAAC,KAAK,IAAI,CAAA,CAAE,AAAC,CAAC;KACnE,CAAC;IACF,MAAM,qBAAqB,GAAG;QAC5B,MAAM,EAAE,oBAAoB;QAC5B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,GAAG,oCAAoC,CAAC,YAAY;SACrD;QACD,gBAAgB,EAAE,KAAK;QACvB,kBAAkB,EAAE,oCAAoC,CAAC,kBAAkB;QAC3E,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,oCAAoC,CAAC,YAAY;QAC/D,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IACF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,wDAAA,EAA2D,kBAAkB,CAC/F,iBAAiB,CAClB,CAAA,kDAAA,EAAqD,kBAAkB,CACtE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;gCAEU,sBAAsB,CAAC,qBAAqB,CAAC,CAAA;;;;;;;;CAQ5E,CAAC;AACF,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "file": "packageVersion.js", "sourceRoot": "", "sources": ["../../../src/generated/packageVersion.ts"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc,GAAG,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/index.ts"], "names": [], "mappings": ";;;;;;;AAUA,OAAO,EACL,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;;;;;AAO9B,SAAU,yCAAyC,CACvD,UAA4D,CAAA,CAAE;IAE9D,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG;QAEzD,KAAK,EAAE,IAAa;QACpB,GAAG,OAAO;KACX,CAAC;IACF,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,KAAK;QACb,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,8CAA8C,CAC5D,UAAiE,CAAA,CAAE;IAEnE,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IACnE,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,IAAI;QACZ,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAUD,SAAS,YAAY,CAAC,MAAyB;IAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,MAAM,6BAA6B,GAAG,CACpC,UAAkB,EAClB,MAAyB,EACzB,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAE3C,OAAO,CAAA;;;;;iBAKQ,KAAK,CAAA,uBAAA,EAA0B,aAAa,CAAA;iBAC5C,KAAK,CAAA,gEAAA,EAAmE,kBAAkB,CACvG,UAAU,CACX,CAAA,2BAAA,EAA8B,mBAAmB,CAAA,WAAA,CAAa,CAAC;AAClE,CAAC,CAAC;AAEK,MAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,MAAM,gCAAgC,GAAG,IAAI,CAAC;AAC9C,MAAM,0CAA0C,GAAG,SAAS,CAAC;AAGpE,SAAS,oCAAoC,CAC3C,YAAgC,EAChC,MAGC;IAED,MAAM,eAAe,GAAG,YAAY,IAAI,iCAAiC,CAAC;IAC1E,MAAM,cAAc,GAAG,YAAY,IAAI,gCAAgC,CAAC;IACxE,MAAM,8BAA8B,GAClC,YAAY,IAAI,0CAA0C,CAAC;IAC7D,MAAM,mBAAmB,GAAG,CAAA,eAAA,qLAAkB,iBAAc,EAAE,CAAC;IAE/D,MAAM,cAAc,GAAG;QACrB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;KACpD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,aAAa,GAAG;QACpB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;QACnD,8BAA8B;KAC/B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,cAAc,GAAG;QACrB,0CAA0C;QAC1C,yCAAyC;QACzC,iCAAiC;KAClC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,OAAO;QACL,iCAAiC,EAAE,KAAK;QACxC,KAAK,CAAC,eAAe,EAAC,MAAM;YAC1B,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,kOAAkO,CACnO,CAAC;YACJ,CAAC;YACD,OAAO;gBACL,KAAK,CAAC,iBAAiB;oBACrB,MAAM,2BAA2B,GAAG,kBAAkB,CACpD,8BAA8B,CAC/B,CAAC;oBACF,KAAK,UAAU,IAAI;wBACjB,MAAM,KAAK,GACT,MAAM,CAAC,gBAAgB,2KACvB,aAAA,AAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,KAAC,0LAAA,AAAM,EAAE,CAAC,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACtD,MAAM,SAAS,GAAG,CAAA,yBAAA,EAA4B,KAAK,CAAA,EAAA,EAAK,cAAc,EAAE,CAAC;wBACzE,MAAM,QAAQ,GAAG,CAAA,iBAAA,EAAoB,KAAK,CAAA,EAAA,EAAK,aAAa,EAAE,CAAC;wBAC/D,MAAM,QAAQ,GAAG,CAAA,gEAAA,CAAkE,CAAC;wBACpF,MAAM,WAAW,GAAG,CAAA,qEAAA,CAAuE,CAAC;wBAC5F,MAAM,QAAQ,GAAG,CAAA,UAAA,EAAa,cAAc,EAAE,CAAC;wBAC/C,OAAO,CAAA;;;;;0DAKuC,SAAS,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,WAAW,CAAA,EAAA,EAAK,QAAQ,CAAA;;;uEAGnD,2BAA2B,CAAA;;;;;;;;;;;;uEAY3B,2BAA2B,CAAA;;;;uEAI3B,2BAA2B,CAAA;;;;;;;qBAO7E,KAAK,CAAA;;;;;;;;;;;;;;;;;;MAmBpB,MAAM,CAAC,KAAK,GACR,UAAU,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,kNACrC,0BAAA,AAAuB,EACrB,eAAe,EACf,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,IACrB,uOAAA,AAAsB,EACpB,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACL,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CAEb,CAAA;;;;WAIO,CAAC;oBACF,CAAC;oBACD,OAAO;wBAAE,IAAI;oBAAA,CAAE,CAAC;gBAClB,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}]}