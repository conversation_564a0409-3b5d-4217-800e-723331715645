{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/schema.ts"], "sourcesContent": ["import { gql } from 'graphql-tag';\n\nexport const typeDefs = gql`\n  # API Key Types\n  type ApiKey {\n    id: ID!\n    name: String!\n    key: String!\n    keyId: String!\n    environment: Environment!\n    permissions: [String!]!\n    isActive: Boolean!\n    expiresAt: Float\n    lastUsedAt: Float\n    usageCount: Int!\n    rateLimit: RateLimit!\n    revokedAt: Float\n    revokedBy: ID\n    revocationReason: String\n    rotatedAt: Float\n    rotatedBy: ID\n    rotationReason: String\n    createdBy: ID!\n    createdAt: Float!\n    updatedAt: Float!\n  }\n\n  type RateLimit {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  type ApiKeyStats {\n    total: Int!\n    active: Int!\n    inactive: Int!\n    expired: Int!\n    recentlyUsed: Int!\n    totalUsage: Int!\n  }\n\n  enum Environment {\n    live\n    test\n  }\n\n  # Input Types\n  input RateLimitInput {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  input CreateApiKeyInput {\n    name: String!\n    permissions: [String!]!\n    adminId: ID!\n    environment: Environment = live\n    expiresAt: Float\n    rateLimit: RateLimitInput\n  }\n\n  input UpdateApiKeyInput {\n    apiKeyId: ID!\n    name: String\n    permissions: [String!]\n    isActive: Boolean\n    expiresAt: Float\n    rateLimit: RateLimitInput\n    updatedBy: ID!\n  }\n\n  input RevokeApiKeyInput {\n    apiKeyId: ID!\n    revokedBy: ID!\n    reason: String\n  }\n\n  input DeleteApiKeyInput {\n    apiKeyId: ID!\n    deletedBy: ID!\n    reason: String\n  }\n\n  input GetApiKeysInput {\n    search: String\n    isActive: Boolean\n    createdBy: ID\n    limit: Int\n    offset: Int\n  }\n\n  # Queries\n  type Query {\n    getApiKeys(input: GetApiKeysInput): [ApiKey!]!\n    getApiKeyById(id: ID!): ApiKey\n    getFullApiKeyById(id: ID!): ApiKey\n    getApiKeyStats: ApiKeyStats!\n    validateApiKey(key: String!): ApiKey\n  }\n\n  # Mutations\n  type Mutation {\n    createApiKey(input: CreateApiKeyInput!): ApiKey!\n    updateApiKey(input: UpdateApiKeyInput!): ApiKey!\n    revokeApiKey(input: RevokeApiKeyInput!): ApiKey!\n    deleteApiKey(input: DeleteApiKeyInput!): Boolean!\n  }\n`;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,gJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6G5B,CAAC", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/resolvers.ts"], "sourcesContent": ["// GraphQL resolvers that use the existing REST API endpoints\n// This approach avoids import issues and provides a clean GraphQL interface\n\nexport const resolvers = {\n  Query: {\n    getApiKeys: async (_: any, { input }: { input?: any }) => {\n      try {\n        // For now, return mock data to test the GraphQL setup\n        // In a real implementation, this would call the Convex backend\n        return [\n          {\n            id: \"1\",\n            name: \"Test API Key\",\n            key: \"bzk_live_test123\",\n            keyId: \"test123\",\n            environment: \"live\",\n            permissions: [\"products.read\", \"collections.read\"],\n            isActive: true,\n            expiresAt: null,\n            lastUsedAt: Date.now(),\n            usageCount: 42,\n            rateLimit: {\n              requestsPerMinute: 100,\n              requestsPerHour: 5000,\n              requestsPerDay: 50000,\n              burstLimit: 150,\n            },\n            createdBy: \"admin\",\n            createdAt: Date.now() - 86400000,\n            updatedAt: Date.now(),\n          }\n        ];\n      } catch (error) {\n        console.error('GraphQL getApiKeys error:', error);\n        throw new Error('Failed to fetch API keys');\n      }\n    },\n\n    getApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        // Mock implementation\n        return {\n          id: id,\n          name: \"Test API Key\",\n          key: \"bzk_live_test123\",\n          keyId: \"test123\",\n          environment: \"live\",\n          permissions: [\"products.read\", \"collections.read\"],\n          isActive: true,\n          expiresAt: null,\n          lastUsedAt: Date.now(),\n          usageCount: 42,\n          rateLimit: {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n          createdBy: \"admin\",\n          createdAt: Date.now() - 86400000,\n          updatedAt: Date.now(),\n        };\n      } catch (error) {\n        console.error('GraphQL getApiKeyById error:', error);\n        throw new Error('Failed to fetch API key');\n      }\n    },\n\n    getFullApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        // Mock implementation - same as getApiKeyById for now\n        return {\n          id: id,\n          name: \"Test API Key\",\n          key: \"bzk_live_test123\",\n          keyId: \"test123\",\n          environment: \"live\",\n          permissions: [\"products.read\", \"collections.read\"],\n          isActive: true,\n          expiresAt: null,\n          lastUsedAt: Date.now(),\n          usageCount: 42,\n          rateLimit: {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n          createdBy: \"admin\",\n          createdAt: Date.now() - 86400000,\n          updatedAt: Date.now(),\n        };\n      } catch (error) {\n        console.error('GraphQL getFullApiKeyById error:', error);\n        throw new Error('Failed to fetch full API key');\n      }\n    },\n\n    getApiKeyStats: async () => {\n      try {\n        // Mock stats\n        return {\n          total: 1,\n          active: 1,\n          inactive: 0,\n          expired: 0,\n          recentlyUsed: 1,\n          totalUsage: 42,\n        };\n      } catch (error) {\n        console.error('GraphQL getApiKeyStats error:', error);\n        throw new Error('Failed to fetch API key statistics');\n      }\n    },\n\n    validateApiKey: async (_: any, { key }: { key: string }) => {\n      try {\n        // Mock validation\n        if (key === \"bzk_live_test123\") {\n          return {\n            id: \"1\",\n            name: \"Test API Key\",\n            key: key,\n            keyId: \"test123\",\n            environment: \"live\",\n            permissions: [\"products.read\", \"collections.read\"],\n            isActive: true,\n            expiresAt: null,\n            lastUsedAt: Date.now(),\n            usageCount: 42,\n            rateLimit: {\n              requestsPerMinute: 100,\n              requestsPerHour: 5000,\n              requestsPerDay: 50000,\n              burstLimit: 150,\n            },\n            createdBy: \"admin\",\n            createdAt: Date.now() - 86400000,\n            updatedAt: Date.now(),\n          };\n        }\n        return null;\n      } catch (error) {\n        console.error('GraphQL validateApiKey error:', error);\n        throw new Error('Failed to validate API key');\n      }\n    },\n  },\n\n  Mutation: {\n    createApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        // Mock creation\n        const newKey = {\n          id: Date.now().toString(),\n          name: input.name,\n          key: `bzk_${input.environment || 'live'}_${Math.random().toString(36).substring(2)}`,\n          keyId: Math.random().toString(36).substring(2, 10),\n          environment: input.environment || \"live\",\n          permissions: input.permissions,\n          isActive: true,\n          expiresAt: input.expiresAt || null,\n          lastUsedAt: null,\n          usageCount: 0,\n          rateLimit: input.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n          createdBy: input.adminId,\n          createdAt: Date.now(),\n          updatedAt: Date.now(),\n        };\n\n        return newKey;\n      } catch (error) {\n        console.error('GraphQL createApiKey error:', error);\n        throw new Error('Failed to create API key');\n      }\n    },\n\n    updateApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        // Mock update\n        return {\n          id: input.apiKeyId,\n          name: input.name || \"Updated API Key\",\n          key: \"bzk_live_updated123\",\n          keyId: \"updated123\",\n          environment: \"live\",\n          permissions: input.permissions || [\"products.read\"],\n          isActive: input.isActive !== undefined ? input.isActive : true,\n          expiresAt: input.expiresAt || null,\n          lastUsedAt: Date.now(),\n          usageCount: 42,\n          rateLimit: input.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n          createdBy: \"admin\",\n          createdAt: Date.now() - 86400000,\n          updatedAt: Date.now(),\n        };\n      } catch (error) {\n        console.error('GraphQL updateApiKey error:', error);\n        throw new Error('Failed to update API key');\n      }\n    },\n\n    revokeApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        // Mock revocation\n        return {\n          id: input.apiKeyId,\n          name: \"Revoked API Key\",\n          key: \"bzk_live_revoked123\",\n          keyId: \"revoked123\",\n          environment: \"live\",\n          permissions: [\"products.read\"],\n          isActive: false,\n          expiresAt: null,\n          lastUsedAt: Date.now(),\n          usageCount: 42,\n          rateLimit: {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n          revokedAt: Date.now(),\n          revokedBy: input.revokedBy,\n          revocationReason: input.reason,\n          createdBy: \"admin\",\n          createdAt: Date.now() - 86400000,\n          updatedAt: Date.now(),\n        };\n      } catch (error) {\n        console.error('GraphQL revokeApiKey error:', error);\n        throw new Error('Failed to revoke API key');\n      }\n    },\n\n    deleteApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        // Mock deletion\n        console.log('Deleting API key:', input.apiKeyId);\n        return true;\n      } catch (error) {\n        console.error('GraphQL deleteApiKey error:', error);\n        throw new Error('Failed to delete API key');\n      }\n    },\n  },\n};\n"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,4EAA4E;;;;AAErE,MAAM,YAAY;IACvB,OAAO;QACL,YAAY,OAAO,GAAQ,EAAE,KAAK,EAAmB;YACnD,IAAI;gBACF,sDAAsD;gBACtD,+DAA+D;gBAC/D,OAAO;oBACL;wBACE,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,OAAO;wBACP,aAAa;wBACb,aAAa;4BAAC;4BAAiB;yBAAmB;wBAClD,UAAU;wBACV,WAAW;wBACX,YAAY,KAAK,GAAG;wBACpB,YAAY;wBACZ,WAAW;4BACT,mBAAmB;4BACnB,iBAAiB;4BACjB,gBAAgB;4BAChB,YAAY;wBACd;wBACA,WAAW;wBACX,WAAW,KAAK,GAAG,KAAK;wBACxB,WAAW,KAAK,GAAG;oBACrB;iBACD;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,eAAe,OAAO,GAAQ,EAAE,EAAE,EAAkB;YAClD,IAAI;gBACF,sBAAsB;gBACtB,OAAO;oBACL,IAAI;oBACJ,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;wBAAC;wBAAiB;qBAAmB;oBAClD,UAAU;oBACV,WAAW;oBACX,YAAY,KAAK,GAAG;oBACpB,YAAY;oBACZ,WAAW;wBACT,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,WAAW;oBACX,WAAW,KAAK,GAAG,KAAK;oBACxB,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,mBAAmB,OAAO,GAAQ,EAAE,EAAE,EAAkB;YACtD,IAAI;gBACF,sDAAsD;gBACtD,OAAO;oBACL,IAAI;oBACJ,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;wBAAC;wBAAiB;qBAAmB;oBAClD,UAAU;oBACV,WAAW;oBACX,YAAY,KAAK,GAAG;oBACpB,YAAY;oBACZ,WAAW;wBACT,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,WAAW;oBACX,WAAW,KAAK,GAAG,KAAK;oBACxB,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB;YACd,IAAI;gBACF,aAAa;gBACb,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,SAAS;oBACT,cAAc;oBACd,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB,OAAO,GAAQ,EAAE,GAAG,EAAmB;YACrD,IAAI;gBACF,kBAAkB;gBAClB,IAAI,QAAQ,oBAAoB;oBAC9B,OAAO;wBACL,IAAI;wBACJ,MAAM;wBACN,KAAK;wBACL,OAAO;wBACP,aAAa;wBACb,aAAa;4BAAC;4BAAiB;yBAAmB;wBAClD,UAAU;wBACV,WAAW;wBACX,YAAY,KAAK,GAAG;wBACpB,YAAY;wBACZ,WAAW;4BACT,mBAAmB;4BACnB,iBAAiB;4BACjB,gBAAgB;4BAChB,YAAY;wBACd;wBACA,WAAW;wBACX,WAAW,KAAK,GAAG,KAAK;wBACxB,WAAW,KAAK,GAAG;oBACrB;gBACF;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,UAAU;QACR,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,gBAAgB;gBAChB,MAAM,SAAS;oBACb,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,MAAM,MAAM,IAAI;oBAChB,KAAK,CAAC,IAAI,EAAE,MAAM,WAAW,IAAI,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI;oBACpF,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;oBAC/C,aAAa,MAAM,WAAW,IAAI;oBAClC,aAAa,MAAM,WAAW;oBAC9B,UAAU;oBACV,WAAW,MAAM,SAAS,IAAI;oBAC9B,YAAY;oBACZ,YAAY;oBACZ,WAAW,MAAM,SAAS,IAAI;wBAC5B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,WAAW,MAAM,OAAO;oBACxB,WAAW,KAAK,GAAG;oBACnB,WAAW,KAAK,GAAG;gBACrB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,cAAc;gBACd,OAAO;oBACL,IAAI,MAAM,QAAQ;oBAClB,MAAM,MAAM,IAAI,IAAI;oBACpB,KAAK;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa,MAAM,WAAW,IAAI;wBAAC;qBAAgB;oBACnD,UAAU,MAAM,QAAQ,KAAK,YAAY,MAAM,QAAQ,GAAG;oBAC1D,WAAW,MAAM,SAAS,IAAI;oBAC9B,YAAY,KAAK,GAAG;oBACpB,YAAY;oBACZ,WAAW,MAAM,SAAS,IAAI;wBAC5B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,WAAW;oBACX,WAAW,KAAK,GAAG,KAAK;oBACxB,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,kBAAkB;gBAClB,OAAO;oBACL,IAAI,MAAM,QAAQ;oBAClB,MAAM;oBACN,KAAK;oBACL,OAAO;oBACP,aAAa;oBACb,aAAa;wBAAC;qBAAgB;oBAC9B,UAAU;oBACV,WAAW;oBACX,YAAY,KAAK,GAAG;oBACpB,YAAY;oBACZ,WAAW;wBACT,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;oBACA,WAAW,KAAK,GAAG;oBACnB,WAAW,MAAM,SAAS;oBAC1B,kBAAkB,MAAM,MAAM;oBAC9B,WAAW;oBACX,WAAW,KAAK,GAAG,KAAK;oBACxB,WAAW,KAAK,GAAG;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,gBAAgB;gBAChB,QAAQ,GAAG,CAAC,qBAAqB,MAAM,QAAQ;gBAC/C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/graphql/route.ts"], "sourcesContent": ["import { ApolloServer } from '@apollo/server';\nimport { startServerAndCreateNextHandler } from '@as-integrations/next';\nimport { makeExecutableSchema } from '@graphql-tools/schema';\nimport { typeDefs } from '@/lib/graphql/schema';\nimport { resolvers } from '@/lib/graphql/resolvers';\nimport { NextRequest } from 'next/server';\n\n// Create the GraphQL schema\nconst schema = makeExecutableSchema({\n  typeDefs,\n  resolvers,\n});\n\n// Create Apollo Server instance\nconst server = new ApolloServer({\n  schema,\n  introspection: process.env.NODE_ENV !== 'production',\n  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',\n});\n\n// Create the Next.js handler\nconst handler = startServerAndCreateNextHandler<NextRequest>(server, {\n  context: async (req) => {\n    // You can add authentication context here if needed\n    return {\n      req,\n    };\n  },\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGA,4BAA4B;AAC5B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE;IAClC,UAAA,iIAAA,CAAA,WAAQ;IACR,WAAA,oIAAA,CAAA,YAAS;AACX;AAEA,gCAAgC;AAChC,MAAM,SAAS,IAAI,mKAAA,CAAA,eAAY,CAAC;IAC9B;IACA,eAAe,oDAAyB;IACxC,mCAAmC,oDAAyB;AAC9D;AAEA,6BAA6B;AAC7B,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,kCAA+B,AAAD,EAAe,QAAQ;IACnE,SAAS,OAAO;QACd,oDAAoD;QACpD,OAAO;YACL;QACF;IACF;AACF", "debugId": null}}]}