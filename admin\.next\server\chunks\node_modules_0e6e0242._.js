module.exports = {

"[project]/node_modules/@apollo/utils.isnodelike/dist/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isNodeLike = void 0;
exports.isNodeLike = typeof process === "object" && process && process.release && process.versions && typeof process.versions.node === "string"; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/utils.keyvaluecache/dist/PrefixingKeyValueCache.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var _a;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.PrefixingKeyValueCache = void 0;
const prefixesAreUnnecessaryForIsolationSymbol = Symbol("prefixesAreUnnecessaryForIsolation");
class PrefixingKeyValueCache {
    constructor(wrapped, prefix){
        this.wrapped = wrapped;
        if (PrefixingKeyValueCache.prefixesAreUnnecessaryForIsolation(wrapped)) {
            this.prefix = "";
            this[prefixesAreUnnecessaryForIsolationSymbol] = true;
        } else {
            this.prefix = prefix;
        }
    }
    get(key) {
        return this.wrapped.get(this.prefix + key);
    }
    set(key, value, options) {
        return this.wrapped.set(this.prefix + key, value, options);
    }
    delete(key) {
        return this.wrapped.delete(this.prefix + key);
    }
    static prefixesAreUnnecessaryForIsolation(c) {
        return prefixesAreUnnecessaryForIsolationSymbol in c;
    }
    static cacheDangerouslyDoesNotNeedPrefixesForIsolation(c) {
        return new PrefixesAreUnnecessaryForIsolationCache(c);
    }
}
exports.PrefixingKeyValueCache = PrefixingKeyValueCache;
class PrefixesAreUnnecessaryForIsolationCache {
    constructor(wrapped){
        this.wrapped = wrapped;
        this[_a] = true;
    }
    get(key) {
        return this.wrapped.get(key);
    }
    set(key, value, options) {
        return this.wrapped.set(key, value, options);
    }
    delete(key) {
        return this.wrapped.delete(key);
    }
}
_a = prefixesAreUnnecessaryForIsolationSymbol; //# sourceMappingURL=PrefixingKeyValueCache.js.map
}}),
"[project]/node_modules/@apollo/utils.keyvaluecache/dist/InMemoryLRUCache.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InMemoryLRUCache = void 0;
const lru_cache_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/lru-cache/index.js [app-route] (ecmascript)"));
class InMemoryLRUCache {
    constructor(lruCacheOpts){
        this.cache = new lru_cache_1.default({
            sizeCalculation: InMemoryLRUCache.sizeCalculation,
            maxSize: Math.pow(2, 20) * 30,
            ...lruCacheOpts
        });
    }
    static sizeCalculation(item) {
        if (typeof item === "string") {
            return item.length;
        }
        if (typeof item === "object") {
            return Buffer.byteLength(JSON.stringify(item), "utf8");
        }
        return 1;
    }
    async set(key, value, options) {
        if (options === null || options === void 0 ? void 0 : options.ttl) {
            this.cache.set(key, value, {
                ttl: options.ttl * 1000
            });
        } else {
            this.cache.set(key, value);
        }
    }
    async get(key) {
        return this.cache.get(key);
    }
    async delete(key) {
        return this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
    }
    keys() {
        return [
            ...this.cache.keys()
        ];
    }
}
exports.InMemoryLRUCache = InMemoryLRUCache; //# sourceMappingURL=InMemoryLRUCache.js.map
}}),
"[project]/node_modules/@apollo/utils.keyvaluecache/dist/ErrorsAreMissesCache.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ErrorsAreMissesCache = void 0;
class ErrorsAreMissesCache {
    constructor(cache, logger){
        this.cache = cache;
        this.logger = logger;
    }
    async get(key) {
        try {
            return await this.cache.get(key);
        } catch (e) {
            if (this.logger) {
                if (e instanceof Error) {
                    this.logger.error(e.message);
                } else {
                    this.logger.error(e);
                }
            }
            return undefined;
        }
    }
    async set(key, value, opts) {
        return this.cache.set(key, value, opts);
    }
    async delete(key) {
        return this.cache.delete(key);
    }
}
exports.ErrorsAreMissesCache = ErrorsAreMissesCache; //# sourceMappingURL=ErrorsAreMissesCache.js.map
}}),
"[project]/node_modules/@apollo/utils.keyvaluecache/dist/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ErrorsAreMissesCache = exports.InMemoryLRUCache = exports.PrefixingKeyValueCache = void 0;
var PrefixingKeyValueCache_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.keyvaluecache/dist/PrefixingKeyValueCache.js [app-route] (ecmascript)");
Object.defineProperty(exports, "PrefixingKeyValueCache", {
    enumerable: true,
    get: function() {
        return PrefixingKeyValueCache_1.PrefixingKeyValueCache;
    }
});
var InMemoryLRUCache_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.keyvaluecache/dist/InMemoryLRUCache.js [app-route] (ecmascript)");
Object.defineProperty(exports, "InMemoryLRUCache", {
    enumerable: true,
    get: function() {
        return InMemoryLRUCache_1.InMemoryLRUCache;
    }
});
var ErrorsAreMissesCache_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.keyvaluecache/dist/ErrorsAreMissesCache.js [app-route] (ecmascript)");
Object.defineProperty(exports, "ErrorsAreMissesCache", {
    enumerable: true,
    get: function() {
        return ErrorsAreMissesCache_1.ErrorsAreMissesCache;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/lru-cache/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const perf = typeof performance === 'object' && performance && typeof performance.now === 'function' ? performance : Date;
const hasAbortController = typeof AbortController === 'function';
// minimal backwards-compatibility polyfill
// this doesn't have nearly all the checks and whatnot that
// actual AbortController/Signal has, but it's enough for
// our purposes, and if used properly, behaves the same.
const AC = hasAbortController ? AbortController : class AbortController1 {
    constructor(){
        this.signal = new AS();
    }
    abort(reason = new Error('This operation was aborted')) {
        this.signal.reason = this.signal.reason || reason;
        this.signal.aborted = true;
        this.signal.dispatchEvent({
            type: 'abort',
            target: this.signal
        });
    }
};
const hasAbortSignal = typeof AbortSignal === 'function';
// Some polyfills put this on the AC class, not global
const hasACAbortSignal = typeof AC.AbortSignal === 'function';
const AS = hasAbortSignal ? AbortSignal : hasACAbortSignal ? AC.AbortController : class AbortSignal1 {
    constructor(){
        this.reason = undefined;
        this.aborted = false;
        this._listeners = [];
    }
    dispatchEvent(e) {
        if (e.type === 'abort') {
            this.aborted = true;
            this.onabort(e);
            this._listeners.forEach((f)=>f(e), this);
        }
    }
    onabort() {}
    addEventListener(ev, fn) {
        if (ev === 'abort') {
            this._listeners.push(fn);
        }
    }
    removeEventListener(ev, fn) {
        if (ev === 'abort') {
            this._listeners = this._listeners.filter((f)=>f !== fn);
        }
    }
};
const warned = new Set();
const deprecatedOption = (opt, instead)=>{
    const code = `LRU_CACHE_OPTION_${opt}`;
    if (shouldWarn(code)) {
        warn(code, `${opt} option`, `options.${instead}`, LRUCache);
    }
};
const deprecatedMethod = (method, instead)=>{
    const code = `LRU_CACHE_METHOD_${method}`;
    if (shouldWarn(code)) {
        const { prototype } = LRUCache;
        const { get } = Object.getOwnPropertyDescriptor(prototype, method);
        warn(code, `${method} method`, `cache.${instead}()`, get);
    }
};
const deprecatedProperty = (field, instead)=>{
    const code = `LRU_CACHE_PROPERTY_${field}`;
    if (shouldWarn(code)) {
        const { prototype } = LRUCache;
        const { get } = Object.getOwnPropertyDescriptor(prototype, field);
        warn(code, `${field} property`, `cache.${instead}`, get);
    }
};
const emitWarning = (...a)=>{
    typeof process === 'object' && process && typeof process.emitWarning === 'function' ? process.emitWarning(...a) : console.error(...a);
};
const shouldWarn = (code)=>!warned.has(code);
const warn = (code, what, instead, fn)=>{
    warned.add(code);
    const msg = `The ${what} is deprecated. Please use ${instead} instead.`;
    emitWarning(msg, 'DeprecationWarning', code, fn);
};
const isPosInt = (n)=>n && n === Math.floor(n) && n > 0 && isFinite(n);
/* istanbul ignore next - This is a little bit ridiculous, tbh.
 * The maximum array length is 2^32-1 or thereabouts on most JS impls.
 * And well before that point, you're caching the entire world, I mean,
 * that's ~32GB of just integers for the next/prev links, plus whatever
 * else to hold that many keys and values.  Just filling the memory with
 * zeroes at init time is brutal when you get that big.
 * But why not be complete?
 * Maybe in the future, these limits will have expanded. */ const getUintArray = (max)=>!isPosInt(max) ? null : max <= Math.pow(2, 8) ? Uint8Array : max <= Math.pow(2, 16) ? Uint16Array : max <= Math.pow(2, 32) ? Uint32Array : max <= Number.MAX_SAFE_INTEGER ? ZeroArray : null;
class ZeroArray extends Array {
    constructor(size){
        super(size);
        this.fill(0);
    }
}
class Stack {
    constructor(max){
        if (max === 0) {
            return [];
        }
        const UintArray = getUintArray(max);
        this.heap = new UintArray(max);
        this.length = 0;
    }
    push(n) {
        this.heap[this.length++] = n;
    }
    pop() {
        return this.heap[--this.length];
    }
}
class LRUCache {
    constructor(options = {}){
        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, fetchContext, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort } = options;
        // deprecated options, don't trigger a warning for getting them if
        // the thing being passed in is another LRUCache we're copying.
        const { length, maxAge, stale } = options instanceof LRUCache ? {} : options;
        if (max !== 0 && !isPosInt(max)) {
            throw new TypeError('max option must be a nonnegative integer');
        }
        const UintArray = max ? getUintArray(max) : Array;
        if (!UintArray) {
            throw new Error('invalid max value: ' + max);
        }
        this.max = max;
        this.maxSize = maxSize;
        this.maxEntrySize = maxEntrySize || this.maxSize;
        this.sizeCalculation = sizeCalculation || length;
        if (this.sizeCalculation) {
            if (!this.maxSize && !this.maxEntrySize) {
                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');
            }
            if (typeof this.sizeCalculation !== 'function') {
                throw new TypeError('sizeCalculation set to non-function');
            }
        }
        this.fetchMethod = fetchMethod || null;
        if (this.fetchMethod && typeof this.fetchMethod !== 'function') {
            throw new TypeError('fetchMethod must be a function if specified');
        }
        this.fetchContext = fetchContext;
        if (!this.fetchMethod && fetchContext !== undefined) {
            throw new TypeError('cannot set fetchContext without fetchMethod');
        }
        this.keyMap = new Map();
        this.keyList = new Array(max).fill(null);
        this.valList = new Array(max).fill(null);
        this.next = new UintArray(max);
        this.prev = new UintArray(max);
        this.head = 0;
        this.tail = 0;
        this.free = new Stack(max);
        this.initialFill = 1;
        this.size = 0;
        if (typeof dispose === 'function') {
            this.dispose = dispose;
        }
        if (typeof disposeAfter === 'function') {
            this.disposeAfter = disposeAfter;
            this.disposed = [];
        } else {
            this.disposeAfter = null;
            this.disposed = null;
        }
        this.noDisposeOnSet = !!noDisposeOnSet;
        this.noUpdateTTL = !!noUpdateTTL;
        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;
        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;
        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;
        this.ignoreFetchAbort = !!ignoreFetchAbort;
        // NB: maxEntrySize is set to maxSize if it's set
        if (this.maxEntrySize !== 0) {
            if (this.maxSize !== 0) {
                if (!isPosInt(this.maxSize)) {
                    throw new TypeError('maxSize must be a positive integer if specified');
                }
            }
            if (!isPosInt(this.maxEntrySize)) {
                throw new TypeError('maxEntrySize must be a positive integer if specified');
            }
            this.initializeSizeTracking();
        }
        this.allowStale = !!allowStale || !!stale;
        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;
        this.updateAgeOnGet = !!updateAgeOnGet;
        this.updateAgeOnHas = !!updateAgeOnHas;
        this.ttlResolution = isPosInt(ttlResolution) || ttlResolution === 0 ? ttlResolution : 1;
        this.ttlAutopurge = !!ttlAutopurge;
        this.ttl = ttl || maxAge || 0;
        if (this.ttl) {
            if (!isPosInt(this.ttl)) {
                throw new TypeError('ttl must be a positive integer if specified');
            }
            this.initializeTTLTracking();
        }
        // do not allow completely unbounded caches
        if (this.max === 0 && this.ttl === 0 && this.maxSize === 0) {
            throw new TypeError('At least one of max, maxSize, or ttl is required');
        }
        if (!this.ttlAutopurge && !this.max && !this.maxSize) {
            const code = 'LRU_CACHE_UNBOUNDED';
            if (shouldWarn(code)) {
                warned.add(code);
                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' + 'result in unbounded memory consumption.';
                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);
            }
        }
        if (stale) {
            deprecatedOption('stale', 'allowStale');
        }
        if (maxAge) {
            deprecatedOption('maxAge', 'ttl');
        }
        if (length) {
            deprecatedOption('length', 'sizeCalculation');
        }
    }
    getRemainingTTL(key) {
        return this.has(key, {
            updateAgeOnHas: false
        }) ? Infinity : 0;
    }
    initializeTTLTracking() {
        this.ttls = new ZeroArray(this.max);
        this.starts = new ZeroArray(this.max);
        this.setItemTTL = (index, ttl, start = perf.now())=>{
            this.starts[index] = ttl !== 0 ? start : 0;
            this.ttls[index] = ttl;
            if (ttl !== 0 && this.ttlAutopurge) {
                const t = setTimeout(()=>{
                    if (this.isStale(index)) {
                        this.delete(this.keyList[index]);
                    }
                }, ttl + 1);
                /* istanbul ignore else - unref() not supported on all platforms */ if (t.unref) {
                    t.unref();
                }
            }
        };
        this.updateItemAge = (index)=>{
            this.starts[index] = this.ttls[index] !== 0 ? perf.now() : 0;
        };
        this.statusTTL = (status, index)=>{
            if (status) {
                status.ttl = this.ttls[index];
                status.start = this.starts[index];
                status.now = cachedNow || getNow();
                status.remainingTTL = status.now + status.ttl - status.start;
            }
        };
        // debounce calls to perf.now() to 1s so we're not hitting
        // that costly call repeatedly.
        let cachedNow = 0;
        const getNow = ()=>{
            const n = perf.now();
            if (this.ttlResolution > 0) {
                cachedNow = n;
                const t = setTimeout(()=>cachedNow = 0, this.ttlResolution);
                /* istanbul ignore else - not available on all platforms */ if (t.unref) {
                    t.unref();
                }
            }
            return n;
        };
        this.getRemainingTTL = (key)=>{
            const index = this.keyMap.get(key);
            if (index === undefined) {
                return 0;
            }
            return this.ttls[index] === 0 || this.starts[index] === 0 ? Infinity : this.starts[index] + this.ttls[index] - (cachedNow || getNow());
        };
        this.isStale = (index)=>{
            return this.ttls[index] !== 0 && this.starts[index] !== 0 && (cachedNow || getNow()) - this.starts[index] > this.ttls[index];
        };
    }
    updateItemAge(_index) {}
    statusTTL(_status, _index) {}
    setItemTTL(_index, _ttl, _start) {}
    isStale(_index) {
        return false;
    }
    initializeSizeTracking() {
        this.calculatedSize = 0;
        this.sizes = new ZeroArray(this.max);
        this.removeItemSize = (index)=>{
            this.calculatedSize -= this.sizes[index];
            this.sizes[index] = 0;
        };
        this.requireSize = (k, v, size, sizeCalculation)=>{
            // provisionally accept background fetches.
            // actual value size will be checked when they return.
            if (this.isBackgroundFetch(v)) {
                return 0;
            }
            if (!isPosInt(size)) {
                if (sizeCalculation) {
                    if (typeof sizeCalculation !== 'function') {
                        throw new TypeError('sizeCalculation must be a function');
                    }
                    size = sizeCalculation(v, k);
                    if (!isPosInt(size)) {
                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');
                    }
                } else {
                    throw new TypeError('invalid size value (must be positive integer). ' + 'When maxSize or maxEntrySize is used, sizeCalculation or size ' + 'must be set.');
                }
            }
            return size;
        };
        this.addItemSize = (index, size, status)=>{
            this.sizes[index] = size;
            if (this.maxSize) {
                const maxSize = this.maxSize - this.sizes[index];
                while(this.calculatedSize > maxSize){
                    this.evict(true);
                }
            }
            this.calculatedSize += this.sizes[index];
            if (status) {
                status.entrySize = size;
                status.totalCalculatedSize = this.calculatedSize;
            }
        };
    }
    removeItemSize(_index) {}
    addItemSize(_index, _size) {}
    requireSize(_k, _v, size, sizeCalculation) {
        if (size || sizeCalculation) {
            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');
        }
    }
    *indexes({ allowStale = this.allowStale } = {}) {
        if (this.size) {
            for(let i = this.tail; true;){
                if (!this.isValidIndex(i)) {
                    break;
                }
                if (allowStale || !this.isStale(i)) {
                    yield i;
                }
                if (i === this.head) {
                    break;
                } else {
                    i = this.prev[i];
                }
            }
        }
    }
    *rindexes({ allowStale = this.allowStale } = {}) {
        if (this.size) {
            for(let i = this.head; true;){
                if (!this.isValidIndex(i)) {
                    break;
                }
                if (allowStale || !this.isStale(i)) {
                    yield i;
                }
                if (i === this.tail) {
                    break;
                } else {
                    i = this.next[i];
                }
            }
        }
    }
    isValidIndex(index) {
        return index !== undefined && this.keyMap.get(this.keyList[index]) === index;
    }
    *entries() {
        for (const i of this.indexes()){
            if (this.valList[i] !== undefined && this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield [
                    this.keyList[i],
                    this.valList[i]
                ];
            }
        }
    }
    *rentries() {
        for (const i of this.rindexes()){
            if (this.valList[i] !== undefined && this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield [
                    this.keyList[i],
                    this.valList[i]
                ];
            }
        }
    }
    *keys() {
        for (const i of this.indexes()){
            if (this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield this.keyList[i];
            }
        }
    }
    *rkeys() {
        for (const i of this.rindexes()){
            if (this.keyList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield this.keyList[i];
            }
        }
    }
    *values() {
        for (const i of this.indexes()){
            if (this.valList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield this.valList[i];
            }
        }
    }
    *rvalues() {
        for (const i of this.rindexes()){
            if (this.valList[i] !== undefined && !this.isBackgroundFetch(this.valList[i])) {
                yield this.valList[i];
            }
        }
    }
    [Symbol.iterator]() {
        return this.entries();
    }
    find(fn, getOptions) {
        for (const i of this.indexes()){
            const v = this.valList[i];
            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;
            if (value === undefined) continue;
            if (fn(value, this.keyList[i], this)) {
                return this.get(this.keyList[i], getOptions);
            }
        }
    }
    forEach(fn, thisp = this) {
        for (const i of this.indexes()){
            const v = this.valList[i];
            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;
            if (value === undefined) continue;
            fn.call(thisp, value, this.keyList[i], this);
        }
    }
    rforEach(fn, thisp = this) {
        for (const i of this.rindexes()){
            const v = this.valList[i];
            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;
            if (value === undefined) continue;
            fn.call(thisp, value, this.keyList[i], this);
        }
    }
    get prune() {
        deprecatedMethod('prune', 'purgeStale');
        return this.purgeStale;
    }
    purgeStale() {
        let deleted = false;
        for (const i of this.rindexes({
            allowStale: true
        })){
            if (this.isStale(i)) {
                this.delete(this.keyList[i]);
                deleted = true;
            }
        }
        return deleted;
    }
    dump() {
        const arr = [];
        for (const i of this.indexes({
            allowStale: true
        })){
            const key = this.keyList[i];
            const v = this.valList[i];
            const value = this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;
            if (value === undefined) continue;
            const entry = {
                value
            };
            if (this.ttls) {
                entry.ttl = this.ttls[i];
                // always dump the start relative to a portable timestamp
                // it's ok for this to be a bit slow, it's a rare operation.
                const age = perf.now() - this.starts[i];
                entry.start = Math.floor(Date.now() - age);
            }
            if (this.sizes) {
                entry.size = this.sizes[i];
            }
            arr.unshift([
                key,
                entry
            ]);
        }
        return arr;
    }
    load(arr) {
        this.clear();
        for (const [key, entry] of arr){
            if (entry.start) {
                // entry.start is a portable timestamp, but we may be using
                // node's performance.now(), so calculate the offset.
                // it's ok for this to be a bit slow, it's a rare operation.
                const age = Date.now() - entry.start;
                entry.start = perf.now() - age;
            }
            this.set(key, entry.value, entry);
        }
    }
    dispose(_v, _k, _reason) {}
    set(k, v, { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, status } = {}) {
        size = this.requireSize(k, v, size, sizeCalculation);
        // if the item doesn't fit, don't do anything
        // NB: maxEntrySize set to maxSize by default
        if (this.maxEntrySize && size > this.maxEntrySize) {
            if (status) {
                status.set = 'miss';
                status.maxEntrySizeExceeded = true;
            }
            // have to delete, in case a background fetch is there already.
            // in non-async cases, this is a no-op
            this.delete(k);
            return this;
        }
        let index = this.size === 0 ? undefined : this.keyMap.get(k);
        if (index === undefined) {
            // addition
            index = this.newIndex();
            this.keyList[index] = k;
            this.valList[index] = v;
            this.keyMap.set(k, index);
            this.next[this.tail] = index;
            this.prev[index] = this.tail;
            this.tail = index;
            this.size++;
            this.addItemSize(index, size, status);
            if (status) {
                status.set = 'add';
            }
            noUpdateTTL = false;
        } else {
            // update
            this.moveToTail(index);
            const oldVal = this.valList[index];
            if (v !== oldVal) {
                if (this.isBackgroundFetch(oldVal)) {
                    oldVal.__abortController.abort(new Error('replaced'));
                } else {
                    if (!noDisposeOnSet) {
                        this.dispose(oldVal, k, 'set');
                        if (this.disposeAfter) {
                            this.disposed.push([
                                oldVal,
                                k,
                                'set'
                            ]);
                        }
                    }
                }
                this.removeItemSize(index);
                this.valList[index] = v;
                this.addItemSize(index, size, status);
                if (status) {
                    status.set = 'replace';
                    const oldValue = oldVal && this.isBackgroundFetch(oldVal) ? oldVal.__staleWhileFetching : oldVal;
                    if (oldValue !== undefined) status.oldValue = oldValue;
                }
            } else if (status) {
                status.set = 'update';
            }
        }
        if (ttl !== 0 && this.ttl === 0 && !this.ttls) {
            this.initializeTTLTracking();
        }
        if (!noUpdateTTL) {
            this.setItemTTL(index, ttl, start);
        }
        this.statusTTL(status, index);
        if (this.disposeAfter) {
            while(this.disposed.length){
                this.disposeAfter(...this.disposed.shift());
            }
        }
        return this;
    }
    newIndex() {
        if (this.size === 0) {
            return this.tail;
        }
        if (this.size === this.max && this.max !== 0) {
            return this.evict(false);
        }
        if (this.free.length !== 0) {
            return this.free.pop();
        }
        // initial fill, just keep writing down the list
        return this.initialFill++;
    }
    pop() {
        if (this.size) {
            const val = this.valList[this.head];
            this.evict(true);
            return val;
        }
    }
    evict(free) {
        const head = this.head;
        const k = this.keyList[head];
        const v = this.valList[head];
        if (this.isBackgroundFetch(v)) {
            v.__abortController.abort(new Error('evicted'));
        } else {
            this.dispose(v, k, 'evict');
            if (this.disposeAfter) {
                this.disposed.push([
                    v,
                    k,
                    'evict'
                ]);
            }
        }
        this.removeItemSize(head);
        // if we aren't about to use the index, then null these out
        if (free) {
            this.keyList[head] = null;
            this.valList[head] = null;
            this.free.push(head);
        }
        this.head = this.next[head];
        this.keyMap.delete(k);
        this.size--;
        return head;
    }
    has(k, { updateAgeOnHas = this.updateAgeOnHas, status } = {}) {
        const index = this.keyMap.get(k);
        if (index !== undefined) {
            if (!this.isStale(index)) {
                if (updateAgeOnHas) {
                    this.updateItemAge(index);
                }
                if (status) status.has = 'hit';
                this.statusTTL(status, index);
                return true;
            } else if (status) {
                status.has = 'stale';
                this.statusTTL(status, index);
            }
        } else if (status) {
            status.has = 'miss';
        }
        return false;
    }
    // like get(), but without any LRU updating or TTL expiration
    peek(k, { allowStale = this.allowStale } = {}) {
        const index = this.keyMap.get(k);
        if (index !== undefined && (allowStale || !this.isStale(index))) {
            const v = this.valList[index];
            // either stale and allowed, or forcing a refresh of non-stale value
            return this.isBackgroundFetch(v) ? v.__staleWhileFetching : v;
        }
    }
    backgroundFetch(k, index, options, context) {
        const v = index === undefined ? undefined : this.valList[index];
        if (this.isBackgroundFetch(v)) {
            return v;
        }
        const ac = new AC();
        if (options.signal) {
            options.signal.addEventListener('abort', ()=>ac.abort(options.signal.reason));
        }
        const fetchOpts = {
            signal: ac.signal,
            options,
            context
        };
        const cb = (v, updateCache = false)=>{
            const { aborted } = ac.signal;
            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;
            if (options.status) {
                if (aborted && !updateCache) {
                    options.status.fetchAborted = true;
                    options.status.fetchError = ac.signal.reason;
                    if (ignoreAbort) options.status.fetchAbortIgnored = true;
                } else {
                    options.status.fetchResolved = true;
                }
            }
            if (aborted && !ignoreAbort && !updateCache) {
                return fetchFail(ac.signal.reason);
            }
            // either we didn't abort, and are still here, or we did, and ignored
            if (this.valList[index] === p) {
                if (v === undefined) {
                    if (p.__staleWhileFetching) {
                        this.valList[index] = p.__staleWhileFetching;
                    } else {
                        this.delete(k);
                    }
                } else {
                    if (options.status) options.status.fetchUpdated = true;
                    this.set(k, v, fetchOpts.options);
                }
            }
            return v;
        };
        const eb = (er)=>{
            if (options.status) {
                options.status.fetchRejected = true;
                options.status.fetchError = er;
            }
            return fetchFail(er);
        };
        const fetchFail = (er)=>{
            const { aborted } = ac.signal;
            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;
            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;
            const noDelete = allowStale || options.noDeleteOnFetchRejection;
            if (this.valList[index] === p) {
                // if we allow stale on fetch rejections, then we need to ensure that
                // the stale value is not removed from the cache when the fetch fails.
                const del = !noDelete || p.__staleWhileFetching === undefined;
                if (del) {
                    this.delete(k);
                } else if (!allowStaleAborted) {
                    // still replace the *promise* with the stale value,
                    // since we are done with the promise at this point.
                    // leave it untouched if we're still waiting for an
                    // aborted background fetch that hasn't yet returned.
                    this.valList[index] = p.__staleWhileFetching;
                }
            }
            if (allowStale) {
                if (options.status && p.__staleWhileFetching !== undefined) {
                    options.status.returnedStale = true;
                }
                return p.__staleWhileFetching;
            } else if (p.__returned === p) {
                throw er;
            }
        };
        const pcall = (res, rej)=>{
            this.fetchMethod(k, v, fetchOpts).then((v)=>res(v), rej);
            // ignored, we go until we finish, regardless.
            // defer check until we are actually aborting,
            // so fetchMethod can override.
            ac.signal.addEventListener('abort', ()=>{
                if (!options.ignoreFetchAbort || options.allowStaleOnFetchAbort) {
                    res();
                    // when it eventually resolves, update the cache.
                    if (options.allowStaleOnFetchAbort) {
                        res = (v)=>cb(v, true);
                    }
                }
            });
        };
        if (options.status) options.status.fetchDispatched = true;
        const p = new Promise(pcall).then(cb, eb);
        p.__abortController = ac;
        p.__staleWhileFetching = v;
        p.__returned = null;
        if (index === undefined) {
            // internal, don't expose status.
            this.set(k, p, {
                ...fetchOpts.options,
                status: undefined
            });
            index = this.keyMap.get(k);
        } else {
            this.valList[index] = p;
        }
        return p;
    }
    isBackgroundFetch(p) {
        return p && typeof p === 'object' && typeof p.then === 'function' && Object.prototype.hasOwnProperty.call(p, '__staleWhileFetching') && Object.prototype.hasOwnProperty.call(p, '__returned') && (p.__returned === p || p.__returned === null);
    }
    // this takes the union of get() and set() opts, because it does both
    async fetch(k, { // get options
    allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, // set options
    ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, // fetch exclusive options
    noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, fetchContext = this.fetchContext, forceRefresh = false, status, signal } = {}) {
        if (!this.fetchMethod) {
            if (status) status.fetch = 'get';
            return this.get(k, {
                allowStale,
                updateAgeOnGet,
                noDeleteOnStaleGet,
                status
            });
        }
        const options = {
            allowStale,
            updateAgeOnGet,
            noDeleteOnStaleGet,
            ttl,
            noDisposeOnSet,
            size,
            sizeCalculation,
            noUpdateTTL,
            noDeleteOnFetchRejection,
            allowStaleOnFetchRejection,
            allowStaleOnFetchAbort,
            ignoreFetchAbort,
            status,
            signal
        };
        let index = this.keyMap.get(k);
        if (index === undefined) {
            if (status) status.fetch = 'miss';
            const p = this.backgroundFetch(k, index, options, fetchContext);
            return p.__returned = p;
        } else {
            // in cache, maybe already fetching
            const v = this.valList[index];
            if (this.isBackgroundFetch(v)) {
                const stale = allowStale && v.__staleWhileFetching !== undefined;
                if (status) {
                    status.fetch = 'inflight';
                    if (stale) status.returnedStale = true;
                }
                return stale ? v.__staleWhileFetching : v.__returned = v;
            }
            // if we force a refresh, that means do NOT serve the cached value,
            // unless we are already in the process of refreshing the cache.
            const isStale = this.isStale(index);
            if (!forceRefresh && !isStale) {
                if (status) status.fetch = 'hit';
                this.moveToTail(index);
                if (updateAgeOnGet) {
                    this.updateItemAge(index);
                }
                this.statusTTL(status, index);
                return v;
            }
            // ok, it is stale or a forced refresh, and not already fetching.
            // refresh the cache.
            const p = this.backgroundFetch(k, index, options, fetchContext);
            const hasStale = p.__staleWhileFetching !== undefined;
            const staleVal = hasStale && allowStale;
            if (status) {
                status.fetch = hasStale && isStale ? 'stale' : 'refresh';
                if (staleVal && isStale) status.returnedStale = true;
            }
            return staleVal ? p.__staleWhileFetching : p.__returned = p;
        }
    }
    get(k, { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status } = {}) {
        const index = this.keyMap.get(k);
        if (index !== undefined) {
            const value = this.valList[index];
            const fetching = this.isBackgroundFetch(value);
            this.statusTTL(status, index);
            if (this.isStale(index)) {
                if (status) status.get = 'stale';
                // delete only if not an in-flight background fetch
                if (!fetching) {
                    if (!noDeleteOnStaleGet) {
                        this.delete(k);
                    }
                    if (status) status.returnedStale = allowStale;
                    return allowStale ? value : undefined;
                } else {
                    if (status) {
                        status.returnedStale = allowStale && value.__staleWhileFetching !== undefined;
                    }
                    return allowStale ? value.__staleWhileFetching : undefined;
                }
            } else {
                if (status) status.get = 'hit';
                // if we're currently fetching it, we don't actually have it yet
                // it's not stale, which means this isn't a staleWhileRefetching.
                // If it's not stale, and fetching, AND has a __staleWhileFetching
                // value, then that means the user fetched with {forceRefresh:true},
                // so it's safe to return that value.
                if (fetching) {
                    return value.__staleWhileFetching;
                }
                this.moveToTail(index);
                if (updateAgeOnGet) {
                    this.updateItemAge(index);
                }
                return value;
            }
        } else if (status) {
            status.get = 'miss';
        }
    }
    connect(p, n) {
        this.prev[n] = p;
        this.next[p] = n;
    }
    moveToTail(index) {
        // if tail already, nothing to do
        // if head, move head to next[index]
        // else
        //   move next[prev[index]] to next[index] (head has no prev)
        //   move prev[next[index]] to prev[index]
        // prev[index] = tail
        // next[tail] = index
        // tail = index
        if (index !== this.tail) {
            if (index === this.head) {
                this.head = this.next[index];
            } else {
                this.connect(this.prev[index], this.next[index]);
            }
            this.connect(this.tail, index);
            this.tail = index;
        }
    }
    get del() {
        deprecatedMethod('del', 'delete');
        return this.delete;
    }
    delete(k) {
        let deleted = false;
        if (this.size !== 0) {
            const index = this.keyMap.get(k);
            if (index !== undefined) {
                deleted = true;
                if (this.size === 1) {
                    this.clear();
                } else {
                    this.removeItemSize(index);
                    const v = this.valList[index];
                    if (this.isBackgroundFetch(v)) {
                        v.__abortController.abort(new Error('deleted'));
                    } else {
                        this.dispose(v, k, 'delete');
                        if (this.disposeAfter) {
                            this.disposed.push([
                                v,
                                k,
                                'delete'
                            ]);
                        }
                    }
                    this.keyMap.delete(k);
                    this.keyList[index] = null;
                    this.valList[index] = null;
                    if (index === this.tail) {
                        this.tail = this.prev[index];
                    } else if (index === this.head) {
                        this.head = this.next[index];
                    } else {
                        this.next[this.prev[index]] = this.next[index];
                        this.prev[this.next[index]] = this.prev[index];
                    }
                    this.size--;
                    this.free.push(index);
                }
            }
        }
        if (this.disposed) {
            while(this.disposed.length){
                this.disposeAfter(...this.disposed.shift());
            }
        }
        return deleted;
    }
    clear() {
        for (const index of this.rindexes({
            allowStale: true
        })){
            const v = this.valList[index];
            if (this.isBackgroundFetch(v)) {
                v.__abortController.abort(new Error('deleted'));
            } else {
                const k = this.keyList[index];
                this.dispose(v, k, 'delete');
                if (this.disposeAfter) {
                    this.disposed.push([
                        v,
                        k,
                        'delete'
                    ]);
                }
            }
        }
        this.keyMap.clear();
        this.valList.fill(null);
        this.keyList.fill(null);
        if (this.ttls) {
            this.ttls.fill(0);
            this.starts.fill(0);
        }
        if (this.sizes) {
            this.sizes.fill(0);
        }
        this.head = 0;
        this.tail = 0;
        this.initialFill = 1;
        this.free.length = 0;
        this.calculatedSize = 0;
        this.size = 0;
        if (this.disposed) {
            while(this.disposed.length){
                this.disposeAfter(...this.disposed.shift());
            }
        }
    }
    get reset() {
        deprecatedMethod('reset', 'clear');
        return this.clear;
    }
    get length() {
        deprecatedProperty('length', 'size');
        return this.size;
    }
    static get AbortController() {
        return AC;
    }
    static get AbortSignal() {
        return AS;
    }
}
module.exports = LRUCache;
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/checkForResolveTypeResolver.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkForResolveTypeResolver": (()=>checkForResolveTypeResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/mapSchema.js [app-route] (ecmascript)");
;
function checkForResolveTypeResolver(schema, requireResolversForResolveType) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ABSTRACT_TYPE]: (type)=>{
            if (!type.resolveType) {
                const message = `Type "${type.name}" is missing a "__resolveType" resolver. Pass 'ignore' into ` + '"resolverValidationOptions.requireResolversForResolveType" to disable this error.';
                if (requireResolversForResolveType === 'error') {
                    throw new Error(message);
                }
                if (requireResolversForResolveType === 'warn') {
                    console.warn(message);
                }
            }
            return undefined;
        }
    });
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/extendResolversFromInterfaces.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extendResolversFromInterfaces": (()=>extendResolversFromInterfaces)
});
function extendResolversFromInterfaces(schema, resolvers) {
    const extendedResolvers = {};
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        if ('getInterfaces' in type) {
            extendedResolvers[typeName] = {};
            for (const iFace of type.getInterfaces()){
                if (resolvers[iFace.name]) {
                    for(const fieldName in resolvers[iFace.name]){
                        if (fieldName === '__isTypeOf' || !fieldName.startsWith('__')) {
                            extendedResolvers[typeName][fieldName] = resolvers[iFace.name][fieldName];
                        }
                    }
                }
            }
            const typeResolvers = resolvers[typeName];
            extendedResolvers[typeName] = {
                ...extendedResolvers[typeName],
                ...typeResolvers
            };
        } else {
            const typeResolvers = resolvers[typeName];
            if (typeResolvers != null) {
                extendedResolvers[typeName] = typeResolvers;
            }
        }
    }
    return extendedResolvers;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/addResolversToSchema.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addResolversToSchema": (()=>addResolversToSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/scalars.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/mapSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/forEachDefaultValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/transformInputValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$heal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/heal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/forEachField.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$checkForResolveTypeResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/checkForResolveTypeResolver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$extendResolversFromInterfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/extendResolversFromInterfaces.js [app-route] (ecmascript)");
;
;
;
;
function addResolversToSchema({ schema, resolvers: inputResolvers, defaultFieldResolver, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false }) {
    const { requireResolversToMatchSchema = 'error', requireResolversForResolveType } = resolverValidationOptions;
    const resolvers = inheritResolversFromInterfaces ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$extendResolversFromInterfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extendResolversFromInterfaces"])(schema, inputResolvers) : inputResolvers;
    for(const typeName in resolvers){
        const resolverValue = resolvers[typeName];
        const resolverType = typeof resolverValue;
        if (resolverType !== 'object') {
            throw new Error(`"${typeName}" defined in resolvers, but has invalid value "${resolverValue}". The resolver's value must be of type object.`);
        }
        const type = schema.getType(typeName);
        if (type == null) {
            if (requireResolversToMatchSchema === 'ignore') {
                continue;
            }
            throw new Error(`"${typeName}" defined in resolvers, but not in schema`);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type)) {
            // allow -- without recommending -- overriding of specified scalar types
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                } else {
                    type[fieldName] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            const values = type.getValues();
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__') && !values.some((value)=>value.name === fieldName) && requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                    throw new Error(`${type.name}.${fieldName} was defined in resolvers, but not present within ${type.name}`);
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__') && requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                    throw new Error(`${type.name}.${fieldName} was defined in resolvers, but ${type.name} is not an object or interface type`);
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__')) {
                    const fields = type.getFields();
                    const field = fields[fieldName];
                    if (field == null) {
                        // Field present in resolver but not in schema
                        if (requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                            throw new Error(`${typeName}.${fieldName} defined in resolvers, but not in schema`);
                        }
                    } else {
                        // Field present in both the resolver and schema
                        const fieldResolve = resolverValue[fieldName];
                        if (typeof fieldResolve !== 'function' && typeof fieldResolve !== 'object') {
                            throw new Error(`Resolver ${typeName}.${fieldName} must be object or function`);
                        }
                    }
                }
            }
        }
    }
    schema = updateResolversInPlace ? addResolversToExistingSchema(schema, resolvers, defaultFieldResolver) : createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver);
    if (requireResolversForResolveType && requireResolversForResolveType !== 'ignore') {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$checkForResolveTypeResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkForResolveTypeResolver"])(schema, requireResolversForResolveType);
    }
    return schema;
}
function addResolversToExistingSchema(schema, resolvers, defaultFieldResolver) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
    const typeMap = schema.getTypeMap();
    for(const typeName in resolvers){
        const type = schema.getType(typeName);
        const resolverValue = resolvers[typeName];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                } else if (fieldName === 'astNode' && type.astNode != null) {
                    type.astNode = {
                        ...type.astNode,
                        description: (_b = (_a = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _a === void 0 ? void 0 : _a.description) !== null && _b !== void 0 ? _b : type.astNode.description,
                        directives: ((_c = type.astNode.directives) !== null && _c !== void 0 ? _c : []).concat((_e = (_d = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _d === void 0 ? void 0 : _d.directives) !== null && _e !== void 0 ? _e : [])
                    };
                } else if (fieldName === 'extensionASTNodes' && type.extensionASTNodes != null) {
                    type.extensionASTNodes = type.extensionASTNodes.concat((_f = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.extensionASTNodes) !== null && _f !== void 0 ? _f : []);
                } else if (fieldName === 'extensions' && type.extensions != null && resolverValue.extensions != null) {
                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                } else {
                    type[fieldName] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            const config = type.toConfig();
            const enumValueConfigMap = config.values;
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    config[fieldName.substring(2)] = resolverValue[fieldName];
                } else if (fieldName === 'astNode' && config.astNode != null) {
                    config.astNode = {
                        ...config.astNode,
                        description: (_h = (_g = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _g === void 0 ? void 0 : _g.description) !== null && _h !== void 0 ? _h : config.astNode.description,
                        directives: ((_j = config.astNode.directives) !== null && _j !== void 0 ? _j : []).concat((_l = (_k = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _k === void 0 ? void 0 : _k.directives) !== null && _l !== void 0 ? _l : [])
                    };
                } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                    config.extensionASTNodes = config.extensionASTNodes.concat((_m = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.extensionASTNodes) !== null && _m !== void 0 ? _m : []);
                } else if (fieldName === 'extensions' && type.extensions != null && resolverValue.extensions != null) {
                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                } else if (enumValueConfigMap[fieldName]) {
                    enumValueConfigMap[fieldName].value = resolverValue[fieldName];
                }
            }
            typeMap[typeName] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](config);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    // this is for isTypeOf and resolveType and all the other stuff.
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                    continue;
                }
                const fields = type.getFields();
                const field = fields[fieldName];
                if (field != null) {
                    const fieldResolve = resolverValue[fieldName];
                    if (typeof fieldResolve === 'function') {
                        // for convenience. Allows shorter syntax in resolver definition file
                        field.resolve = fieldResolve.bind(resolverValue);
                    } else {
                        setFieldProperties(field, fieldResolve);
                    }
                }
            }
        }
    }
    // serialize all default values prior to healing fields with new scalar/enum types.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachDefaultValue"])(schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serializeInputValue"]);
    // schema may have new scalar/enum types that require healing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$heal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["healSchema"])(schema);
    // reparse all default values with new parsing functions.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachDefaultValue"])(schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseInputValue"]);
    if (defaultFieldResolver != null) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachField"])(schema, (field)=>{
            if (!field.resolve) {
                field.resolve = defaultFieldResolver;
            }
        });
    }
    return schema;
}
function createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver) {
    schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].SCALAR_TYPE]: (type)=>{
            var _a, _b, _c, _d, _e, _f;
            const config = type.toConfig();
            const resolverValue = resolvers[type.name];
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type) && resolverValue != null) {
                for(const fieldName in resolverValue){
                    if (fieldName.startsWith('__')) {
                        config[fieldName.substring(2)] = resolverValue[fieldName];
                    } else if (fieldName === 'astNode' && config.astNode != null) {
                        config.astNode = {
                            ...config.astNode,
                            description: (_b = (_a = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _a === void 0 ? void 0 : _a.description) !== null && _b !== void 0 ? _b : config.astNode.description,
                            directives: ((_c = config.astNode.directives) !== null && _c !== void 0 ? _c : []).concat((_e = (_d = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _d === void 0 ? void 0 : _d.directives) !== null && _e !== void 0 ? _e : [])
                        };
                    } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                        config.extensionASTNodes = config.extensionASTNodes.concat((_f = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.extensionASTNodes) !== null && _f !== void 0 ? _f : []);
                    } else if (fieldName === 'extensions' && config.extensions != null && resolverValue.extensions != null) {
                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                    } else {
                        config[fieldName] = resolverValue[fieldName];
                    }
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLScalarType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ENUM_TYPE]: (type)=>{
            var _a, _b, _c, _d, _e, _f;
            const resolverValue = resolvers[type.name];
            const config = type.toConfig();
            const enumValueConfigMap = config.values;
            if (resolverValue != null) {
                for(const fieldName in resolverValue){
                    if (fieldName.startsWith('__')) {
                        config[fieldName.substring(2)] = resolverValue[fieldName];
                    } else if (fieldName === 'astNode' && config.astNode != null) {
                        config.astNode = {
                            ...config.astNode,
                            description: (_b = (_a = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _a === void 0 ? void 0 : _a.description) !== null && _b !== void 0 ? _b : config.astNode.description,
                            directives: ((_c = config.astNode.directives) !== null && _c !== void 0 ? _c : []).concat((_e = (_d = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.astNode) === null || _d === void 0 ? void 0 : _d.directives) !== null && _e !== void 0 ? _e : [])
                        };
                    } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                        config.extensionASTNodes = config.extensionASTNodes.concat((_f = resolverValue === null || resolverValue === void 0 ? void 0 : resolverValue.extensionASTNodes) !== null && _f !== void 0 ? _f : []);
                    } else if (fieldName === 'extensions' && config.extensions != null && resolverValue.extensions != null) {
                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                    } else if (enumValueConfigMap[fieldName]) {
                        enumValueConfigMap[fieldName].value = resolverValue[fieldName];
                    }
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].UNION_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__resolveType']) {
                    config.resolveType = resolverValue['__resolveType'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLUnionType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__isTypeOf']) {
                    config.isTypeOf = resolverValue['__isTypeOf'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INTERFACE_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__resolveType']) {
                    config.resolveType = resolverValue['__resolveType'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_FIELD]: (fieldConfig, fieldName, typeName)=>{
            const resolverValue = resolvers[typeName];
            if (resolverValue != null) {
                const fieldResolve = resolverValue[fieldName];
                if (fieldResolve != null) {
                    const newFieldConfig = {
                        ...fieldConfig
                    };
                    if (typeof fieldResolve === 'function') {
                        // for convenience. Allows shorter syntax in resolver definition file
                        newFieldConfig.resolve = fieldResolve.bind(resolverValue);
                    } else {
                        setFieldProperties(newFieldConfig, fieldResolve);
                    }
                    return newFieldConfig;
                }
            }
        }
    });
    if (defaultFieldResolver != null) {
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_FIELD]: (fieldConfig)=>({
                    ...fieldConfig,
                    resolve: fieldConfig.resolve != null ? fieldConfig.resolve : defaultFieldResolver
                })
        });
    }
    return schema;
}
function setFieldProperties(field, propertiesObj) {
    for(const propertyName in propertiesObj){
        field[propertyName] = propertiesObj[propertyName];
    }
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/assertResolversPresent.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "assertResolversPresent": (()=>assertResolversPresent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/forEachField.js [app-route] (ecmascript)");
;
;
function assertResolversPresent(schema, resolverValidationOptions = {}) {
    const { requireResolversForArgs, requireResolversForNonScalar, requireResolversForAllFields } = resolverValidationOptions;
    if (requireResolversForAllFields && (requireResolversForArgs || requireResolversForNonScalar)) {
        throw new TypeError('requireResolversForAllFields takes precedence over the more specific assertions. ' + 'Please configure either requireResolversForAllFields or requireResolversForArgs / ' + 'requireResolversForNonScalar, but not a combination of them.');
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachField"])(schema, (field, typeName, fieldName)=>{
        // requires a resolver for *every* field.
        if (requireResolversForAllFields) {
            expectResolver('requireResolversForAllFields', requireResolversForAllFields, field, typeName, fieldName);
        }
        // requires a resolver on every field that has arguments
        if (requireResolversForArgs && field.args.length > 0) {
            expectResolver('requireResolversForArgs', requireResolversForArgs, field, typeName, fieldName);
        }
        // requires a resolver on every field that returns a non-scalar type
        if (requireResolversForNonScalar !== 'ignore' && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(field.type))) {
            expectResolver('requireResolversForNonScalar', requireResolversForNonScalar, field, typeName, fieldName);
        }
    });
}
function expectResolver(validator, behavior, field, typeName, fieldName) {
    if (!field.resolve) {
        const message = `Resolver missing for "${typeName}.${fieldName}".
To disable this validator, use:
  resolverValidationOptions: {
    ${validator}: 'ignore'
  }`;
        if (behavior === 'error') {
            throw new Error(message);
        }
        if (behavior === 'warn') {
            console.warn(message);
        }
        return;
    }
    if (typeof field.resolve !== 'function') {
        throw new Error(`Resolver "${typeName}.${fieldName}" must be a function`);
    }
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makeExecutableSchema": (()=>makeExecutableSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/buildASTSchema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/schema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$addResolversToSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/addResolversToSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$assertResolversPresent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/assertResolversPresent.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$extensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/extensions.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$merge$2d$resolvers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/merge-resolvers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js [app-route] (ecmascript)");
;
;
;
;
;
function makeExecutableSchema({ typeDefs, resolvers = {}, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false, schemaExtensions, ...otherOptions }) {
    // Validate and clean up arguments
    if (typeof resolverValidationOptions !== 'object') {
        throw new Error('Expected `resolverValidationOptions` to be an object');
    }
    if (!typeDefs) {
        throw new Error('Must provide typeDefs');
    }
    let schema;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSchema"])(typeDefs)) {
        schema = typeDefs;
    } else if (otherOptions === null || otherOptions === void 0 ? void 0 : otherOptions.commentDescriptions) {
        const mergedTypeDefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeTypeDefs"])(typeDefs, {
            ...otherOptions,
            commentDescriptions: true
        });
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSchema"])(mergedTypeDefs, otherOptions);
    } else {
        const mergedTypeDefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeTypeDefs"])(typeDefs, otherOptions);
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildASTSchema"])(mergedTypeDefs, otherOptions);
    }
    // We allow passing in an array of resolver maps, in which case we merge them
    schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$addResolversToSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addResolversToSchema"])({
        schema,
        resolvers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$merge$2d$resolvers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeResolvers"])(resolvers),
        resolverValidationOptions,
        inheritResolversFromInterfaces,
        updateResolversInPlace
    });
    if (Object.keys(resolverValidationOptions).length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$assertResolversPresent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertResolversPresent"])(schema, resolverValidationOptions);
    }
    if (schemaExtensions) {
        schemaExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$extensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["mergeExtensions"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["asArray"])(schemaExtensions));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$extensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["applyExtensions"])(schema, schemaExtensions);
    }
    return schema;
}
}}),
"[project]/node_modules/@graphql-tools/schema/esm/checkForResolveTypeResolver.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkForResolveTypeResolver": (()=>checkForResolveTypeResolver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/mapSchema.js [app-route] (ecmascript)");
;
function checkForResolveTypeResolver(schema, requireResolversForResolveType) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ABSTRACT_TYPE]: (type)=>{
            if (!type.resolveType) {
                const message = `Type "${type.name}" is missing a "__resolveType" resolver. Pass 'ignore' into ` + '"resolverValidationOptions.requireResolversForResolveType" to disable this error.';
                if (requireResolversForResolveType === 'error') {
                    throw new Error(message);
                }
                if (requireResolversForResolveType === 'warn') {
                    console.warn(message);
                }
            }
            return undefined;
        }
    });
}
}}),
"[project]/node_modules/@graphql-tools/schema/esm/extendResolversFromInterfaces.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extendResolversFromInterfaces": (()=>extendResolversFromInterfaces)
});
function extendResolversFromInterfaces(schema, resolvers) {
    const extendedResolvers = {};
    const typeMap = schema.getTypeMap();
    for(const typeName in typeMap){
        const type = typeMap[typeName];
        if ('getInterfaces' in type) {
            extendedResolvers[typeName] = {};
            for (const iFace of type.getInterfaces()){
                if (resolvers[iFace.name]) {
                    for(const fieldName in resolvers[iFace.name]){
                        if (fieldName === '__isTypeOf' || !fieldName.startsWith('__')) {
                            extendedResolvers[typeName][fieldName] = resolvers[iFace.name][fieldName];
                        }
                    }
                }
            }
            const typeResolvers = resolvers[typeName];
            extendedResolvers[typeName] = {
                ...extendedResolvers[typeName],
                ...typeResolvers
            };
        } else {
            const typeResolvers = resolvers[typeName];
            if (typeResolvers != null) {
                extendedResolvers[typeName] = typeResolvers;
            }
        }
    }
    return extendedResolvers;
}
}}),
"[project]/node_modules/@graphql-tools/schema/esm/addResolversToSchema.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addResolversToSchema": (()=>addResolversToSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/scalars.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/forEachDefaultValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/forEachField.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$heal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/heal.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/Interfaces.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/mapSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/transformInputValue.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$checkForResolveTypeResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/schema/esm/checkForResolveTypeResolver.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$extendResolversFromInterfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/schema/esm/extendResolversFromInterfaces.js [app-route] (ecmascript)");
;
;
;
;
function addResolversToSchema({ schema, resolvers: inputResolvers, defaultFieldResolver, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false }) {
    const { requireResolversToMatchSchema = 'error', requireResolversForResolveType } = resolverValidationOptions;
    const resolvers = inheritResolversFromInterfaces ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$extendResolversFromInterfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extendResolversFromInterfaces"])(schema, inputResolvers) : inputResolvers;
    for(const typeName in resolvers){
        const resolverValue = resolvers[typeName];
        const resolverType = typeof resolverValue;
        if (resolverType !== 'object') {
            throw new Error(`"${typeName}" defined in resolvers, but has invalid value "${resolverValue}". The resolver's value must be of type object.`);
        }
        const type = schema.getType(typeName);
        if (type == null) {
            const msg = `"${typeName}" defined in resolvers, but not in schema`;
            if (requireResolversToMatchSchema && requireResolversToMatchSchema !== 'error') {
                if (requireResolversToMatchSchema === 'warn') {
                    console.warn(msg);
                }
                continue;
            }
            throw new Error(msg);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type)) {
            // allow -- without recommending -- overriding of specified scalar types
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                } else {
                    type[fieldName] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            const values = type.getValues();
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__') && !values.some((value)=>value.name === fieldName) && requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                    const msg = `${type.name}.${fieldName} was defined in resolvers, but not present within ${type.name}`;
                    if (requireResolversToMatchSchema === 'error') {
                        throw new Error(msg);
                    } else {
                        console.warn(msg);
                    }
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__') && requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                    const msg = `${type.name}.${fieldName} was defined in resolvers, but ${type.name} is not an object or interface type`;
                    if (requireResolversToMatchSchema === 'error') {
                        throw new Error(msg);
                    } else {
                        console.warn(msg);
                    }
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            for(const fieldName in resolverValue){
                if (!fieldName.startsWith('__')) {
                    const fields = type.getFields();
                    const field = fields[fieldName];
                    if (field == null) {
                        // Field present in resolver but not in schema
                        if (requireResolversToMatchSchema && requireResolversToMatchSchema !== 'ignore') {
                            const msg = `${typeName}.${fieldName} defined in resolvers, but not in schema`;
                            if (requireResolversToMatchSchema === 'error') {
                                throw new Error(msg);
                            } else {
                                console.error(msg);
                            }
                        }
                    } else {
                        // Field present in both the resolver and schema
                        const fieldResolve = resolverValue[fieldName];
                        if (typeof fieldResolve !== 'function' && typeof fieldResolve !== 'object') {
                            throw new Error(`Resolver ${typeName}.${fieldName} must be object or function`);
                        }
                    }
                }
            }
        }
    }
    schema = updateResolversInPlace ? addResolversToExistingSchema(schema, resolvers, defaultFieldResolver) : createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver);
    if (requireResolversForResolveType && requireResolversForResolveType !== 'ignore') {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$checkForResolveTypeResolver$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkForResolveTypeResolver"])(schema, requireResolversForResolveType);
    }
    return schema;
}
function addResolversToExistingSchema(schema, resolvers, defaultFieldResolver) {
    const typeMap = schema.getTypeMap();
    for(const typeName in resolvers){
        const type = schema.getType(typeName);
        const resolverValue = resolvers[typeName];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                } else if (fieldName === 'astNode' && type.astNode != null) {
                    type.astNode = {
                        ...type.astNode,
                        description: resolverValue?.astNode?.description ?? type.astNode.description,
                        directives: (type.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? [])
                    };
                } else if (fieldName === 'extensionASTNodes' && type.extensionASTNodes != null) {
                    type.extensionASTNodes = type.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);
                } else if (fieldName === 'extensions' && type.extensions != null && resolverValue.extensions != null) {
                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                } else {
                    type[fieldName] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isEnumType"])(type)) {
            const config = type.toConfig();
            const enumValueConfigMap = config.values;
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    config[fieldName.substring(2)] = resolverValue[fieldName];
                } else if (fieldName === 'astNode' && config.astNode != null) {
                    config.astNode = {
                        ...config.astNode,
                        description: resolverValue?.astNode?.description ?? config.astNode.description,
                        directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? [])
                    };
                } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                    config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);
                } else if (fieldName === 'extensions' && type.extensions != null && resolverValue.extensions != null) {
                    type.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                } else if (enumValueConfigMap[fieldName]) {
                    enumValueConfigMap[fieldName].value = resolverValue[fieldName];
                }
            }
            typeMap[typeName] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](config);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isUnionType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                }
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isObjectType"])(type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isInterfaceType"])(type)) {
            for(const fieldName in resolverValue){
                if (fieldName.startsWith('__')) {
                    // this is for isTypeOf and resolveType and all the other stuff.
                    type[fieldName.substring(2)] = resolverValue[fieldName];
                    continue;
                }
                const fields = type.getFields();
                const field = fields[fieldName];
                if (field != null) {
                    const fieldResolve = resolverValue[fieldName];
                    if (typeof fieldResolve === 'function') {
                        // for convenience. Allows shorter syntax in resolver definition file
                        field.resolve = fieldResolve.bind(resolverValue);
                    } else {
                        setFieldProperties(field, fieldResolve);
                    }
                }
            }
        }
    }
    // serialize all default values prior to healing fields with new scalar/enum types.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachDefaultValue"])(schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["serializeInputValue"]);
    // schema may have new scalar/enum types that require healing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$heal$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["healSchema"])(schema);
    // reparse all default values with new parsing functions.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachDefaultValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachDefaultValue"])(schema, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$transformInputValue$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseInputValue"]);
    if (defaultFieldResolver != null) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachField"])(schema, (field)=>{
            if (!field.resolve) {
                field.resolve = defaultFieldResolver;
            }
        });
    }
    return schema;
}
function createNewSchemaWithResolvers(schema, resolvers, defaultFieldResolver) {
    schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].SCALAR_TYPE]: (type)=>{
            const config = type.toConfig();
            const resolverValue = resolvers[type.name];
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$scalars$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSpecifiedScalarType"])(type) && resolverValue != null) {
                for(const fieldName in resolverValue){
                    if (fieldName.startsWith('__')) {
                        config[fieldName.substring(2)] = resolverValue[fieldName];
                    } else if (fieldName === 'astNode' && config.astNode != null) {
                        config.astNode = {
                            ...config.astNode,
                            description: resolverValue?.astNode?.description ?? config.astNode.description,
                            directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? [])
                        };
                    } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                        config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);
                    } else if (fieldName === 'extensions' && config.extensions != null && resolverValue.extensions != null) {
                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                    } else {
                        config[fieldName] = resolverValue[fieldName];
                    }
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLScalarType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].ENUM_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            const config = type.toConfig();
            const enumValueConfigMap = config.values;
            if (resolverValue != null) {
                for(const fieldName in resolverValue){
                    if (fieldName.startsWith('__')) {
                        config[fieldName.substring(2)] = resolverValue[fieldName];
                    } else if (fieldName === 'astNode' && config.astNode != null) {
                        config.astNode = {
                            ...config.astNode,
                            description: resolverValue?.astNode?.description ?? config.astNode.description,
                            directives: (config.astNode.directives ?? []).concat(resolverValue?.astNode?.directives ?? [])
                        };
                    } else if (fieldName === 'extensionASTNodes' && config.extensionASTNodes != null) {
                        config.extensionASTNodes = config.extensionASTNodes.concat(resolverValue?.extensionASTNodes ?? []);
                    } else if (fieldName === 'extensions' && config.extensions != null && resolverValue.extensions != null) {
                        config.extensions = Object.assign(Object.create(null), type.extensions, resolverValue.extensions);
                    } else if (enumValueConfigMap[fieldName]) {
                        enumValueConfigMap[fieldName].value = resolverValue[fieldName];
                    }
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLEnumType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].UNION_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__resolveType']) {
                    config.resolveType = resolverValue['__resolveType'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLUnionType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__isTypeOf']) {
                    config.isTypeOf = resolverValue['__isTypeOf'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].INTERFACE_TYPE]: (type)=>{
            const resolverValue = resolvers[type.name];
            if (resolverValue != null) {
                const config = type.toConfig();
                if (resolverValue['__resolveType']) {
                    config.resolveType = resolverValue['__resolveType'];
                }
                return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLInterfaceType"](config);
            }
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].COMPOSITE_FIELD]: (fieldConfig, fieldName, typeName)=>{
            const resolverValue = resolvers[typeName];
            if (resolverValue != null) {
                const fieldResolve = resolverValue[fieldName];
                if (fieldResolve != null) {
                    const newFieldConfig = {
                        ...fieldConfig
                    };
                    if (typeof fieldResolve === 'function') {
                        // for convenience. Allows shorter syntax in resolver definition file
                        newFieldConfig.resolve = fieldResolve.bind(resolverValue);
                    } else {
                        setFieldProperties(newFieldConfig, fieldResolve);
                    }
                    return newFieldConfig;
                }
            }
        }
    });
    if (defaultFieldResolver != null) {
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mapSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mapSchema"])(schema, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$Interfaces$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MapperKind"].OBJECT_FIELD]: (fieldConfig)=>({
                    ...fieldConfig,
                    resolve: fieldConfig.resolve != null ? fieldConfig.resolve : defaultFieldResolver
                })
        });
    }
    return schema;
}
function setFieldProperties(field, propertiesObj) {
    for(const propertyName in propertiesObj){
        field[propertyName] = propertiesObj[propertyName];
    }
}
}}),
"[project]/node_modules/@graphql-tools/schema/esm/assertResolversPresent.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "assertResolversPresent": (()=>assertResolversPresent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/forEachField.js [app-route] (ecmascript)");
;
;
function assertResolversPresent(schema, resolverValidationOptions = {}) {
    const { requireResolversForArgs, requireResolversForNonScalar, requireResolversForAllFields } = resolverValidationOptions;
    if (requireResolversForAllFields && (requireResolversForArgs || requireResolversForNonScalar)) {
        throw new TypeError('requireResolversForAllFields takes precedence over the more specific assertions. ' + 'Please configure either requireResolversForAllFields or requireResolversForArgs / ' + 'requireResolversForNonScalar, but not a combination of them.');
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$forEachField$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["forEachField"])(schema, (field, typeName, fieldName)=>{
        // requires a resolver for *every* field.
        if (requireResolversForAllFields) {
            expectResolver('requireResolversForAllFields', requireResolversForAllFields, field, typeName, fieldName);
        }
        // requires a resolver on every field that has arguments
        if (requireResolversForArgs && field.args.length > 0) {
            expectResolver('requireResolversForArgs', requireResolversForArgs, field, typeName, fieldName);
        }
        // requires a resolver on every field that returns a non-scalar type
        if (requireResolversForNonScalar !== 'ignore' && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isScalarType"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(field.type))) {
            expectResolver('requireResolversForNonScalar', requireResolversForNonScalar, field, typeName, fieldName);
        }
    });
}
function expectResolver(validator, behavior, field, typeName, fieldName) {
    if (!field.resolve) {
        const message = `Resolver missing for "${typeName}.${fieldName}".
To disable this validator, use:
  resolverValidationOptions: {
    ${validator}: 'ignore'
  }`;
        if (behavior === 'error') {
            throw new Error(message);
        }
        if (behavior === 'warn') {
            console.warn(message);
        }
        return;
    }
    if (typeof field.resolve !== 'function') {
        throw new Error(`Resolver "${typeName}.${fieldName}" must be a function`);
    }
}
}}),
"[project]/node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makeExecutableSchema": (()=>makeExecutableSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/buildASTSchema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/schema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$extensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/extensions.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$merge$2d$resolvers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/merge-resolvers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$addResolversToSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/schema/esm/addResolversToSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$assertResolversPresent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/schema/esm/assertResolversPresent.js [app-route] (ecmascript)");
;
;
;
;
;
function makeExecutableSchema({ typeDefs, resolvers = {}, resolverValidationOptions = {}, inheritResolversFromInterfaces = false, updateResolversInPlace = false, schemaExtensions, defaultFieldResolver, ...otherOptions }) {
    // Validate and clean up arguments
    if (typeof resolverValidationOptions !== 'object') {
        throw new Error('Expected `resolverValidationOptions` to be an object');
    }
    if (!typeDefs) {
        throw new Error('Must provide typeDefs');
    }
    let schema;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSchema"])(typeDefs)) {
        schema = typeDefs;
    } else if (otherOptions?.commentDescriptions) {
        const mergedTypeDefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeTypeDefs"])(typeDefs, {
            ...otherOptions,
            commentDescriptions: true
        });
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildSchema"])(mergedTypeDefs, otherOptions);
    } else {
        const mergedTypeDefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$typedefs$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeTypeDefs"])(typeDefs, otherOptions);
        schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$buildASTSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["buildASTSchema"])(mergedTypeDefs, otherOptions);
    }
    // We allow passing in an array of resolver maps, in which case we merge them
    schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$addResolversToSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addResolversToSchema"])({
        schema,
        resolvers: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$merge$2d$resolvers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeResolvers"])(resolvers),
        resolverValidationOptions,
        inheritResolversFromInterfaces,
        updateResolversInPlace,
        defaultFieldResolver
    });
    if (Object.keys(resolverValidationOptions).length > 0) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$assertResolversPresent$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertResolversPresent"])(schema, resolverValidationOptions);
    }
    if (schemaExtensions) {
        for (const schemaExtension of (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["asArray"])(schemaExtensions)){
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$extensions$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["applyExtensions"])(schema, schemaExtension);
        }
    }
    return schema;
}
}}),
"[project]/node_modules/loglevel/lib/loglevel.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*
* loglevel - https://github.com/pimterry/loglevel
*
* Copyright (c) 2013 Tim Perry
* Licensed under the MIT license.
*/ (function(root, definition) {
    "use strict";
    if (typeof define === 'function' && define.amd) {
        ((r)=>r !== undefined && __turbopack_context__.v(r))(definition(__turbopack_context__.r, exports, module));
    } else if (("TURBOPACK compile-time value", "object") === 'object' && module.exports) {
        module.exports = definition();
    } else {
        root.log = definition();
    }
})(this, function() {
    "use strict";
    // Slightly dubious tricks to cut down minimized file size
    var noop = function() {};
    var undefinedType = "undefined";
    var isIE = "undefined" !== undefinedType && typeof window.navigator !== undefinedType && /Trident\/|MSIE /.test(window.navigator.userAgent);
    var logMethods = [
        "trace",
        "debug",
        "info",
        "warn",
        "error"
    ];
    var _loggersByName = {};
    var defaultLogger = null;
    // Cross-browser bind equivalent that works at least back to IE6
    function bindMethod(obj, methodName) {
        var method = obj[methodName];
        if (typeof method.bind === 'function') {
            return method.bind(obj);
        } else {
            try {
                return Function.prototype.bind.call(method, obj);
            } catch (e) {
                // Missing bind shim or IE8 + Modernizr, fallback to wrapping
                return function() {
                    return Function.prototype.apply.apply(method, [
                        obj,
                        arguments
                    ]);
                };
            }
        }
    }
    // Trace() doesn't print the message in IE, so for that case we need to wrap it
    function traceForIE() {
        if (console.log) {
            if (console.log.apply) {
                console.log.apply(console, arguments);
            } else {
                // In old IE, native console methods themselves don't have apply().
                Function.prototype.apply.apply(console.log, [
                    console,
                    arguments
                ]);
            }
        }
        if (console.trace) console.trace();
    }
    // Build the best logging method possible for this env
    // Wherever possible we want to bind, not wrap, to preserve stack traces
    function realMethod(methodName) {
        if (methodName === 'debug') {
            methodName = 'log';
        }
        if (typeof console === undefinedType) {
            return false; // No method possible, for now - fixed later by enableLoggingWhenConsoleArrives
        } else if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else if (console[methodName] !== undefined) {
            return bindMethod(console, methodName);
        } else if (console.log !== undefined) {
            return bindMethod(console, 'log');
        } else {
            return noop;
        }
    }
    // These private functions always need `this` to be set properly
    function replaceLoggingMethods() {
        /*jshint validthis:true */ var level = this.getLevel();
        // Replace the actual methods.
        for(var i = 0; i < logMethods.length; i++){
            var methodName = logMethods[i];
            this[methodName] = i < level ? noop : this.methodFactory(methodName, level, this.name);
        }
        // Define log.log as an alias for log.debug
        this.log = this.debug;
        // Return any important warnings.
        if (typeof console === undefinedType && level < this.levels.SILENT) {
            return "No console available for logging";
        }
    }
    // In old IE versions, the console isn't present until you first open it.
    // We build realMethod() replacements here that regenerate logging methods
    function enableLoggingWhenConsoleArrives(methodName) {
        return function() {
            if (typeof console !== undefinedType) {
                replaceLoggingMethods.call(this);
                this[methodName].apply(this, arguments);
            }
        };
    }
    // By default, we use closely bound real methods wherever possible, and
    // otherwise we wait for a console to appear, and then try again.
    function defaultMethodFactory(methodName, _level, _loggerName) {
        /*jshint validthis:true */ return realMethod(methodName) || enableLoggingWhenConsoleArrives.apply(this, arguments);
    }
    function Logger(name, factory) {
        // Private instance variables.
        var self = this;
        /**
       * The level inherited from a parent logger (or a global default). We
       * cache this here rather than delegating to the parent so that it stays
       * in sync with the actual logging methods that we have installed (the
       * parent could change levels but we might not have rebuilt the loggers
       * in this child yet).
       * @type {number}
       */ var inheritedLevel;
        /**
       * The default level for this logger, if any. If set, this overrides
       * `inheritedLevel`.
       * @type {number|null}
       */ var defaultLevel;
        /**
       * A user-specific level for this logger. If set, this overrides
       * `defaultLevel`.
       * @type {number|null}
       */ var userLevel;
        var storageKey = "loglevel";
        if (typeof name === "string") {
            storageKey += ":" + name;
        } else if (typeof name === "symbol") {
            storageKey = undefined;
        }
        function persistLevelIfPossible(levelNum) {
            var levelName = (logMethods[levelNum] || 'silent').toUpperCase();
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
        }
        function getPersistedLevel() {
            var storedLevel;
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
            var cookie;
            var cookieName;
            var location;
        }
        function clearPersistedLevel() {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
        }
        function normalizeLevel(input) {
            var level = input;
            if (typeof level === "string" && self.levels[level.toUpperCase()] !== undefined) {
                level = self.levels[level.toUpperCase()];
            }
            if (typeof level === "number" && level >= 0 && level <= self.levels.SILENT) {
                return level;
            } else {
                throw new TypeError("log.setLevel() called with invalid level: " + input);
            }
        }
        /*
       *
       * Public logger API - see https://github.com/pimterry/loglevel for details
       *
       */ self.name = name;
        self.levels = {
            "TRACE": 0,
            "DEBUG": 1,
            "INFO": 2,
            "WARN": 3,
            "ERROR": 4,
            "SILENT": 5
        };
        self.methodFactory = factory || defaultMethodFactory;
        self.getLevel = function() {
            if (userLevel != null) {
                return userLevel;
            } else if (defaultLevel != null) {
                return defaultLevel;
            } else {
                return inheritedLevel;
            }
        };
        self.setLevel = function(level, persist) {
            userLevel = normalizeLevel(level);
            if (persist !== false) {
                persistLevelIfPossible(userLevel);
            }
            // NOTE: in v2, this should call rebuild(), which updates children.
            return replaceLoggingMethods.call(self);
        };
        self.setDefaultLevel = function(level) {
            defaultLevel = normalizeLevel(level);
            if (!getPersistedLevel()) {
                self.setLevel(level, false);
            }
        };
        self.resetLevel = function() {
            userLevel = null;
            clearPersistedLevel();
            replaceLoggingMethods.call(self);
        };
        self.enableAll = function(persist) {
            self.setLevel(self.levels.TRACE, persist);
        };
        self.disableAll = function(persist) {
            self.setLevel(self.levels.SILENT, persist);
        };
        self.rebuild = function() {
            if (defaultLogger !== self) {
                inheritedLevel = normalizeLevel(defaultLogger.getLevel());
            }
            replaceLoggingMethods.call(self);
            if (defaultLogger === self) {
                for(var childName in _loggersByName){
                    _loggersByName[childName].rebuild();
                }
            }
        };
        // Initialize all the internal levels.
        inheritedLevel = normalizeLevel(defaultLogger ? defaultLogger.getLevel() : "WARN");
        var initialLevel = getPersistedLevel();
        if (initialLevel != null) {
            userLevel = normalizeLevel(initialLevel);
        }
        replaceLoggingMethods.call(self);
    }
    /*
     *
     * Top-level API
     *
     */ defaultLogger = new Logger();
    defaultLogger.getLogger = function getLogger(name) {
        if (typeof name !== "symbol" && typeof name !== "string" || name === "") {
            throw new TypeError("You must supply a name when creating a logger.");
        }
        var logger = _loggersByName[name];
        if (!logger) {
            logger = _loggersByName[name] = new Logger(name, defaultLogger.methodFactory);
        }
        return logger;
    };
    // Grab the current global log variable in case of overwrite
    var _log = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : undefined;
    defaultLogger.noConflict = function() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return defaultLogger;
    };
    defaultLogger.getLoggers = function getLoggers() {
        return _loggersByName;
    };
    // ES6 default export, for compatibility
    defaultLogger['default'] = defaultLogger;
    return defaultLogger;
});
}}),
"[project]/node_modules/negotiator/lib/charset.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * negotiator
 * Copyright(c) 2012 Isaac Z. Schlueter
 * Copyright(c) 2014 Federico Romero
 * Copyright(c) 2014-2015 Douglas Christopher Wilson
 * MIT Licensed
 */ 'use strict';
/**
 * Module exports.
 * @public
 */ module.exports = preferredCharsets;
module.exports.preferredCharsets = preferredCharsets;
/**
 * Module variables.
 * @private
 */ var simpleCharsetRegExp = /^\s*([^\s;]+)\s*(?:;(.*))?$/;
/**
 * Parse the Accept-Charset header.
 * @private
 */ function parseAcceptCharset(accept) {
    var accepts = accept.split(',');
    for(var i = 0, j = 0; i < accepts.length; i++){
        var charset = parseCharset(accepts[i].trim(), i);
        if (charset) {
            accepts[j++] = charset;
        }
    }
    // trim accepts
    accepts.length = j;
    return accepts;
}
/**
 * Parse a charset from the Accept-Charset header.
 * @private
 */ function parseCharset(str, i) {
    var match = simpleCharsetRegExp.exec(str);
    if (!match) return null;
    var charset = match[1];
    var q = 1;
    if (match[2]) {
        var params = match[2].split(';');
        for(var j = 0; j < params.length; j++){
            var p = params[j].trim().split('=');
            if (p[0] === 'q') {
                q = parseFloat(p[1]);
                break;
            }
        }
    }
    return {
        charset: charset,
        q: q,
        i: i
    };
}
/**
 * Get the priority of a charset.
 * @private
 */ function getCharsetPriority(charset, accepted, index) {
    var priority = {
        o: -1,
        q: 0,
        s: 0
    };
    for(var i = 0; i < accepted.length; i++){
        var spec = specify(charset, accepted[i], index);
        if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {
            priority = spec;
        }
    }
    return priority;
}
/**
 * Get the specificity of the charset.
 * @private
 */ function specify(charset, spec, index) {
    var s = 0;
    if (spec.charset.toLowerCase() === charset.toLowerCase()) {
        s |= 1;
    } else if (spec.charset !== '*') {
        return null;
    }
    return {
        i: index,
        o: spec.i,
        q: spec.q,
        s: s
    };
}
/**
 * Get the preferred charsets from an Accept-Charset header.
 * @public
 */ function preferredCharsets(accept, provided) {
    // RFC 2616 sec 14.2: no header = *
    var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');
    if (!provided) {
        // sorted list of all charsets
        return accepts.filter(isQuality).sort(compareSpecs).map(getFullCharset);
    }
    var priorities = provided.map(function getPriority(type, index) {
        return getCharsetPriority(type, accepts, index);
    });
    // sorted list of accepted charsets
    return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {
        return provided[priorities.indexOf(priority)];
    });
}
/**
 * Compare two specs.
 * @private
 */ function compareSpecs(a, b) {
    return b.q - a.q || b.s - a.s || a.o - b.o || a.i - b.i || 0;
}
/**
 * Get full charset string.
 * @private
 */ function getFullCharset(spec) {
    return spec.charset;
}
/**
 * Check if a spec has any quality.
 * @private
 */ function isQuality(spec) {
    return spec.q > 0;
}
}}),
"[project]/node_modules/negotiator/lib/encoding.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * negotiator
 * Copyright(c) 2012 Isaac Z. Schlueter
 * Copyright(c) 2014 Federico Romero
 * Copyright(c) 2014-2015 Douglas Christopher Wilson
 * MIT Licensed
 */ 'use strict';
/**
 * Module exports.
 * @public
 */ module.exports = preferredEncodings;
module.exports.preferredEncodings = preferredEncodings;
/**
 * Module variables.
 * @private
 */ var simpleEncodingRegExp = /^\s*([^\s;]+)\s*(?:;(.*))?$/;
/**
 * Parse the Accept-Encoding header.
 * @private
 */ function parseAcceptEncoding(accept) {
    var accepts = accept.split(',');
    var hasIdentity = false;
    var minQuality = 1;
    for(var i = 0, j = 0; i < accepts.length; i++){
        var encoding = parseEncoding(accepts[i].trim(), i);
        if (encoding) {
            accepts[j++] = encoding;
            hasIdentity = hasIdentity || specify('identity', encoding);
            minQuality = Math.min(minQuality, encoding.q || 1);
        }
    }
    if (!hasIdentity) {
        /*
     * If identity doesn't explicitly appear in the accept-encoding header,
     * it's added to the list of acceptable encoding with the lowest q
     */ accepts[j++] = {
            encoding: 'identity',
            q: minQuality,
            i: i
        };
    }
    // trim accepts
    accepts.length = j;
    return accepts;
}
/**
 * Parse an encoding from the Accept-Encoding header.
 * @private
 */ function parseEncoding(str, i) {
    var match = simpleEncodingRegExp.exec(str);
    if (!match) return null;
    var encoding = match[1];
    var q = 1;
    if (match[2]) {
        var params = match[2].split(';');
        for(var j = 0; j < params.length; j++){
            var p = params[j].trim().split('=');
            if (p[0] === 'q') {
                q = parseFloat(p[1]);
                break;
            }
        }
    }
    return {
        encoding: encoding,
        q: q,
        i: i
    };
}
/**
 * Get the priority of an encoding.
 * @private
 */ function getEncodingPriority(encoding, accepted, index) {
    var priority = {
        encoding: encoding,
        o: -1,
        q: 0,
        s: 0
    };
    for(var i = 0; i < accepted.length; i++){
        var spec = specify(encoding, accepted[i], index);
        if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {
            priority = spec;
        }
    }
    return priority;
}
/**
 * Get the specificity of the encoding.
 * @private
 */ function specify(encoding, spec, index) {
    var s = 0;
    if (spec.encoding.toLowerCase() === encoding.toLowerCase()) {
        s |= 1;
    } else if (spec.encoding !== '*') {
        return null;
    }
    return {
        encoding: encoding,
        i: index,
        o: spec.i,
        q: spec.q,
        s: s
    };
}
;
/**
 * Get the preferred encodings from an Accept-Encoding header.
 * @public
 */ function preferredEncodings(accept, provided, preferred) {
    var accepts = parseAcceptEncoding(accept || '');
    var comparator = preferred ? function comparator(a, b) {
        if (a.q !== b.q) {
            return b.q - a.q // higher quality first
            ;
        }
        var aPreferred = preferred.indexOf(a.encoding);
        var bPreferred = preferred.indexOf(b.encoding);
        if (aPreferred === -1 && bPreferred === -1) {
            // consider the original specifity/order
            return b.s - a.s || a.o - b.o || a.i - b.i;
        }
        if (aPreferred !== -1 && bPreferred !== -1) {
            return aPreferred - bPreferred // consider the preferred order
            ;
        }
        return aPreferred === -1 ? 1 : -1 // preferred first
        ;
    } : compareSpecs;
    if (!provided) {
        // sorted list of all encodings
        return accepts.filter(isQuality).sort(comparator).map(getFullEncoding);
    }
    var priorities = provided.map(function getPriority(type, index) {
        return getEncodingPriority(type, accepts, index);
    });
    // sorted list of accepted encodings
    return priorities.filter(isQuality).sort(comparator).map(function getEncoding(priority) {
        return provided[priorities.indexOf(priority)];
    });
}
/**
 * Compare two specs.
 * @private
 */ function compareSpecs(a, b) {
    return b.q - a.q || b.s - a.s || a.o - b.o || a.i - b.i;
}
/**
 * Get full encoding string.
 * @private
 */ function getFullEncoding(spec) {
    return spec.encoding;
}
/**
 * Check if a spec has any quality.
 * @private
 */ function isQuality(spec) {
    return spec.q > 0;
}
}}),
"[project]/node_modules/negotiator/lib/language.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * negotiator
 * Copyright(c) 2012 Isaac Z. Schlueter
 * Copyright(c) 2014 Federico Romero
 * Copyright(c) 2014-2015 Douglas Christopher Wilson
 * MIT Licensed
 */ 'use strict';
/**
 * Module exports.
 * @public
 */ module.exports = preferredLanguages;
module.exports.preferredLanguages = preferredLanguages;
/**
 * Module variables.
 * @private
 */ var simpleLanguageRegExp = /^\s*([^\s\-;]+)(?:-([^\s;]+))?\s*(?:;(.*))?$/;
/**
 * Parse the Accept-Language header.
 * @private
 */ function parseAcceptLanguage(accept) {
    var accepts = accept.split(',');
    for(var i = 0, j = 0; i < accepts.length; i++){
        var language = parseLanguage(accepts[i].trim(), i);
        if (language) {
            accepts[j++] = language;
        }
    }
    // trim accepts
    accepts.length = j;
    return accepts;
}
/**
 * Parse a language from the Accept-Language header.
 * @private
 */ function parseLanguage(str, i) {
    var match = simpleLanguageRegExp.exec(str);
    if (!match) return null;
    var prefix = match[1];
    var suffix = match[2];
    var full = prefix;
    if (suffix) full += "-" + suffix;
    var q = 1;
    if (match[3]) {
        var params = match[3].split(';');
        for(var j = 0; j < params.length; j++){
            var p = params[j].split('=');
            if (p[0] === 'q') q = parseFloat(p[1]);
        }
    }
    return {
        prefix: prefix,
        suffix: suffix,
        q: q,
        i: i,
        full: full
    };
}
/**
 * Get the priority of a language.
 * @private
 */ function getLanguagePriority(language, accepted, index) {
    var priority = {
        o: -1,
        q: 0,
        s: 0
    };
    for(var i = 0; i < accepted.length; i++){
        var spec = specify(language, accepted[i], index);
        if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {
            priority = spec;
        }
    }
    return priority;
}
/**
 * Get the specificity of the language.
 * @private
 */ function specify(language, spec, index) {
    var p = parseLanguage(language);
    if (!p) return null;
    var s = 0;
    if (spec.full.toLowerCase() === p.full.toLowerCase()) {
        s |= 4;
    } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {
        s |= 2;
    } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {
        s |= 1;
    } else if (spec.full !== '*') {
        return null;
    }
    return {
        i: index,
        o: spec.i,
        q: spec.q,
        s: s
    };
}
;
/**
 * Get the preferred languages from an Accept-Language header.
 * @public
 */ function preferredLanguages(accept, provided) {
    // RFC 2616 sec 14.4: no header = *
    var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');
    if (!provided) {
        // sorted list of all languages
        return accepts.filter(isQuality).sort(compareSpecs).map(getFullLanguage);
    }
    var priorities = provided.map(function getPriority(type, index) {
        return getLanguagePriority(type, accepts, index);
    });
    // sorted list of accepted languages
    return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {
        return provided[priorities.indexOf(priority)];
    });
}
/**
 * Compare two specs.
 * @private
 */ function compareSpecs(a, b) {
    return b.q - a.q || b.s - a.s || a.o - b.o || a.i - b.i || 0;
}
/**
 * Get full language string.
 * @private
 */ function getFullLanguage(spec) {
    return spec.full;
}
/**
 * Check if a spec has any quality.
 * @private
 */ function isQuality(spec) {
    return spec.q > 0;
}
}}),
"[project]/node_modules/negotiator/lib/mediaType.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * negotiator
 * Copyright(c) 2012 Isaac Z. Schlueter
 * Copyright(c) 2014 Federico Romero
 * Copyright(c) 2014-2015 Douglas Christopher Wilson
 * MIT Licensed
 */ 'use strict';
/**
 * Module exports.
 * @public
 */ module.exports = preferredMediaTypes;
module.exports.preferredMediaTypes = preferredMediaTypes;
/**
 * Module variables.
 * @private
 */ var simpleMediaTypeRegExp = /^\s*([^\s\/;]+)\/([^;\s]+)\s*(?:;(.*))?$/;
/**
 * Parse the Accept header.
 * @private
 */ function parseAccept(accept) {
    var accepts = splitMediaTypes(accept);
    for(var i = 0, j = 0; i < accepts.length; i++){
        var mediaType = parseMediaType(accepts[i].trim(), i);
        if (mediaType) {
            accepts[j++] = mediaType;
        }
    }
    // trim accepts
    accepts.length = j;
    return accepts;
}
/**
 * Parse a media type from the Accept header.
 * @private
 */ function parseMediaType(str, i) {
    var match = simpleMediaTypeRegExp.exec(str);
    if (!match) return null;
    var params = Object.create(null);
    var q = 1;
    var subtype = match[2];
    var type = match[1];
    if (match[3]) {
        var kvps = splitParameters(match[3]).map(splitKeyValuePair);
        for(var j = 0; j < kvps.length; j++){
            var pair = kvps[j];
            var key = pair[0].toLowerCase();
            var val = pair[1];
            // get the value, unwrapping quotes
            var value = val && val[0] === '"' && val[val.length - 1] === '"' ? val.slice(1, -1) : val;
            if (key === 'q') {
                q = parseFloat(value);
                break;
            }
            // store parameter
            params[key] = value;
        }
    }
    return {
        type: type,
        subtype: subtype,
        params: params,
        q: q,
        i: i
    };
}
/**
 * Get the priority of a media type.
 * @private
 */ function getMediaTypePriority(type, accepted, index) {
    var priority = {
        o: -1,
        q: 0,
        s: 0
    };
    for(var i = 0; i < accepted.length; i++){
        var spec = specify(type, accepted[i], index);
        if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {
            priority = spec;
        }
    }
    return priority;
}
/**
 * Get the specificity of the media type.
 * @private
 */ function specify(type, spec, index) {
    var p = parseMediaType(type);
    var s = 0;
    if (!p) {
        return null;
    }
    if (spec.type.toLowerCase() == p.type.toLowerCase()) {
        s |= 4;
    } else if (spec.type != '*') {
        return null;
    }
    if (spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {
        s |= 2;
    } else if (spec.subtype != '*') {
        return null;
    }
    var keys = Object.keys(spec.params);
    if (keys.length > 0) {
        if (keys.every(function(k) {
            return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();
        })) {
            s |= 1;
        } else {
            return null;
        }
    }
    return {
        i: index,
        o: spec.i,
        q: spec.q,
        s: s
    };
}
/**
 * Get the preferred media types from an Accept header.
 * @public
 */ function preferredMediaTypes(accept, provided) {
    // RFC 2616 sec 14.2: no header = */*
    var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');
    if (!provided) {
        // sorted list of all types
        return accepts.filter(isQuality).sort(compareSpecs).map(getFullType);
    }
    var priorities = provided.map(function getPriority(type, index) {
        return getMediaTypePriority(type, accepts, index);
    });
    // sorted list of accepted types
    return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {
        return provided[priorities.indexOf(priority)];
    });
}
/**
 * Compare two specs.
 * @private
 */ function compareSpecs(a, b) {
    return b.q - a.q || b.s - a.s || a.o - b.o || a.i - b.i || 0;
}
/**
 * Get full type string.
 * @private
 */ function getFullType(spec) {
    return spec.type + '/' + spec.subtype;
}
/**
 * Check if a spec has any quality.
 * @private
 */ function isQuality(spec) {
    return spec.q > 0;
}
/**
 * Count the number of quotes in a string.
 * @private
 */ function quoteCount(string) {
    var count = 0;
    var index = 0;
    while((index = string.indexOf('"', index)) !== -1){
        count++;
        index++;
    }
    return count;
}
/**
 * Split a key value pair.
 * @private
 */ function splitKeyValuePair(str) {
    var index = str.indexOf('=');
    var key;
    var val;
    if (index === -1) {
        key = str;
    } else {
        key = str.slice(0, index);
        val = str.slice(index + 1);
    }
    return [
        key,
        val
    ];
}
/**
 * Split an Accept header into media types.
 * @private
 */ function splitMediaTypes(accept) {
    var accepts = accept.split(',');
    for(var i = 1, j = 0; i < accepts.length; i++){
        if (quoteCount(accepts[j]) % 2 == 0) {
            accepts[++j] = accepts[i];
        } else {
            accepts[j] += ',' + accepts[i];
        }
    }
    // trim accepts
    accepts.length = j + 1;
    return accepts;
}
/**
 * Split a string of parameters.
 * @private
 */ function splitParameters(str) {
    var parameters = str.split(';');
    for(var i = 1, j = 0; i < parameters.length; i++){
        if (quoteCount(parameters[j]) % 2 == 0) {
            parameters[++j] = parameters[i];
        } else {
            parameters[j] += ';' + parameters[i];
        }
    }
    // trim parameters
    parameters.length = j + 1;
    for(var i = 0; i < parameters.length; i++){
        parameters[i] = parameters[i].trim();
    }
    return parameters;
}
}}),
"[project]/node_modules/negotiator/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*!
 * negotiator
 * Copyright(c) 2012 Federico Romero
 * Copyright(c) 2012-2014 Isaac Z. Schlueter
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */ 'use strict';
var preferredCharsets = __turbopack_context__.r("[project]/node_modules/negotiator/lib/charset.js [app-route] (ecmascript)");
var preferredEncodings = __turbopack_context__.r("[project]/node_modules/negotiator/lib/encoding.js [app-route] (ecmascript)");
var preferredLanguages = __turbopack_context__.r("[project]/node_modules/negotiator/lib/language.js [app-route] (ecmascript)");
var preferredMediaTypes = __turbopack_context__.r("[project]/node_modules/negotiator/lib/mediaType.js [app-route] (ecmascript)");
/**
 * Module exports.
 * @public
 */ module.exports = Negotiator;
module.exports.Negotiator = Negotiator;
/**
 * Create a Negotiator instance from a request.
 * @param {object} request
 * @public
 */ function Negotiator(request) {
    if (!(this instanceof Negotiator)) {
        return new Negotiator(request);
    }
    this.request = request;
}
Negotiator.prototype.charset = function charset(available) {
    var set = this.charsets(available);
    return set && set[0];
};
Negotiator.prototype.charsets = function charsets(available) {
    return preferredCharsets(this.request.headers['accept-charset'], available);
};
Negotiator.prototype.encoding = function encoding(available, preferred) {
    var set = this.encodings(available, preferred);
    return set && set[0];
};
Negotiator.prototype.encodings = function encodings(available, preferred) {
    return preferredEncodings(this.request.headers['accept-encoding'], available, preferred);
};
Negotiator.prototype.language = function language(available) {
    var set = this.languages(available);
    return set && set[0];
};
Negotiator.prototype.languages = function languages(available) {
    return preferredLanguages(this.request.headers['accept-language'], available);
};
Negotiator.prototype.mediaType = function mediaType(available) {
    var set = this.mediaTypes(available);
    return set && set[0];
};
Negotiator.prototype.mediaTypes = function mediaTypes(available) {
    return preferredMediaTypes(this.request.headers.accept, available);
};
// Backwards compatibility
Negotiator.prototype.preferredCharset = Negotiator.prototype.charset;
Negotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;
Negotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;
Negotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;
Negotiator.prototype.preferredLanguage = Negotiator.prototype.language;
Negotiator.prototype.preferredLanguages = Negotiator.prototype.languages;
Negotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;
Negotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;
}}),
"[project]/node_modules/inherits/inherits_browser.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
if (typeof Object.create === 'function') {
    // implementation from standard node.js 'util' module
    module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
            ctor.super_ = superCtor;
            ctor.prototype = Object.create(superCtor.prototype, {
                constructor: {
                    value: ctor,
                    enumerable: false,
                    writable: true,
                    configurable: true
                }
            });
        }
    };
} else {
    // old school shim for old browsers
    module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
            ctor.super_ = superCtor;
            var TempCtor = function() {};
            TempCtor.prototype = superCtor.prototype;
            ctor.prototype = new TempCtor();
            ctor.prototype.constructor = ctor;
        }
    };
}
}}),
"[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
try {
    var util = __turbopack_context__.r("[externals]/util [external] (util, cjs)");
    /* istanbul ignore next */ if (typeof util.inherits !== 'function') throw '';
    module.exports = util.inherits;
} catch (e) {
    /* istanbul ignore next */ module.exports = __turbopack_context__.r("[project]/node_modules/inherits/inherits_browser.js [app-route] (ecmascript)");
}
}}),
"[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */ /* eslint-disable node/no-deprecated-api */ var buffer = __turbopack_context__.r("[externals]/buffer [external] (buffer, cjs)");
var Buffer = buffer.Buffer;
// alternative to using Object.keys for old browsers
function copyProps(src, dst) {
    for(var key in src){
        dst[key] = src[key];
    }
}
if (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {
    module.exports = buffer;
} else {
    // Copy properties from require('buffer')
    copyProps(buffer, exports);
    exports.Buffer = SafeBuffer;
}
function SafeBuffer(arg, encodingOrOffset, length) {
    return Buffer(arg, encodingOrOffset, length);
}
SafeBuffer.prototype = Object.create(Buffer.prototype);
// Copy static methods from Buffer
copyProps(Buffer, SafeBuffer);
SafeBuffer.from = function(arg, encodingOrOffset, length) {
    if (typeof arg === 'number') {
        throw new TypeError('Argument must not be a number');
    }
    return Buffer(arg, encodingOrOffset, length);
};
SafeBuffer.alloc = function(size, fill, encoding) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    var buf = Buffer(size);
    if (fill !== undefined) {
        if (typeof encoding === 'string') {
            buf.fill(fill, encoding);
        } else {
            buf.fill(fill);
        }
    } else {
        buf.fill(0);
    }
    return buf;
};
SafeBuffer.allocUnsafe = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return Buffer(size);
};
SafeBuffer.allocUnsafeSlow = function(size) {
    if (typeof size !== 'number') {
        throw new TypeError('Argument must be a number');
    }
    return buffer.SlowBuffer(size);
};
}}),
"[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
// prototype class for hash functions
function Hash(blockSize, finalSize) {
    this._block = Buffer.alloc(blockSize);
    this._finalSize = finalSize;
    this._blockSize = blockSize;
    this._len = 0;
}
Hash.prototype.update = function(data, enc) {
    if (typeof data === 'string') {
        enc = enc || 'utf8';
        data = Buffer.from(data, enc);
    }
    var block = this._block;
    var blockSize = this._blockSize;
    var length = data.length;
    var accum = this._len;
    for(var offset = 0; offset < length;){
        var assigned = accum % blockSize;
        var remainder = Math.min(length - offset, blockSize - assigned);
        for(var i = 0; i < remainder; i++){
            block[assigned + i] = data[offset + i];
        }
        accum += remainder;
        offset += remainder;
        if (accum % blockSize === 0) {
            this._update(block);
        }
    }
    this._len += length;
    return this;
};
Hash.prototype.digest = function(enc) {
    var rem = this._len % this._blockSize;
    this._block[rem] = 0x80;
    // zero (rem + 1) trailing bits, where (rem + 1) is the smallest
    // non-negative solution to the equation (length + 1 + (rem + 1)) === finalSize mod blockSize
    this._block.fill(0, rem + 1);
    if (rem >= this._finalSize) {
        this._update(this._block);
        this._block.fill(0);
    }
    var bits = this._len * 8;
    // uint32
    if (bits <= 0xffffffff) {
        this._block.writeUInt32BE(bits, this._blockSize - 4);
    // uint64
    } else {
        var lowBits = (bits & 0xffffffff) >>> 0;
        var highBits = (bits - lowBits) / 0x100000000;
        this._block.writeUInt32BE(highBits, this._blockSize - 8);
        this._block.writeUInt32BE(lowBits, this._blockSize - 4);
    }
    this._update(this._block);
    var hash = this._hash();
    return enc ? hash.toString(enc) : hash;
};
Hash.prototype._update = function() {
    throw new Error('_update must be implemented by subclass');
};
module.exports = Hash;
}}),
"[project]/node_modules/sha.js/sha.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*
 * A JavaScript implementation of the Secure Hash Algorithm, SHA-0, as defined
 * in FIPS PUB 180-1
 * This source code is derived from sha1.js of the same repository.
 * The difference between SHA-0 and SHA-1 is just a bitwise rotate left
 * operation was added.
 */ var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var K = [
    0x5a827999,
    0x6ed9eba1,
    0x8f1bbcdc | 0,
    0xca62c1d6 | 0
];
var W = new Array(80);
function Sha() {
    this.init();
    this._w = W;
    Hash.call(this, 64, 56);
}
inherits(Sha, Hash);
Sha.prototype.init = function() {
    this._a = 0x67452301;
    this._b = 0xefcdab89;
    this._c = 0x98badcfe;
    this._d = 0x10325476;
    this._e = 0xc3d2e1f0;
    return this;
};
function rotl5(num) {
    return num << 5 | num >>> 27;
}
function rotl30(num) {
    return num << 30 | num >>> 2;
}
function ft(s, b, c, d) {
    if (s === 0) return b & c | ~b & d;
    if (s === 2) return b & c | b & d | c & d;
    return b ^ c ^ d;
}
Sha.prototype._update = function(M) {
    var W = this._w;
    var a = this._a | 0;
    var b = this._b | 0;
    var c = this._c | 0;
    var d = this._d | 0;
    var e = this._e | 0;
    for(var i = 0; i < 16; ++i)W[i] = M.readInt32BE(i * 4);
    for(; i < 80; ++i)W[i] = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];
    for(var j = 0; j < 80; ++j){
        var s = ~~(j / 20);
        var t = rotl5(a) + ft(s, b, c, d) + e + W[j] + K[s] | 0;
        e = d;
        d = c;
        c = rotl30(b);
        b = a;
        a = t;
    }
    this._a = a + this._a | 0;
    this._b = b + this._b | 0;
    this._c = c + this._c | 0;
    this._d = d + this._d | 0;
    this._e = e + this._e | 0;
};
Sha.prototype._hash = function() {
    var H = Buffer.allocUnsafe(20);
    H.writeInt32BE(this._a | 0, 0);
    H.writeInt32BE(this._b | 0, 4);
    H.writeInt32BE(this._c | 0, 8);
    H.writeInt32BE(this._d | 0, 12);
    H.writeInt32BE(this._e | 0, 16);
    return H;
};
module.exports = Sha;
}}),
"[project]/node_modules/sha.js/sha1.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/*
 * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined
 * in FIPS PUB 180-1
 * Version 2.1a Copyright Paul Johnston 2000 - 2002.
 * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
 * Distributed under the BSD License
 * See http://pajhome.org.uk/crypt/md5 for details.
 */ var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var K = [
    0x5a827999,
    0x6ed9eba1,
    0x8f1bbcdc | 0,
    0xca62c1d6 | 0
];
var W = new Array(80);
function Sha1() {
    this.init();
    this._w = W;
    Hash.call(this, 64, 56);
}
inherits(Sha1, Hash);
Sha1.prototype.init = function() {
    this._a = 0x67452301;
    this._b = 0xefcdab89;
    this._c = 0x98badcfe;
    this._d = 0x10325476;
    this._e = 0xc3d2e1f0;
    return this;
};
function rotl1(num) {
    return num << 1 | num >>> 31;
}
function rotl5(num) {
    return num << 5 | num >>> 27;
}
function rotl30(num) {
    return num << 30 | num >>> 2;
}
function ft(s, b, c, d) {
    if (s === 0) return b & c | ~b & d;
    if (s === 2) return b & c | b & d | c & d;
    return b ^ c ^ d;
}
Sha1.prototype._update = function(M) {
    var W = this._w;
    var a = this._a | 0;
    var b = this._b | 0;
    var c = this._c | 0;
    var d = this._d | 0;
    var e = this._e | 0;
    for(var i = 0; i < 16; ++i)W[i] = M.readInt32BE(i * 4);
    for(; i < 80; ++i)W[i] = rotl1(W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16]);
    for(var j = 0; j < 80; ++j){
        var s = ~~(j / 20);
        var t = rotl5(a) + ft(s, b, c, d) + e + W[j] + K[s] | 0;
        e = d;
        d = c;
        c = rotl30(b);
        b = a;
        a = t;
    }
    this._a = a + this._a | 0;
    this._b = b + this._b | 0;
    this._c = c + this._c | 0;
    this._d = d + this._d | 0;
    this._e = e + this._e | 0;
};
Sha1.prototype._hash = function() {
    var H = Buffer.allocUnsafe(20);
    H.writeInt32BE(this._a | 0, 0);
    H.writeInt32BE(this._b | 0, 4);
    H.writeInt32BE(this._c | 0, 8);
    H.writeInt32BE(this._d | 0, 12);
    H.writeInt32BE(this._e | 0, 16);
    return H;
};
module.exports = Sha1;
}}),
"[project]/node_modules/sha.js/sha256.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * A JavaScript implementation of the Secure Hash Algorithm, SHA-256, as defined
 * in FIPS 180-2
 * Version 2.2-beta Copyright Angel Marin, Paul Johnston 2000 - 2009.
 * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
 *
 */ var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var K = [
    0x428A2F98,
    0x71374491,
    0xB5C0FBCF,
    0xE9B5DBA5,
    0x3956C25B,
    0x59F111F1,
    0x923F82A4,
    0xAB1C5ED5,
    0xD807AA98,
    0x12835B01,
    0x243185BE,
    0x550C7DC3,
    0x72BE5D74,
    0x80DEB1FE,
    0x9BDC06A7,
    0xC19BF174,
    0xE49B69C1,
    0xEFBE4786,
    0x0FC19DC6,
    0x240CA1CC,
    0x2DE92C6F,
    0x4A7484AA,
    0x5CB0A9DC,
    0x76F988DA,
    0x983E5152,
    0xA831C66D,
    0xB00327C8,
    0xBF597FC7,
    0xC6E00BF3,
    0xD5A79147,
    0x06CA6351,
    0x14292967,
    0x27B70A85,
    0x2E1B2138,
    0x4D2C6DFC,
    0x53380D13,
    0x650A7354,
    0x766A0ABB,
    0x81C2C92E,
    0x92722C85,
    0xA2BFE8A1,
    0xA81A664B,
    0xC24B8B70,
    0xC76C51A3,
    0xD192E819,
    0xD6990624,
    0xF40E3585,
    0x106AA070,
    0x19A4C116,
    0x1E376C08,
    0x2748774C,
    0x34B0BCB5,
    0x391C0CB3,
    0x4ED8AA4A,
    0x5B9CCA4F,
    0x682E6FF3,
    0x748F82EE,
    0x78A5636F,
    0x84C87814,
    0x8CC70208,
    0x90BEFFFA,
    0xA4506CEB,
    0xBEF9A3F7,
    0xC67178F2
];
var W = new Array(64);
function Sha256() {
    this.init();
    this._w = W // new Array(64)
    ;
    Hash.call(this, 64, 56);
}
inherits(Sha256, Hash);
Sha256.prototype.init = function() {
    this._a = 0x6a09e667;
    this._b = 0xbb67ae85;
    this._c = 0x3c6ef372;
    this._d = 0xa54ff53a;
    this._e = 0x510e527f;
    this._f = 0x9b05688c;
    this._g = 0x1f83d9ab;
    this._h = 0x5be0cd19;
    return this;
};
function ch(x, y, z) {
    return z ^ x & (y ^ z);
}
function maj(x, y, z) {
    return x & y | z & (x | y);
}
function sigma0(x) {
    return (x >>> 2 | x << 30) ^ (x >>> 13 | x << 19) ^ (x >>> 22 | x << 10);
}
function sigma1(x) {
    return (x >>> 6 | x << 26) ^ (x >>> 11 | x << 21) ^ (x >>> 25 | x << 7);
}
function gamma0(x) {
    return (x >>> 7 | x << 25) ^ (x >>> 18 | x << 14) ^ x >>> 3;
}
function gamma1(x) {
    return (x >>> 17 | x << 15) ^ (x >>> 19 | x << 13) ^ x >>> 10;
}
Sha256.prototype._update = function(M) {
    var W = this._w;
    var a = this._a | 0;
    var b = this._b | 0;
    var c = this._c | 0;
    var d = this._d | 0;
    var e = this._e | 0;
    var f = this._f | 0;
    var g = this._g | 0;
    var h = this._h | 0;
    for(var i = 0; i < 16; ++i)W[i] = M.readInt32BE(i * 4);
    for(; i < 64; ++i)W[i] = gamma1(W[i - 2]) + W[i - 7] + gamma0(W[i - 15]) + W[i - 16] | 0;
    for(var j = 0; j < 64; ++j){
        var T1 = h + sigma1(e) + ch(e, f, g) + K[j] + W[j] | 0;
        var T2 = sigma0(a) + maj(a, b, c) | 0;
        h = g;
        g = f;
        f = e;
        e = d + T1 | 0;
        d = c;
        c = b;
        b = a;
        a = T1 + T2 | 0;
    }
    this._a = a + this._a | 0;
    this._b = b + this._b | 0;
    this._c = c + this._c | 0;
    this._d = d + this._d | 0;
    this._e = e + this._e | 0;
    this._f = f + this._f | 0;
    this._g = g + this._g | 0;
    this._h = h + this._h | 0;
};
Sha256.prototype._hash = function() {
    var H = Buffer.allocUnsafe(32);
    H.writeInt32BE(this._a, 0);
    H.writeInt32BE(this._b, 4);
    H.writeInt32BE(this._c, 8);
    H.writeInt32BE(this._d, 12);
    H.writeInt32BE(this._e, 16);
    H.writeInt32BE(this._f, 20);
    H.writeInt32BE(this._g, 24);
    H.writeInt32BE(this._h, 28);
    return H;
};
module.exports = Sha256;
}}),
"[project]/node_modules/sha.js/sha224.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * A JavaScript implementation of the Secure Hash Algorithm, SHA-256, as defined
 * in FIPS 180-2
 * Version 2.2-beta Copyright Angel Marin, Paul Johnston 2000 - 2009.
 * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
 *
 */ var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var Sha256 = __turbopack_context__.r("[project]/node_modules/sha.js/sha256.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var W = new Array(64);
function Sha224() {
    this.init();
    this._w = W // new Array(64)
    ;
    Hash.call(this, 64, 56);
}
inherits(Sha224, Sha256);
Sha224.prototype.init = function() {
    this._a = 0xc1059ed8;
    this._b = 0x367cd507;
    this._c = 0x3070dd17;
    this._d = 0xf70e5939;
    this._e = 0xffc00b31;
    this._f = 0x68581511;
    this._g = 0x64f98fa7;
    this._h = 0xbefa4fa4;
    return this;
};
Sha224.prototype._hash = function() {
    var H = Buffer.allocUnsafe(28);
    H.writeInt32BE(this._a, 0);
    H.writeInt32BE(this._b, 4);
    H.writeInt32BE(this._c, 8);
    H.writeInt32BE(this._d, 12);
    H.writeInt32BE(this._e, 16);
    H.writeInt32BE(this._f, 20);
    H.writeInt32BE(this._g, 24);
    return H;
};
module.exports = Sha224;
}}),
"[project]/node_modules/sha.js/sha512.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var K = [
    0x428a2f98,
    0xd728ae22,
    0x71374491,
    0x23ef65cd,
    0xb5c0fbcf,
    0xec4d3b2f,
    0xe9b5dba5,
    0x8189dbbc,
    0x3956c25b,
    0xf348b538,
    0x59f111f1,
    0xb605d019,
    0x923f82a4,
    0xaf194f9b,
    0xab1c5ed5,
    0xda6d8118,
    0xd807aa98,
    0xa3030242,
    0x12835b01,
    0x45706fbe,
    0x243185be,
    0x4ee4b28c,
    0x550c7dc3,
    0xd5ffb4e2,
    0x72be5d74,
    0xf27b896f,
    0x80deb1fe,
    0x3b1696b1,
    0x9bdc06a7,
    0x25c71235,
    0xc19bf174,
    0xcf692694,
    0xe49b69c1,
    0x9ef14ad2,
    0xefbe4786,
    0x384f25e3,
    0x0fc19dc6,
    0x8b8cd5b5,
    0x240ca1cc,
    0x77ac9c65,
    0x2de92c6f,
    0x592b0275,
    0x4a7484aa,
    0x6ea6e483,
    0x5cb0a9dc,
    0xbd41fbd4,
    0x76f988da,
    0x831153b5,
    0x983e5152,
    0xee66dfab,
    0xa831c66d,
    0x2db43210,
    0xb00327c8,
    0x98fb213f,
    0xbf597fc7,
    0xbeef0ee4,
    0xc6e00bf3,
    0x3da88fc2,
    0xd5a79147,
    0x930aa725,
    0x06ca6351,
    0xe003826f,
    0x14292967,
    0x0a0e6e70,
    0x27b70a85,
    0x46d22ffc,
    0x2e1b2138,
    0x5c26c926,
    0x4d2c6dfc,
    0x5ac42aed,
    0x53380d13,
    0x9d95b3df,
    0x650a7354,
    0x8baf63de,
    0x766a0abb,
    0x3c77b2a8,
    0x81c2c92e,
    0x47edaee6,
    0x92722c85,
    0x1482353b,
    0xa2bfe8a1,
    0x4cf10364,
    0xa81a664b,
    0xbc423001,
    0xc24b8b70,
    0xd0f89791,
    0xc76c51a3,
    0x0654be30,
    0xd192e819,
    0xd6ef5218,
    0xd6990624,
    0x5565a910,
    0xf40e3585,
    0x5771202a,
    0x106aa070,
    0x32bbd1b8,
    0x19a4c116,
    0xb8d2d0c8,
    0x1e376c08,
    0x5141ab53,
    0x2748774c,
    0xdf8eeb99,
    0x34b0bcb5,
    0xe19b48a8,
    0x391c0cb3,
    0xc5c95a63,
    0x4ed8aa4a,
    0xe3418acb,
    0x5b9cca4f,
    0x7763e373,
    0x682e6ff3,
    0xd6b2b8a3,
    0x748f82ee,
    0x5defb2fc,
    0x78a5636f,
    0x43172f60,
    0x84c87814,
    0xa1f0ab72,
    0x8cc70208,
    0x1a6439ec,
    0x90befffa,
    0x23631e28,
    0xa4506ceb,
    0xde82bde9,
    0xbef9a3f7,
    0xb2c67915,
    0xc67178f2,
    0xe372532b,
    0xca273ece,
    0xea26619c,
    0xd186b8c7,
    0x21c0c207,
    0xeada7dd6,
    0xcde0eb1e,
    0xf57d4f7f,
    0xee6ed178,
    0x06f067aa,
    0x72176fba,
    0x0a637dc5,
    0xa2c898a6,
    0x113f9804,
    0xbef90dae,
    0x1b710b35,
    0x131c471b,
    0x28db77f5,
    0x23047d84,
    0x32caab7b,
    0x40c72493,
    0x3c9ebe0a,
    0x15c9bebc,
    0x431d67c4,
    0x9c100d4c,
    0x4cc5d4be,
    0xcb3e42b6,
    0x597f299c,
    0xfc657e2a,
    0x5fcb6fab,
    0x3ad6faec,
    0x6c44198c,
    0x4a475817
];
var W = new Array(160);
function Sha512() {
    this.init();
    this._w = W;
    Hash.call(this, 128, 112);
}
inherits(Sha512, Hash);
Sha512.prototype.init = function() {
    this._ah = 0x6a09e667;
    this._bh = 0xbb67ae85;
    this._ch = 0x3c6ef372;
    this._dh = 0xa54ff53a;
    this._eh = 0x510e527f;
    this._fh = 0x9b05688c;
    this._gh = 0x1f83d9ab;
    this._hh = 0x5be0cd19;
    this._al = 0xf3bcc908;
    this._bl = 0x84caa73b;
    this._cl = 0xfe94f82b;
    this._dl = 0x5f1d36f1;
    this._el = 0xade682d1;
    this._fl = 0x2b3e6c1f;
    this._gl = 0xfb41bd6b;
    this._hl = 0x137e2179;
    return this;
};
function Ch(x, y, z) {
    return z ^ x & (y ^ z);
}
function maj(x, y, z) {
    return x & y | z & (x | y);
}
function sigma0(x, xl) {
    return (x >>> 28 | xl << 4) ^ (xl >>> 2 | x << 30) ^ (xl >>> 7 | x << 25);
}
function sigma1(x, xl) {
    return (x >>> 14 | xl << 18) ^ (x >>> 18 | xl << 14) ^ (xl >>> 9 | x << 23);
}
function Gamma0(x, xl) {
    return (x >>> 1 | xl << 31) ^ (x >>> 8 | xl << 24) ^ x >>> 7;
}
function Gamma0l(x, xl) {
    return (x >>> 1 | xl << 31) ^ (x >>> 8 | xl << 24) ^ (x >>> 7 | xl << 25);
}
function Gamma1(x, xl) {
    return (x >>> 19 | xl << 13) ^ (xl >>> 29 | x << 3) ^ x >>> 6;
}
function Gamma1l(x, xl) {
    return (x >>> 19 | xl << 13) ^ (xl >>> 29 | x << 3) ^ (x >>> 6 | xl << 26);
}
function getCarry(a, b) {
    return a >>> 0 < b >>> 0 ? 1 : 0;
}
Sha512.prototype._update = function(M) {
    var W = this._w;
    var ah = this._ah | 0;
    var bh = this._bh | 0;
    var ch = this._ch | 0;
    var dh = this._dh | 0;
    var eh = this._eh | 0;
    var fh = this._fh | 0;
    var gh = this._gh | 0;
    var hh = this._hh | 0;
    var al = this._al | 0;
    var bl = this._bl | 0;
    var cl = this._cl | 0;
    var dl = this._dl | 0;
    var el = this._el | 0;
    var fl = this._fl | 0;
    var gl = this._gl | 0;
    var hl = this._hl | 0;
    for(var i = 0; i < 32; i += 2){
        W[i] = M.readInt32BE(i * 4);
        W[i + 1] = M.readInt32BE(i * 4 + 4);
    }
    for(; i < 160; i += 2){
        var xh = W[i - 15 * 2];
        var xl = W[i - 15 * 2 + 1];
        var gamma0 = Gamma0(xh, xl);
        var gamma0l = Gamma0l(xl, xh);
        xh = W[i - 2 * 2];
        xl = W[i - 2 * 2 + 1];
        var gamma1 = Gamma1(xh, xl);
        var gamma1l = Gamma1l(xl, xh);
        // W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16]
        var Wi7h = W[i - 7 * 2];
        var Wi7l = W[i - 7 * 2 + 1];
        var Wi16h = W[i - 16 * 2];
        var Wi16l = W[i - 16 * 2 + 1];
        var Wil = gamma0l + Wi7l | 0;
        var Wih = gamma0 + Wi7h + getCarry(Wil, gamma0l) | 0;
        Wil = Wil + gamma1l | 0;
        Wih = Wih + gamma1 + getCarry(Wil, gamma1l) | 0;
        Wil = Wil + Wi16l | 0;
        Wih = Wih + Wi16h + getCarry(Wil, Wi16l) | 0;
        W[i] = Wih;
        W[i + 1] = Wil;
    }
    for(var j = 0; j < 160; j += 2){
        Wih = W[j];
        Wil = W[j + 1];
        var majh = maj(ah, bh, ch);
        var majl = maj(al, bl, cl);
        var sigma0h = sigma0(ah, al);
        var sigma0l = sigma0(al, ah);
        var sigma1h = sigma1(eh, el);
        var sigma1l = sigma1(el, eh);
        // t1 = h + sigma1 + ch + K[j] + W[j]
        var Kih = K[j];
        var Kil = K[j + 1];
        var chh = Ch(eh, fh, gh);
        var chl = Ch(el, fl, gl);
        var t1l = hl + sigma1l | 0;
        var t1h = hh + sigma1h + getCarry(t1l, hl) | 0;
        t1l = t1l + chl | 0;
        t1h = t1h + chh + getCarry(t1l, chl) | 0;
        t1l = t1l + Kil | 0;
        t1h = t1h + Kih + getCarry(t1l, Kil) | 0;
        t1l = t1l + Wil | 0;
        t1h = t1h + Wih + getCarry(t1l, Wil) | 0;
        // t2 = sigma0 + maj
        var t2l = sigma0l + majl | 0;
        var t2h = sigma0h + majh + getCarry(t2l, sigma0l) | 0;
        hh = gh;
        hl = gl;
        gh = fh;
        gl = fl;
        fh = eh;
        fl = el;
        el = dl + t1l | 0;
        eh = dh + t1h + getCarry(el, dl) | 0;
        dh = ch;
        dl = cl;
        ch = bh;
        cl = bl;
        bh = ah;
        bl = al;
        al = t1l + t2l | 0;
        ah = t1h + t2h + getCarry(al, t1l) | 0;
    }
    this._al = this._al + al | 0;
    this._bl = this._bl + bl | 0;
    this._cl = this._cl + cl | 0;
    this._dl = this._dl + dl | 0;
    this._el = this._el + el | 0;
    this._fl = this._fl + fl | 0;
    this._gl = this._gl + gl | 0;
    this._hl = this._hl + hl | 0;
    this._ah = this._ah + ah + getCarry(this._al, al) | 0;
    this._bh = this._bh + bh + getCarry(this._bl, bl) | 0;
    this._ch = this._ch + ch + getCarry(this._cl, cl) | 0;
    this._dh = this._dh + dh + getCarry(this._dl, dl) | 0;
    this._eh = this._eh + eh + getCarry(this._el, el) | 0;
    this._fh = this._fh + fh + getCarry(this._fl, fl) | 0;
    this._gh = this._gh + gh + getCarry(this._gl, gl) | 0;
    this._hh = this._hh + hh + getCarry(this._hl, hl) | 0;
};
Sha512.prototype._hash = function() {
    var H = Buffer.allocUnsafe(64);
    function writeInt64BE(h, l, offset) {
        H.writeInt32BE(h, offset);
        H.writeInt32BE(l, offset + 4);
    }
    writeInt64BE(this._ah, this._al, 0);
    writeInt64BE(this._bh, this._bl, 8);
    writeInt64BE(this._ch, this._cl, 16);
    writeInt64BE(this._dh, this._dl, 24);
    writeInt64BE(this._eh, this._el, 32);
    writeInt64BE(this._fh, this._fl, 40);
    writeInt64BE(this._gh, this._gl, 48);
    writeInt64BE(this._hh, this._hl, 56);
    return H;
};
module.exports = Sha512;
}}),
"[project]/node_modules/sha.js/sha384.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var inherits = __turbopack_context__.r("[project]/node_modules/inherits/inherits.js [app-route] (ecmascript)");
var SHA512 = __turbopack_context__.r("[project]/node_modules/sha.js/sha512.js [app-route] (ecmascript)");
var Hash = __turbopack_context__.r("[project]/node_modules/sha.js/hash.js [app-route] (ecmascript)");
var Buffer = __turbopack_context__.r("[project]/node_modules/safe-buffer/index.js [app-route] (ecmascript)").Buffer;
var W = new Array(160);
function Sha384() {
    this.init();
    this._w = W;
    Hash.call(this, 128, 112);
}
inherits(Sha384, SHA512);
Sha384.prototype.init = function() {
    this._ah = 0xcbbb9d5d;
    this._bh = 0x629a292a;
    this._ch = 0x9159015a;
    this._dh = 0x152fecd8;
    this._eh = 0x67332667;
    this._fh = 0x8eb44a87;
    this._gh = 0xdb0c2e0d;
    this._hh = 0x47b5481d;
    this._al = 0xc1059ed8;
    this._bl = 0x367cd507;
    this._cl = 0x3070dd17;
    this._dl = 0xf70e5939;
    this._el = 0xffc00b31;
    this._fl = 0x68581511;
    this._gl = 0x64f98fa7;
    this._hl = 0xbefa4fa4;
    return this;
};
Sha384.prototype._hash = function() {
    var H = Buffer.allocUnsafe(48);
    function writeInt64BE(h, l, offset) {
        H.writeInt32BE(h, offset);
        H.writeInt32BE(l, offset + 4);
    }
    writeInt64BE(this._ah, this._al, 0);
    writeInt64BE(this._bh, this._bl, 8);
    writeInt64BE(this._ch, this._cl, 16);
    writeInt64BE(this._dh, this._dl, 24);
    writeInt64BE(this._eh, this._el, 32);
    writeInt64BE(this._fh, this._fl, 40);
    return H;
};
module.exports = Sha384;
}}),
"[project]/node_modules/sha.js/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var exports = module.exports = function SHA(algorithm) {
    algorithm = algorithm.toLowerCase();
    var Algorithm = exports[algorithm];
    if (!Algorithm) throw new Error(algorithm + ' is not supported (we accept pull requests)');
    return new Algorithm();
};
exports.sha = __turbopack_context__.r("[project]/node_modules/sha.js/sha.js [app-route] (ecmascript)");
exports.sha1 = __turbopack_context__.r("[project]/node_modules/sha.js/sha1.js [app-route] (ecmascript)");
exports.sha224 = __turbopack_context__.r("[project]/node_modules/sha.js/sha224.js [app-route] (ecmascript)");
exports.sha256 = __turbopack_context__.r("[project]/node_modules/sha.js/sha256.js [app-route] (ecmascript)");
exports.sha384 = __turbopack_context__.r("[project]/node_modules/sha.js/sha384.js [app-route] (ecmascript)");
exports.sha512 = __turbopack_context__.r("[project]/node_modules/sha.js/sha512.js [app-route] (ecmascript)");
}}),
"[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createHash = void 0;
const utils_isnodelike_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.isnodelike/dist/index.js [app-route] (ecmascript)");
function createHash(kind) {
    if (utils_isnodelike_1.isNodeLike && module.require) {
        return module.require("crypto").createHash(kind);
    }
    return __turbopack_context__.r("[project]/node_modules/sha.js/index.js [app-route] (ecmascript)")(kind);
}
exports.createHash = createHash; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/whatwg-mimetype/lib/utils.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
exports.removeLeadingAndTrailingHTTPWhitespace = (string)=>{
    return string.replace(/^[ \t\n\r]+/u, "").replace(/[ \t\n\r]+$/u, "");
};
exports.removeTrailingHTTPWhitespace = (string)=>{
    return string.replace(/[ \t\n\r]+$/u, "");
};
exports.isHTTPWhitespaceChar = (char)=>{
    return char === " " || char === "\t" || char === "\n" || char === "\r";
};
exports.solelyContainsHTTPTokenCodePoints = (string)=>{
    return /^[-!#$%&'*+.^_`|~A-Za-z0-9]*$/u.test(string);
};
exports.soleyContainsHTTPQuotedStringTokenCodePoints = (string)=>{
    return /^[\t\u0020-\u007E\u0080-\u00FF]*$/u.test(string);
};
exports.asciiLowercase = (string)=>{
    return string.replace(/[A-Z]/ug, (l)=>l.toLowerCase());
};
// This variant only implements it with the extract-value flag set.
exports.collectAnHTTPQuotedString = (input, position)=>{
    let value = "";
    position++;
    while(true){
        while(position < input.length && input[position] !== "\"" && input[position] !== "\\"){
            value += input[position];
            ++position;
        }
        if (position >= input.length) {
            break;
        }
        const quoteOrBackslash = input[position];
        ++position;
        if (quoteOrBackslash === "\\") {
            if (position >= input.length) {
                value += "\\";
                break;
            }
            value += input[position];
            ++position;
        } else {
            break;
        }
    }
    return [
        value,
        position
    ];
};
}}),
"[project]/node_modules/whatwg-mimetype/lib/mime-type-parameters.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const { asciiLowercase, solelyContainsHTTPTokenCodePoints, soleyContainsHTTPQuotedStringTokenCodePoints } = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/utils.js [app-route] (ecmascript)");
module.exports = class MIMETypeParameters {
    constructor(map){
        this._map = map;
    }
    get size() {
        return this._map.size;
    }
    get(name) {
        name = asciiLowercase(String(name));
        return this._map.get(name);
    }
    has(name) {
        name = asciiLowercase(String(name));
        return this._map.has(name);
    }
    set(name, value) {
        name = asciiLowercase(String(name));
        value = String(value);
        if (!solelyContainsHTTPTokenCodePoints(name)) {
            throw new Error(`Invalid MIME type parameter name "${name}": only HTTP token code points are valid.`);
        }
        if (!soleyContainsHTTPQuotedStringTokenCodePoints(value)) {
            throw new Error(`Invalid MIME type parameter value "${value}": only HTTP quoted-string token code points are ` + `valid.`);
        }
        return this._map.set(name, value);
    }
    clear() {
        this._map.clear();
    }
    delete(name) {
        name = asciiLowercase(String(name));
        return this._map.delete(name);
    }
    forEach(callbackFn, thisArg) {
        this._map.forEach(callbackFn, thisArg);
    }
    keys() {
        return this._map.keys();
    }
    values() {
        return this._map.values();
    }
    entries() {
        return this._map.entries();
    }
    [Symbol.iterator]() {
        return this._map[Symbol.iterator]();
    }
};
}}),
"[project]/node_modules/whatwg-mimetype/lib/parser.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const { removeLeadingAndTrailingHTTPWhitespace, removeTrailingHTTPWhitespace, isHTTPWhitespaceChar, solelyContainsHTTPTokenCodePoints, soleyContainsHTTPQuotedStringTokenCodePoints, asciiLowercase, collectAnHTTPQuotedString } = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/utils.js [app-route] (ecmascript)");
module.exports = (input)=>{
    input = removeLeadingAndTrailingHTTPWhitespace(input);
    let position = 0;
    let type = "";
    while(position < input.length && input[position] !== "/"){
        type += input[position];
        ++position;
    }
    if (type.length === 0 || !solelyContainsHTTPTokenCodePoints(type)) {
        return null;
    }
    if (position >= input.length) {
        return null;
    }
    // Skips past "/"
    ++position;
    let subtype = "";
    while(position < input.length && input[position] !== ";"){
        subtype += input[position];
        ++position;
    }
    subtype = removeTrailingHTTPWhitespace(subtype);
    if (subtype.length === 0 || !solelyContainsHTTPTokenCodePoints(subtype)) {
        return null;
    }
    const mimeType = {
        type: asciiLowercase(type),
        subtype: asciiLowercase(subtype),
        parameters: new Map()
    };
    while(position < input.length){
        // Skip past ";"
        ++position;
        while(isHTTPWhitespaceChar(input[position])){
            ++position;
        }
        let parameterName = "";
        while(position < input.length && input[position] !== ";" && input[position] !== "="){
            parameterName += input[position];
            ++position;
        }
        parameterName = asciiLowercase(parameterName);
        if (position < input.length) {
            if (input[position] === ";") {
                continue;
            }
            // Skip past "="
            ++position;
        }
        let parameterValue = null;
        if (input[position] === "\"") {
            [parameterValue, position] = collectAnHTTPQuotedString(input, position);
            while(position < input.length && input[position] !== ";"){
                ++position;
            }
        } else {
            parameterValue = "";
            while(position < input.length && input[position] !== ";"){
                parameterValue += input[position];
                ++position;
            }
            parameterValue = removeTrailingHTTPWhitespace(parameterValue);
            if (parameterValue === "") {
                continue;
            }
        }
        if (parameterName.length > 0 && solelyContainsHTTPTokenCodePoints(parameterName) && soleyContainsHTTPQuotedStringTokenCodePoints(parameterValue) && !mimeType.parameters.has(parameterName)) {
            mimeType.parameters.set(parameterName, parameterValue);
        }
    }
    return mimeType;
};
}}),
"[project]/node_modules/whatwg-mimetype/lib/serializer.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const { solelyContainsHTTPTokenCodePoints } = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/utils.js [app-route] (ecmascript)");
module.exports = (mimeType)=>{
    let serialization = `${mimeType.type}/${mimeType.subtype}`;
    if (mimeType.parameters.size === 0) {
        return serialization;
    }
    for (let [name, value] of mimeType.parameters){
        serialization += ";";
        serialization += name;
        serialization += "=";
        if (!solelyContainsHTTPTokenCodePoints(value) || value.length === 0) {
            value = value.replace(/(["\\])/ug, "\\$1");
            value = `"${value}"`;
        }
        serialization += value;
    }
    return serialization;
};
}}),
"[project]/node_modules/whatwg-mimetype/lib/mime-type.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
const MIMETypeParameters = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/mime-type-parameters.js [app-route] (ecmascript)");
const parse = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/parser.js [app-route] (ecmascript)");
const serialize = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/serializer.js [app-route] (ecmascript)");
const { asciiLowercase, solelyContainsHTTPTokenCodePoints } = __turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/utils.js [app-route] (ecmascript)");
module.exports = class MIMEType {
    constructor(string){
        string = String(string);
        const result = parse(string);
        if (result === null) {
            throw new Error(`Could not parse MIME type string "${string}"`);
        }
        this._type = result.type;
        this._subtype = result.subtype;
        this._parameters = new MIMETypeParameters(result.parameters);
    }
    static parse(string) {
        try {
            return new this(string);
        } catch (e) {
            return null;
        }
    }
    get essence() {
        return `${this.type}/${this.subtype}`;
    }
    get type() {
        return this._type;
    }
    set type(value) {
        value = asciiLowercase(String(value));
        if (value.length === 0) {
            throw new Error("Invalid type: must be a non-empty string");
        }
        if (!solelyContainsHTTPTokenCodePoints(value)) {
            throw new Error(`Invalid type ${value}: must contain only HTTP token code points`);
        }
        this._type = value;
    }
    get subtype() {
        return this._subtype;
    }
    set subtype(value) {
        value = asciiLowercase(String(value));
        if (value.length === 0) {
            throw new Error("Invalid subtype: must be a non-empty string");
        }
        if (!solelyContainsHTTPTokenCodePoints(value)) {
            throw new Error(`Invalid subtype ${value}: must contain only HTTP token code points`);
        }
        this._subtype = value;
    }
    get parameters() {
        return this._parameters;
    }
    toString() {
        // The serialize function works on both "MIME type records" (i.e. the results of parse) and on this class, since
        // this class's interface is identical.
        return serialize(this);
    }
    isJavaScript({ prohibitParameters = false } = {}) {
        switch(this._type){
            case "text":
                {
                    switch(this._subtype){
                        case "ecmascript":
                        case "javascript":
                        case "javascript1.0":
                        case "javascript1.1":
                        case "javascript1.2":
                        case "javascript1.3":
                        case "javascript1.4":
                        case "javascript1.5":
                        case "jscript":
                        case "livescript":
                        case "x-ecmascript":
                        case "x-javascript":
                            {
                                return !prohibitParameters || this._parameters.size === 0;
                            }
                        default:
                            {
                                return false;
                            }
                    }
                }
            case "application":
                {
                    switch(this._subtype){
                        case "ecmascript":
                        case "javascript":
                        case "x-ecmascript":
                        case "x-javascript":
                            {
                                return !prohibitParameters || this._parameters.size === 0;
                            }
                        default:
                            {
                                return false;
                            }
                    }
                }
            default:
                {
                    return false;
                }
        }
    }
    isXML() {
        return this._subtype === "xml" && (this._type === "text" || this._type === "application") || this._subtype.endsWith("+xml");
    }
    isHTML() {
        return this._subtype === "html" && this._type === "text";
    }
};
}}),
"[project]/node_modules/cross-inspect/esm/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Taken from graphql-js
// https://github.com/graphql/graphql-js/blob/main/src/jsutils/inspect.ts
__turbopack_context__.s({
    "inspect": (()=>inspect)
});
const MAX_RECURSIVE_DEPTH = 3;
function inspect(value) {
    return formatValue(value, []);
}
function formatValue(value, seenValues) {
    switch(typeof value){
        case 'string':
            return JSON.stringify(value);
        case 'function':
            return value.name ? `[function ${value.name}]` : '[function]';
        case 'object':
            return formatObjectValue(value, seenValues);
        default:
            return String(value);
    }
}
function formatError(value) {
    // eslint-disable-next-line no-constant-condition
    if (value.name = 'GraphQLError') {
        return value.toString();
    }
    return `${value.name}: ${value.message};\n ${value.stack}`;
}
function formatObjectValue(value, previouslySeenValues) {
    if (value === null) {
        return 'null';
    }
    if (value instanceof Error) {
        if (value.name === 'AggregateError') {
            return formatError(value) + '\n' + formatArray(value.errors, previouslySeenValues);
        }
        return formatError(value);
    }
    if (previouslySeenValues.includes(value)) {
        return '[Circular]';
    }
    const seenValues = [
        ...previouslySeenValues,
        value
    ];
    if (isJSONable(value)) {
        const jsonValue = value.toJSON();
        // check for infinite recursion
        if (jsonValue !== value) {
            return typeof jsonValue === 'string' ? jsonValue : formatValue(jsonValue, seenValues);
        }
    } else if (Array.isArray(value)) {
        return formatArray(value, seenValues);
    }
    return formatObject(value, seenValues);
}
function isJSONable(value) {
    return typeof value.toJSON === 'function';
}
function formatObject(object, seenValues) {
    const entries = Object.entries(object);
    if (entries.length === 0) {
        return '{}';
    }
    if (seenValues.length > MAX_RECURSIVE_DEPTH) {
        return '[' + getObjectTag(object) + ']';
    }
    const properties = entries.map(([key, value])=>key + ': ' + formatValue(value, seenValues));
    return '{ ' + properties.join(', ') + ' }';
}
function formatArray(array, seenValues) {
    if (array.length === 0) {
        return '[]';
    }
    if (seenValues.length > MAX_RECURSIVE_DEPTH) {
        return '[Array]';
    }
    const len = array.length;
    const items = [];
    for(let i = 0; i < len; ++i){
        items.push(formatValue(array[i], seenValues));
    }
    return '[' + items.join(', ') + ']';
}
function getObjectTag(object) {
    const tag = Object.prototype.toString.call(object).replace(/^\[object /, '').replace(/]$/, '');
    if (tag === 'Object' && typeof object.constructor === 'function') {
        const name = object.constructor.name;
        if (typeof name === 'string' && name !== '') {
            return name;
        }
    }
    return tag;
}
}}),
"[project]/node_modules/@whatwg-node/promise-helpers/esm/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createDeferredPromise": (()=>createDeferredPromise),
    "fakePromise": (()=>fakePromise),
    "fakeRejectPromise": (()=>fakeRejectPromise),
    "handleMaybePromise": (()=>handleMaybePromise),
    "isActualPromise": (()=>isActualPromise),
    "isPromise": (()=>isPromise),
    "iterateAsync": (()=>iterateAsync),
    "iterateAsyncVoid": (()=>iterateAsync),
    "mapAsyncIterator": (()=>mapAsyncIterator),
    "mapMaybePromise": (()=>mapMaybePromise),
    "promiseLikeFinally": (()=>promiseLikeFinally),
    "unfakePromise": (()=>unfakePromise)
});
const kFakePromise = Symbol.for('@whatwg-node/promise-helpers/FakePromise');
function isPromise(value) {
    return value?.then != null;
}
function isActualPromise(value) {
    const maybePromise = value;
    return maybePromise && maybePromise.then && maybePromise.catch && maybePromise.finally;
}
function handleMaybePromise(inputFactory, outputSuccessFactory, outputErrorFactory, finallyFactory) {
    let result$ = fakePromise().then(inputFactory).then(outputSuccessFactory, outputErrorFactory);
    if (finallyFactory) {
        result$ = result$.finally(finallyFactory);
    }
    return unfakePromise(result$);
}
function fakePromise(value) {
    if (value && isActualPromise(value)) {
        return value;
    }
    if (isPromise(value)) {
        return {
            then: (resolve, reject)=>fakePromise(value.then(resolve, reject)),
            catch: (reject)=>fakePromise(value.then((res)=>res, reject)),
            finally: (cb)=>fakePromise(cb ? promiseLikeFinally(value, cb) : value),
            [Symbol.toStringTag]: 'Promise'
        };
    }
    // Write a fake promise to avoid the promise constructor
    // being called with `new Promise` in the browser.
    return {
        then (resolve) {
            if (resolve) {
                try {
                    return fakePromise(resolve(value));
                } catch (err) {
                    return fakeRejectPromise(err);
                }
            }
            return this;
        },
        catch () {
            return this;
        },
        finally (cb) {
            if (cb) {
                try {
                    return fakePromise(cb()).then(()=>value, ()=>value);
                } catch (err) {
                    return fakeRejectPromise(err);
                }
            }
            return this;
        },
        [Symbol.toStringTag]: 'Promise',
        __fakePromiseValue: value,
        [kFakePromise]: 'resolved'
    };
}
function createDeferredPromise() {
    if (Promise.withResolvers) {
        return Promise.withResolvers();
    }
    let resolveFn;
    let rejectFn;
    const promise = new Promise(function deferredPromiseExecutor(resolve, reject) {
        resolveFn = resolve;
        rejectFn = reject;
    });
    return {
        promise,
        get resolve () {
            return resolveFn;
        },
        get reject () {
            return rejectFn;
        }
    };
}
;
function iterateAsync(iterable, callback, results) {
    if (iterable?.length === 0) {
        return;
    }
    const iterator = iterable[Symbol.iterator]();
    let index = 0;
    function iterate() {
        const { done: endOfIterator, value } = iterator.next();
        if (endOfIterator) {
            return;
        }
        let endedEarly = false;
        function endEarly() {
            endedEarly = true;
        }
        return handleMaybePromise(function handleCallback() {
            return callback(value, endEarly, index++);
        }, function handleCallbackResult(result) {
            if (result) {
                results?.push(result);
            }
            if (endedEarly) {
                return;
            }
            return iterate();
        });
    }
    return iterate();
}
function fakeRejectPromise(error) {
    return {
        then (_resolve, reject) {
            if (reject) {
                try {
                    return fakePromise(reject(error));
                } catch (err) {
                    return fakeRejectPromise(err);
                }
            }
            return this;
        },
        catch (reject) {
            if (reject) {
                try {
                    return fakePromise(reject(error));
                } catch (err) {
                    return fakeRejectPromise(err);
                }
            }
            return this;
        },
        finally (cb) {
            if (cb) {
                try {
                    cb();
                } catch (err) {
                    return fakeRejectPromise(err);
                }
            }
            return this;
        },
        __fakeRejectError: error,
        [Symbol.toStringTag]: 'Promise',
        [kFakePromise]: 'rejected'
    };
}
function mapMaybePromise(input, onSuccess, onError) {
    return handleMaybePromise(()=>input, onSuccess, onError);
}
function mapAsyncIterator(iterator, onNext, onError, onEnd) {
    if (Symbol.asyncIterator in iterator) {
        iterator = iterator[Symbol.asyncIterator]();
    }
    let $return;
    let abruptClose;
    let onEndWithValue;
    if (onEnd) {
        let onEndWithValueResult /** R in onEndWithValue */ ;
        onEndWithValue = (value)=>{
            onEndWithValueResult ||= handleMaybePromise(onEnd, ()=>value, ()=>value);
            return onEndWithValueResult;
        };
    }
    if (typeof iterator.return === 'function') {
        $return = iterator.return;
        abruptClose = (error)=>{
            const rethrow = ()=>{
                throw error;
            };
            return $return.call(iterator).then(rethrow, rethrow);
        };
    }
    function mapResult(result) {
        if (result.done) {
            return onEndWithValue ? onEndWithValue(result) : result;
        }
        return handleMaybePromise(()=>result.value, (value)=>handleMaybePromise(()=>onNext(value), iteratorResult, abruptClose));
    }
    let mapReject;
    if (onError) {
        let onErrorResult;
        // Capture rejectCallback to ensure it cannot be null.
        const reject = onError;
        mapReject = (error)=>{
            onErrorResult ||= handleMaybePromise(()=>error, (error)=>handleMaybePromise(()=>reject(error), iteratorResult, abruptClose));
            return onErrorResult;
        };
    }
    return {
        next () {
            return iterator.next().then(mapResult, mapReject);
        },
        return () {
            const res$ = $return ? $return.call(iterator).then(mapResult, mapReject) : fakePromise({
                value: undefined,
                done: true
            });
            return onEndWithValue ? res$.then(onEndWithValue) : res$;
        },
        throw (error) {
            if (typeof iterator.throw === 'function') {
                return iterator.throw(error).then(mapResult, mapReject);
            }
            if (abruptClose) {
                return abruptClose(error);
            }
            return fakeRejectPromise(error);
        },
        [Symbol.asyncIterator] () {
            return this;
        }
    };
}
function iteratorResult(value) {
    return {
        value,
        done: false
    };
}
function isFakePromise(value) {
    return value?.[kFakePromise] === 'resolved';
}
function isFakeRejectPromise(value) {
    return value?.[kFakePromise] === 'rejected';
}
function promiseLikeFinally(value, onFinally) {
    if ('finally' in value) {
        return value.finally(onFinally);
    }
    return value.then((res)=>{
        const finallyRes = onFinally();
        return isPromise(finallyRes) ? finallyRes.then(()=>res) : res;
    }, (err)=>{
        const finallyRes = onFinally();
        if (isPromise(finallyRes)) {
            return finallyRes.then(()=>{
                throw err;
            });
        } else {
            throw err;
        }
    });
}
function unfakePromise(promise) {
    if (isFakePromise(promise)) {
        return promise.__fakePromiseValue;
    }
    if (isFakeRejectPromise(promise)) {
        throw promise.__fakeRejectError;
    }
    return promise;
}
}}),
"[project]/node_modules/tslib/tslib.es6.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */ /* global Reflect, Promise, SuppressedError, Symbol, Iterator */ __turbopack_context__.s({
    "__addDisposableResource": (()=>__addDisposableResource),
    "__assign": (()=>__assign),
    "__asyncDelegator": (()=>__asyncDelegator),
    "__asyncGenerator": (()=>__asyncGenerator),
    "__asyncValues": (()=>__asyncValues),
    "__await": (()=>__await),
    "__awaiter": (()=>__awaiter),
    "__classPrivateFieldGet": (()=>__classPrivateFieldGet),
    "__classPrivateFieldIn": (()=>__classPrivateFieldIn),
    "__classPrivateFieldSet": (()=>__classPrivateFieldSet),
    "__createBinding": (()=>__createBinding),
    "__decorate": (()=>__decorate),
    "__disposeResources": (()=>__disposeResources),
    "__esDecorate": (()=>__esDecorate),
    "__exportStar": (()=>__exportStar),
    "__extends": (()=>__extends),
    "__generator": (()=>__generator),
    "__importDefault": (()=>__importDefault),
    "__importStar": (()=>__importStar),
    "__makeTemplateObject": (()=>__makeTemplateObject),
    "__metadata": (()=>__metadata),
    "__param": (()=>__param),
    "__propKey": (()=>__propKey),
    "__read": (()=>__read),
    "__rest": (()=>__rest),
    "__rewriteRelativeImportExtension": (()=>__rewriteRelativeImportExtension),
    "__runInitializers": (()=>__runInitializers),
    "__setFunctionName": (()=>__setFunctionName),
    "__spread": (()=>__spread),
    "__spreadArray": (()=>__spreadArray),
    "__spreadArrays": (()=>__spreadArrays),
    "__values": (()=>__values),
    "default": (()=>__TURBOPACK__default__export__)
});
var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf || ({
        __proto__: []
    }) instanceof Array && function(d, b) {
        d.__proto__ = b;
    } || function(d, b) {
        for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
    };
    return extendStatics(d, b);
};
function __extends(d, b) {
    if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() {
        this.constructor = d;
    }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}
var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
function __rest(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
}
function __decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function __param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) {
        if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
        return f;
    }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for(var i = decorators.length - 1; i >= 0; i--){
        var context = {};
        for(var p in contextIn)context[p] = p === "access" ? {} : contextIn[p];
        for(var p in contextIn.access)context.access[p] = contextIn.access[p];
        context.addInitializer = function(f) {
            if (done) throw new TypeError("Cannot add initializers after decoration has completed");
            extraInitializers.push(accept(f || null));
        };
        var result = (0, decorators[i])(kind === "accessor" ? {
            get: descriptor.get,
            set: descriptor.set
        } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        } else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
}
;
function __runInitializers(thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for(var i = 0; i < initializers.length; i++){
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
}
;
function __propKey(x) {
    return typeof x === "symbol" ? x : "".concat(x);
}
;
function __setFunctionName(f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", {
        configurable: true,
        value: prefix ? "".concat(prefix, " ", name) : name
    });
}
;
function __metadata(metadataKey, metadataValue) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(metadataKey, metadataValue);
}
function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}
function __generator(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}
var __createBinding = Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
};
function __exportStar(m, o) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);
}
function __values(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
function __read(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
}
function __spread() {
    for(var ar = [], i = 0; i < arguments.length; i++)ar = ar.concat(__read(arguments[i]));
    return ar;
}
function __spreadArrays() {
    for(var s = 0, i = 0, il = arguments.length; i < il; i++)s += arguments[i].length;
    for(var r = Array(s), k = 0, i = 0; i < il; i++)for(var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)r[k] = a[j];
    return r;
}
function __spreadArray(to, from, pack) {
    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
}
function __await(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
}
function __asyncGenerator(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
}
function __asyncDelegator(o) {
    var i, p;
    return i = {}, verb("next"), verb("throw", function(e) {
        throw e;
    }), verb("return"), i[Symbol.iterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function verb(n, f) {
        i[n] = o[n] ? function(v) {
            return (p = !p) ? {
                value: __await(o[n](v)),
                done: false
            } : f ? f(v) : v;
        } : f;
    }
}
function __asyncValues(o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
    }, i);
    "TURBOPACK unreachable";
    function verb(n) {
        i[n] = o[n] && function(v) {
            return new Promise(function(resolve, reject) {
                v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
        };
    }
    function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v) {
            resolve({
                value: v,
                done: d
            });
        }, reject);
    }
}
function __makeTemplateObject(cooked, raw) {
    if (Object.defineProperty) {
        Object.defineProperty(cooked, "raw", {
            value: raw
        });
    } else {
        cooked.raw = raw;
    }
    return cooked;
}
;
var __setModuleDefault = Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
};
var ownKeys = function(o) {
    ownKeys = Object.getOwnPropertyNames || function(o) {
        var ar = [];
        for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
        return ar;
    };
    return ownKeys(o);
};
function __importStar(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== "default") __createBinding(result, mod, k[i]);
    }
    __setModuleDefault(result, mod);
    return result;
}
function __importDefault(mod) {
    return mod && mod.__esModule ? mod : {
        default: mod
    };
}
function __classPrivateFieldGet(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
}
function __classPrivateFieldSet(receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;
}
function __classPrivateFieldIn(state, receiver) {
    if (receiver === null || typeof receiver !== "object" && typeof receiver !== "function") throw new TypeError("Cannot use 'in' operator on non-object");
    return typeof state === "function" ? receiver === state : state.has(receiver);
}
function __addDisposableResource(env, value, async) {
    if (value !== null && value !== void 0) {
        if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
        var dispose, inner;
        if (async) {
            if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
            dispose = value[Symbol.asyncDispose];
        }
        if (dispose === void 0) {
            if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
            dispose = value[Symbol.dispose];
            if (async) inner = dispose;
        }
        if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
        if (inner) dispose = function() {
            try {
                inner.call(this);
            } catch (e) {
                return Promise.reject(e);
            }
        };
        env.stack.push({
            value: value,
            dispose: dispose,
            async: async
        });
    } else if (async) {
        env.stack.push({
            async: true
        });
    }
    return value;
}
var _SuppressedError = typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};
function __disposeResources(env) {
    function fail(e) {
        env.error = env.hasError ? new _SuppressedError(e, env.error, "An error was suppressed during disposal.") : e;
        env.hasError = true;
    }
    var r, s = 0;
    function next() {
        while(r = env.stack.pop()){
            try {
                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);
                if (r.dispose) {
                    var result = r.dispose.call(r.value);
                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {
                        fail(e);
                        return next();
                    });
                } else s |= 1;
            } catch (e) {
                fail(e);
            }
        }
        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();
        if (env.hasError) throw env.error;
    }
    return next();
}
function __rewriteRelativeImportExtension(path, preserveJsx) {
    if (typeof path === "string" && /^\.\.?\//.test(path)) {
        return path.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i, function(m, tsx, d, ext, cm) {
            return tsx ? preserveJsx ? ".jsx" : ".js" : d && (!ext || !cm) ? m : d + ext + "." + cm.toLowerCase() + "js";
        });
    }
    return path;
}
const __TURBOPACK__default__export__ = {
    __extends,
    __assign,
    __rest,
    __decorate,
    __param,
    __esDecorate,
    __runInitializers,
    __propKey,
    __setFunctionName,
    __metadata,
    __awaiter,
    __generator,
    __createBinding,
    __exportStar,
    __values,
    __read,
    __spread,
    __spreadArrays,
    __spreadArray,
    __await,
    __asyncGenerator,
    __asyncDelegator,
    __asyncValues,
    __makeTemplateObject,
    __importStar,
    __importDefault,
    __classPrivateFieldGet,
    __classPrivateFieldSet,
    __classPrivateFieldIn,
    __addDisposableResource,
    __disposeResources,
    __rewriteRelativeImportExtension
};
}}),
"[project]/node_modules/graphql-tag/lib/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "disableExperimentalFragmentVariables": (()=>disableExperimentalFragmentVariables),
    "disableFragmentWarnings": (()=>disableFragmentWarnings),
    "enableExperimentalFragmentVariables": (()=>enableExperimentalFragmentVariables),
    "gql": (()=>gql),
    "resetCaches": (()=>resetCaches)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tslib/tslib.es6.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
;
;
var docCache = new Map();
var fragmentSourceMap = new Map();
var printFragmentWarnings = true;
var experimentalFragmentVariables = false;
function normalize(string) {
    return string.replace(/[\s,]+/g, ' ').trim();
}
function cacheKeyFromLoc(loc) {
    return normalize(loc.source.body.substring(loc.start, loc.end));
}
function processFragments(ast) {
    var seenKeys = new Set();
    var definitions = [];
    ast.definitions.forEach(function(fragmentDefinition) {
        if (fragmentDefinition.kind === 'FragmentDefinition') {
            var fragmentName = fragmentDefinition.name.value;
            var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc);
            var sourceKeySet = fragmentSourceMap.get(fragmentName);
            if (sourceKeySet && !sourceKeySet.has(sourceKey)) {
                if (printFragmentWarnings) {
                    console.warn("Warning: fragment with name " + fragmentName + " already exists.\n" + "graphql-tag enforces all fragment names across your application to be unique; read more about\n" + "this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names");
                }
            } else if (!sourceKeySet) {
                fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);
            }
            sourceKeySet.add(sourceKey);
            if (!seenKeys.has(sourceKey)) {
                seenKeys.add(sourceKey);
                definitions.push(fragmentDefinition);
            }
        } else {
            definitions.push(fragmentDefinition);
        }
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__assign"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["__assign"])({}, ast), {
        definitions: definitions
    });
}
function stripLoc(doc) {
    var workSet = new Set(doc.definitions);
    workSet.forEach(function(node) {
        if (node.loc) delete node.loc;
        Object.keys(node).forEach(function(key) {
            var value = node[key];
            if (value && typeof value === 'object') {
                workSet.add(value);
            }
        });
    });
    var loc = doc.loc;
    if (loc) {
        delete loc.startToken;
        delete loc.endToken;
    }
    return doc;
}
function parseDocument(source) {
    var cacheKey = normalize(source);
    if (!docCache.has(cacheKey)) {
        var parsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(source, {
            experimentalFragmentVariables: experimentalFragmentVariables,
            allowLegacyFragmentVariables: experimentalFragmentVariables
        });
        if (!parsed || parsed.kind !== 'Document') {
            throw new Error('Not a valid GraphQL document.');
        }
        docCache.set(cacheKey, stripLoc(processFragments(parsed)));
    }
    return docCache.get(cacheKey);
}
function gql(literals) {
    var args = [];
    for(var _i = 1; _i < arguments.length; _i++){
        args[_i - 1] = arguments[_i];
    }
    if (typeof literals === 'string') {
        literals = [
            literals
        ];
    }
    var result = literals[0];
    args.forEach(function(arg, i) {
        if (arg && arg.kind === 'Document') {
            result += arg.loc.source.body;
        } else {
            result += arg;
        }
        result += literals[i + 1];
    });
    return parseDocument(result);
}
function resetCaches() {
    docCache.clear();
    fragmentSourceMap.clear();
}
function disableFragmentWarnings() {
    printFragmentWarnings = false;
}
function enableExperimentalFragmentVariables() {
    experimentalFragmentVariables = true;
}
function disableExperimentalFragmentVariables() {
    experimentalFragmentVariables = false;
}
var extras = {
    gql: gql,
    resetCaches: resetCaches,
    disableFragmentWarnings: disableFragmentWarnings,
    enableExperimentalFragmentVariables: enableExperimentalFragmentVariables,
    disableExperimentalFragmentVariables: disableExperimentalFragmentVariables
};
(function(gql_1) {
    gql_1.gql = extras.gql, gql_1.resetCaches = extras.resetCaches, gql_1.disableFragmentWarnings = extras.disableFragmentWarnings, gql_1.enableExperimentalFragmentVariables = extras.enableExperimentalFragmentVariables, gql_1.disableExperimentalFragmentVariables = extras.disableExperimentalFragmentVariables;
})(gql || (gql = {}));
gql["default"] = gql;
const __TURBOPACK__default__export__ = gql;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/jwt-decode/build/esm/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InvalidTokenError": (()=>InvalidTokenError),
    "jwtDecode": (()=>jwtDecode)
});
class InvalidTokenError extends Error {
}
InvalidTokenError.prototype.name = "InvalidTokenError";
function b64DecodeUnicode(str) {
    return decodeURIComponent(atob(str).replace(/(.)/g, (m, p)=>{
        let code = p.charCodeAt(0).toString(16).toUpperCase();
        if (code.length < 2) {
            code = "0" + code;
        }
        return "%" + code;
    }));
}
function base64UrlDecode(str) {
    let output = str.replace(/-/g, "+").replace(/_/g, "/");
    switch(output.length % 4){
        case 0:
            break;
        case 2:
            output += "==";
            break;
        case 3:
            output += "=";
            break;
        default:
            throw new Error("base64 string is not of the correct length");
    }
    try {
        return b64DecodeUnicode(output);
    } catch (err) {
        return atob(output);
    }
}
function jwtDecode(token, options) {
    if (typeof token !== "string") {
        throw new InvalidTokenError("Invalid token specified: must be a string");
    }
    options || (options = {});
    const pos = options.header === true ? 0 : 1;
    const part = token.split(".")[pos];
    if (typeof part !== "string") {
        throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);
    }
    let decoded;
    try {
        decoded = base64UrlDecode(part);
    } catch (e) {
        throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);
    }
    try {
        return JSON.parse(decoded);
    } catch (e) {
        throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);
    }
}
}}),

};

//# sourceMappingURL=node_modules_0e6e0242._.js.map