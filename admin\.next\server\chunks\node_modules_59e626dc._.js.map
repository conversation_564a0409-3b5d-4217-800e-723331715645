{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "file": "packageVersion.js", "sourceRoot": "", "sources": ["../../../src/generated/packageVersion.ts"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc,GAAG,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "file": "schemaReporter.js", "sourceRoot": "", "sources": ["../../../../src/plugin/schemaReporting/schemaReporter.ts"], "names": [], "mappings": ";;;;AAAA,OAAO,KAAK,MAAM,YAAY,CAAC;AAU/B,OAAO,EAAE,cAAc,EAAE,MAAM,mCAAmC,CAAC;;;AAI5D,MAAM,eAAe,GAAiB,CAAA;;;;;;;;;;;;;;CAc5C,CAAC;AAGI,MAAO,cAAc;IAczB,YAAY,OASX,CAAA;QACC,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,OAAO,CAAC,MAAM;YAC3B,2BAA2B,EAAE,mCAAmC;YAChE,8BAA8B,qLAAE,iBAAc;SAC/C,CAAC;QAEF,IAAI,CAAC,WAAW,GACd,OAAO,CAAC,WAAW,IACnB,4DAA4D,CAAC;QAE/D,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,CAAC,0BAA0B,GAAG,OAAO,CAAC,0BAA0B,CAAC;QACrE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,qJAAI,UAAK,CAAC;IAC1C,CAAC;IAEM,OAAO,GAAA;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,KAAK,GAAA;QACV,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAC9C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;IACJ,CAAC;IAEM,IAAI,GAAA;QACT,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,sBAA+B,EAAA;QACxE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAG3B,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,MAAM,CAAC,cAAc,CAAC,EAC9D,MAAM,CAAC,SAAS,GAAG,IAAI,CACxB,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YAIf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,CAAA,+DAAA,EAAkE,KAAK,EAAE,CAC1E,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAC9C,IAAI,CAAC,0BAA0B,CAChC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,YAAY,CACvB,cAAuB,EAAA;QAEvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC;YAC9C,MAAM,EAAE,IAAI,CAAC,YAAY;YACzB,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI;SACpD,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,CAAG,CAAD,AAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAS;YACzC,OAAO;gBACL,4CAA4C;gBAC5C,mDAAmD;gBACnD,mCAAmC;gBACnC,oBAAoB;gBACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACrB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACd,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,sBAAsB,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,mBAAmB,EAAE,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf;gBACE,8CAA8C;gBAC9C,IAAI,CAAC,YAAY,CAAC,OAAO;gBACzB,kDAAkD;aACnD,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;YACF,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,SAAwC,EAAA;QAExC,MAAM,OAAO,GAAmB;YAC9B,KAAK,EAAE,eAAe;YACtB,SAAS;SACV,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YACxD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CACb;gBACE,CAAA,gCAAA,EAAmC,YAAY,CAAC,MAAM,CAAA,KAAA,CAAO;gBAC7D,sCAAsC;aACvC,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAGH,OAAO,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb;gBACE,mCAAmC;gBACnC,kCAAkC;gBAClC,iEAAiE;gBACjE,KAAK;aACN,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;CACF", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "file": "schemaIsSubgraph.js", "sourceRoot": "", "sources": ["../../../src/plugin/schemaIsSubgraph.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAEL,YAAY,EACZ,YAAY,EACZ,aAAa,GACd,MAAM,SAAS,CAAC;;AAiBX,SAAU,gBAAgB,CAAC,MAAqB;IACpD,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC/C,IAAI,sJAAC,eAAA,AAAY,EAAC,WAAW,CAAC,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,QAAQ,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC;IACjC,IAAI,qKAAA,AAAa,EAAC,YAAY,CAAC,EAAE,CAAC;QAChC,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC;IACrC,CAAC;IACD,IAAI,sJAAC,eAAA,AAAY,EAAC,YAAY,CAAC,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,YAAY,CAAC,IAAI,IAAI,QAAQ,CAAC;AACvC,CAAC", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plugin/schemaReporting/index.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;AACpC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;;;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAI1D,OAAO,EAAE,cAAc,EAAE,MAAM,mCAAmC,CAAC;AACnE,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;;;;;;;;;AAkDvE,SAAU,iCAAiC,CAC/C,EACE,iBAAiB,EACjB,sBAAsB,EACtB,WAAW,EACX,OAAO,EAAA,GACqC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEjE,MAAM,MAAM,2LAAG,MAAA,AAAM,EAAE,CAAC;IAExB,iLAAO,iBAAc,AAAd,EAAe;QACpB,sBAAsB,EAAE,iBAAiB;QACzC,sBAAsB,EAAE,KAAK;QAC7B,KAAK,CAAC,eAAe,EAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;YAC9C,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YACjC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,KAAK,CACT,2EAA2E,GACzE,wFAAwF,CAC3F,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAGd,MAAM,KAAK,CACT,iFAAiF,GAC/E,gFAAgF,GAChF,+DAA+D,CAClE,CAAC;YACJ,CAAC;YAGD,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,gBAAgB,sJAAG,iBAAA,AAAc,gKACrC,cAAA,AAAW,EAAC,sBAAsB,EAAE;wBAAE,UAAU,EAAE,IAAI;oBAAA,CAAE,CAAC,CAC1D,CAAC;oBACF,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,KAAK,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAG,CAAD,IAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1D,CAAC;oBACJ,CAAC;gBACH,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CACb,mEAAmE,GACjE,CAAA,UAAA,EAAc,GAAa,CAAC,OAAO,EAAE,CACxC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,0LAAI,mBAAA,AAAgB,EAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,MAAM,KAAK,CACT;oBACE,0EAA0E;oBAC1E,gEAAgE;oBAChE,kEAAkE;oBAClE,4CAA4C;iBAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CACT,CAAA,wDAAA,EAA2D,WAAW,EAAE,CACzE,CAAC;YACJ,CAAC;YAED,MAAM,gBAAgB,GAAyC;gBAC7D,MAAM;gBACN,QAAQ;gBAGR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO;gBACvD,cAAc,EAAE,CAAA,KAAA,EAAQ,OAAO,CAAC,OAAO,EAAE;gBAGzC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B;gBAEnD,QAAQ,EACN,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,wGAAE,CAAC,QAAQ,EAAE;gBACvE,cAAc,EAAE,CAAA,eAAA,qLAAkB,iBAAc,EAAE;aACnD,CAAC;YACF,IAAI,qBAAiD,CAAC;YAEtD,OAAO;gBACL,qBAAqB,EAAC,EAAE,SAAS,EAAE,iBAAiB,EAAE;oBACpD,IAAI,sBAAsB,KAAK,SAAS,EAAE,CAAC;wBACzC,IAAI,qBAAqB,EAAE,CAAC;4BAG1B,OAAO;wBACT,CAAC,MAAM,CAAC;4BACN,MAAM,CAAC,IAAI,CACT,+DAA+D,CAChE,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,MAAM,UAAU,GACd,sBAAsB,IACtB,iBAAiB,+JACjB,cAAA,AAAW,EAAC,SAAS,CAAC,CAAC;oBACzB,MAAM,cAAc,IAAG,iNAAA,AAAqB,EAAC,UAAU,CAAC,CAAC;oBACzD,MAAM,YAAY,GAAiB;wBACjC,GAAG,gBAAgB;wBACnB,cAAc;qBACf,CAAC;oBAEF,qBAAqB,EAAE,IAAI,EAAE,CAAC;oBAC9B,qBAAqB,GAAG,sMAAI,kBAAc,CAAC;wBACzC,YAAY;wBACZ,UAAU;wBACV,MAAM,EAAE,GAAG;wBACX,WAAW;wBACX,MAAM;wBAEN,yBAAyB,EAAE,IAAI,CAAC,KAAK,CACnC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,iBAAiB,IAAI,KAAM,CAAC,CAC9C;wBACD,0BAA0B,EAAE,KAAM;wBAClC,OAAO;qBACR,CAAC,CAAC;oBACH,qBAAqB,CAAC,KAAK,EAAE,CAAC;oBAE9B,MAAM,CAAC,IAAI,CACT,+EAA+E,GAC7E,CAAA,uCAAA,EAA0C,SAAS,CACjD,QAAQ,CACT,CAAA,mBAAA,EAAsB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CACxD,CAAC;gBACJ,CAAC;gBACD,KAAK,CAAC,cAAc;oBAClB,qBAAqB,EAAE,IAAI,EAAE,CAAC;gBAChC,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "debugId": null}}]}