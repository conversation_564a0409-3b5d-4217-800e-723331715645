module.exports = {

"[project]/node_modules/graphql/index.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_3640ffe9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_a9106fd4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_59e626dc._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_@apollo_cde87416._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_e9bdf046._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_@apollo_server_dist_esm_plugin_disableSuggestions_index_71cbddd1.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript)");
    });
});
}}),

};