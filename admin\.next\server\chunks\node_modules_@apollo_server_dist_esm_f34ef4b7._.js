module.exports = {

"[project]/node_modules/@apollo/server/dist/esm/utils/resolvable.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = ()=>{
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject)=>{
        resolve = _resolve;
        reject = _reject;
    });
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
};
 //# sourceMappingURL=resolvable.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/cachePolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "newCachePolicy": (()=>newCachePolicy)
});
function newCachePolicy() {
    return {
        maxAge: undefined,
        scope: undefined,
        restrict (hint) {
            if (hint.maxAge !== undefined && (this.maxAge === undefined || hint.maxAge < this.maxAge)) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined && this.scope !== 'PRIVATE') {
                this.scope = hint.scope;
            }
        },
        replace (hint) {
            if (hint.maxAge !== undefined) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined) {
                this.scope = hint.scope;
            }
        },
        policyIfCacheable () {
            if (this.maxAge === undefined || this.maxAge === 0) {
                return null;
            }
            return {
                maxAge: this.maxAge,
                scope: this.scope ?? 'PUBLIC'
            };
        }
    };
} //# sourceMappingURL=cachePolicy.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/determineApolloConfig.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "determineApolloConfig": (()=>determineApolloConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
;
function determineApolloConfig(input, logger) {
    const apolloConfig = {};
    const { APOLLO_KEY, APOLLO_GRAPH_REF, APOLLO_GRAPH_ID, APOLLO_GRAPH_VARIANT } = process.env;
    if (input?.key) {
        apolloConfig.key = input.key.trim();
    } else if (APOLLO_KEY) {
        apolloConfig.key = APOLLO_KEY.trim();
    }
    if ((input?.key ?? APOLLO_KEY) !== apolloConfig.key) {
        logger.warn('The provided API key has unexpected leading or trailing whitespace. ' + 'Apollo Server will trim the key value before use.');
    }
    if (apolloConfig.key) {
        assertValidHeaderValue(apolloConfig.key);
    }
    if (apolloConfig.key) {
        apolloConfig.keyHash = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha512').update(apolloConfig.key).digest('hex');
    }
    if (input?.graphRef) {
        apolloConfig.graphRef = input.graphRef;
    } else if (APOLLO_GRAPH_REF) {
        apolloConfig.graphRef = APOLLO_GRAPH_REF;
    }
    const graphId = input?.graphId ?? APOLLO_GRAPH_ID;
    const graphVariant = input?.graphVariant ?? APOLLO_GRAPH_VARIANT;
    if (apolloConfig.graphRef) {
        if (graphId) {
            throw new Error('Cannot specify both graph ref and graph ID. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph ID.');
        }
        if (graphVariant) {
            throw new Error('Cannot specify both graph ref and graph variant. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph variant.');
        }
    } else if (graphId) {
        apolloConfig.graphRef = graphVariant ? `${graphId}@${graphVariant}` : graphId;
    }
    return apolloConfig;
}
function assertValidHeaderValue(value) {
    const invalidHeaderCharRegex = /[^\t\x20-\x7e\x80-\xff]/g;
    if (invalidHeaderCharRegex.test(value)) {
        const invalidChars = value.match(invalidHeaderCharRegex);
        throw new Error(`The API key provided to Apollo Server contains characters which are invalid as HTTP header values. The following characters found in the key are invalid: ${invalidChars.join(', ')}. Valid header values may only contain ASCII visible characters. If you think there is an issue with your key, please contact Apollo support.`);
    }
} //# sourceMappingURL=determineApolloConfig.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApolloServerErrorCode": (()=>ApolloServerErrorCode),
    "ApolloServerValidationErrorCode": (()=>ApolloServerValidationErrorCode),
    "unwrapResolverError": (()=>unwrapResolverError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
;
var ApolloServerErrorCode;
(function(ApolloServerErrorCode) {
    ApolloServerErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ApolloServerErrorCode["GRAPHQL_PARSE_FAILED"] = "GRAPHQL_PARSE_FAILED";
    ApolloServerErrorCode["GRAPHQL_VALIDATION_FAILED"] = "GRAPHQL_VALIDATION_FAILED";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_FOUND"] = "PERSISTED_QUERY_NOT_FOUND";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_SUPPORTED"] = "PERSISTED_QUERY_NOT_SUPPORTED";
    ApolloServerErrorCode["BAD_USER_INPUT"] = "BAD_USER_INPUT";
    ApolloServerErrorCode["OPERATION_RESOLUTION_FAILURE"] = "OPERATION_RESOLUTION_FAILURE";
    ApolloServerErrorCode["BAD_REQUEST"] = "BAD_REQUEST";
})(ApolloServerErrorCode || (ApolloServerErrorCode = {}));
var ApolloServerValidationErrorCode;
(function(ApolloServerValidationErrorCode) {
    ApolloServerValidationErrorCode["INTROSPECTION_DISABLED"] = "INTROSPECTION_DISABLED";
    ApolloServerValidationErrorCode["MAX_RECURSIVE_SELECTIONS_EXCEEDED"] = "MAX_RECURSIVE_SELECTIONS_EXCEEDED";
})(ApolloServerValidationErrorCode || (ApolloServerValidationErrorCode = {}));
function unwrapResolverError(error) {
    if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] && error.path && error.originalError) {
        return error.originalError;
    }
    return error;
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HeaderMap": (()=>HeaderMap)
});
class HeaderMap extends Map {
    constructor(){
        super(...arguments);
        this.__identity = Symbol('HeaderMap');
    }
    set(key, value) {
        return super.set(key.toLowerCase(), value);
    }
    get(key) {
        return super.get(key.toLowerCase());
    }
    delete(key) {
        return super.delete(key.toLowerCase());
    }
    has(key) {
        return super.has(key.toLowerCase());
    }
} //# sourceMappingURL=HeaderMap.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BadRequestError": (()=>BadRequestError),
    "OperationResolutionError": (()=>OperationResolutionError),
    "PersistedQueryNotFoundError": (()=>PersistedQueryNotFoundError),
    "PersistedQueryNotSupportedError": (()=>PersistedQueryNotSupportedError),
    "SyntaxError": (()=>SyntaxError),
    "UserInputError": (()=>UserInputError),
    "ValidationError": (()=>ValidationError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
class GraphQLErrorWithCode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] {
    constructor(message, code, options){
        super(message, {
            ...options,
            extensions: {
                ...options?.extensions,
                code
            }
        });
        this.name = this.constructor.name;
    }
}
class SyntaxError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].GRAPHQL_PARSE_FAILED, {
            source: graphqlError.source,
            positions: graphqlError.positions,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError
        });
    }
}
class ValidationError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].GRAPHQL_VALIDATION_FAILED, {
            nodes: graphqlError.nodes,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError.originalError ?? graphqlError
        });
    }
}
const getPersistedQueryErrorHttp = ()=>({
        status: 200,
        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
            [
                'cache-control',
                'private, no-cache, must-revalidate'
            ]
        ])
    });
class PersistedQueryNotFoundError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotFound', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].PERSISTED_QUERY_NOT_FOUND, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
class PersistedQueryNotSupportedError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotSupported', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].PERSISTED_QUERY_NOT_SUPPORTED, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
class UserInputError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_USER_INPUT, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: graphqlError.extensions
        });
    }
}
class OperationResolutionError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].OPERATION_RESOLUTION_FAILURE, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            }
        });
    }
}
class BadRequestError extends GraphQLErrorWithCode {
    constructor(message, options){
        super(message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_REQUEST, {
            ...options,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...options?.extensions
            }
        });
    }
} //# sourceMappingURL=internalErrorClasses.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeHTTPGraphQLHead": (()=>mergeHTTPGraphQLHead),
    "newHTTPGraphQLHead": (()=>newHTTPGraphQLHead),
    "prettyJSONStringify": (()=>prettyJSONStringify),
    "runHttpQuery": (()=>runHttpQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
;
function fieldIfString(o, fieldName) {
    const value = o[fieldName];
    if (typeof value === 'string') {
        return value;
    }
    return undefined;
}
function searchParamIfSpecifiedOnce(searchParams, paramName) {
    const values = searchParams.getAll(paramName);
    switch(values.length){
        case 0:
            return undefined;
        case 1:
            return values[0];
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The '${paramName}' search parameter may only be specified once.`);
    }
}
function jsonParsedSearchParamIfSpecifiedOnce(searchParams, fieldName) {
    const value = searchParamIfSpecifiedOnce(searchParams, fieldName);
    if (value === undefined) {
        return undefined;
    }
    let hopefullyRecord;
    try {
        hopefullyRecord = JSON.parse(value);
    } catch  {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The ${fieldName} search parameter contains invalid JSON.`);
    }
    if (!isStringRecord(hopefullyRecord)) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The ${fieldName} search parameter should contain a JSON-encoded object.`);
    }
    return hopefullyRecord;
}
function fieldIfRecord(o, fieldName) {
    const value = o[fieldName];
    if (isStringRecord(value)) {
        return value;
    }
    return undefined;
}
function isStringRecord(o) {
    return !!o && typeof o === 'object' && !Buffer.isBuffer(o) && !Array.isArray(o);
}
function isNonEmptyStringRecord(o) {
    return isStringRecord(o) && Object.keys(o).length > 0;
}
function ensureQueryIsStringOrMissing(query) {
    if (!query || typeof query === 'string') {
        return;
    }
    if (query.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]("GraphQL queries must be strings. It looks like you're sending the " + 'internal graphql-js representation of a parsed query in your ' + 'request instead of a request in the GraphQL query language. You ' + 'can convert an AST to a string using the `print` function from ' + '`graphql`, or use a client like `apollo-client` which converts ' + 'the internal representation to a string for you.');
    } else {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('GraphQL queries must be strings.');
    }
}
async function runHttpQuery({ server, httpRequest, contextValue, schemaDerivedData, internals, sharedResponseHTTPGraphQLHead }) {
    let graphQLRequest;
    switch(httpRequest.method){
        case 'POST':
            {
                if (!isNonEmptyStringRecord(httpRequest.body)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('POST body missing, invalid Content-Type, or JSON object has no keys.');
                }
                ensureQueryIsStringOrMissing(httpRequest.body.query);
                if (typeof httpRequest.body.variables === 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`variables` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if (typeof httpRequest.body.extensions === 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`extensions` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if ('extensions' in httpRequest.body && httpRequest.body.extensions !== null && !isStringRecord(httpRequest.body.extensions)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`extensions` in a POST body must be an object if provided.');
                }
                if ('variables' in httpRequest.body && httpRequest.body.variables !== null && !isStringRecord(httpRequest.body.variables)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`variables` in a POST body must be an object if provided.');
                }
                if ('operationName' in httpRequest.body && httpRequest.body.operationName !== null && typeof httpRequest.body.operationName !== 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`operationName` in a POST body must be a string if provided.');
                }
                graphQLRequest = {
                    query: fieldIfString(httpRequest.body, 'query'),
                    operationName: fieldIfString(httpRequest.body, 'operationName'),
                    variables: fieldIfRecord(httpRequest.body, 'variables'),
                    extensions: fieldIfRecord(httpRequest.body, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        case 'GET':
            {
                const searchParams = new URLSearchParams(httpRequest.search);
                graphQLRequest = {
                    query: searchParamIfSpecifiedOnce(searchParams, 'query'),
                    operationName: searchParamIfSpecifiedOnce(searchParams, 'operationName'),
                    variables: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'variables'),
                    extensions: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Apollo Server supports only GET/POST requests.', {
                extensions: {
                    http: {
                        status: 405,
                        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                            [
                                'allow',
                                'GET, POST'
                            ]
                        ])
                    }
                }
            });
    }
    const graphQLResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["internalExecuteOperation"])({
        server,
        graphQLRequest,
        internals,
        schemaDerivedData,
        sharedResponseHTTPGraphQLHead
    }, {
        contextValue
    });
    if (graphQLResponse.body.kind === 'single') {
        if (!graphQLResponse.http.headers.get('content-type')) {
            const contentType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chooseContentTypeForSingleResultResponse"])(httpRequest);
            if (contentType === null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`An 'accept' header was provided for this request which does not accept ` + `${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].APPLICATION_JSON} or ${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].APPLICATION_GRAPHQL_RESPONSE_JSON}`, {
                    extensions: {
                        http: {
                            status: 406
                        }
                    }
                });
            }
            graphQLResponse.http.headers.set('content-type', contentType);
        }
        return {
            ...graphQLResponse.http,
            body: {
                kind: 'complete',
                string: await internals.stringifyResult(orderExecutionResultFields(graphQLResponse.body.singleResult))
            }
        };
    }
    const acceptHeader = httpRequest.headers.get('accept');
    if (!(acceptHeader && new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
        headers: {
            accept: httpRequest.headers.get('accept')
        }
    }).mediaType([
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_NO_DEFER_SPEC,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_EXPERIMENTAL
    ]) === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_EXPERIMENTAL)) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Apollo server received an operation that uses incremental delivery ' + '(@defer or @stream), but the client does not accept multipart/mixed ' + 'HTTP responses. To enable incremental delivery support, add the HTTP ' + "header 'Accept: multipart/mixed; deferSpec=20220824'.", {
            extensions: {
                http: {
                    status: 406
                }
            }
        });
    }
    graphQLResponse.http.headers.set('content-type', 'multipart/mixed; boundary="-"; deferSpec=20220824');
    return {
        ...graphQLResponse.http,
        body: {
            kind: 'chunked',
            asyncIterator: writeMultipartBody(graphQLResponse.body.initialResult, graphQLResponse.body.subsequentResults)
        }
    };
}
async function* writeMultipartBody(initialResult, subsequentResults) {
    yield `\r\n---\r\ncontent-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderInitialIncrementalExecutionResultFields(initialResult))}\r\n---${initialResult.hasNext ? '' : '--'}\r\n`;
    for await (const result of subsequentResults){
        yield `content-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderSubsequentIncrementalExecutionResultFields(result))}\r\n---${result.hasNext ? '' : '--'}\r\n`;
    }
}
function orderExecutionResultFields(result) {
    return {
        errors: result.errors,
        data: result.data,
        extensions: result.extensions
    };
}
function orderInitialIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        errors: result.errors,
        data: result.data,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderSubsequentIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderIncrementalResultFields(incremental) {
    return incremental?.map((i)=>({
            hasNext: i.hasNext,
            errors: i.errors,
            path: i.path,
            label: i.label,
            data: i.data,
            items: i.items,
            extensions: i.extensions
        }));
}
function prettyJSONStringify(value) {
    return JSON.stringify(value) + '\n';
}
function newHTTPGraphQLHead(status) {
    return {
        status,
        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]()
    };
}
function mergeHTTPGraphQLHead(target, source) {
    if (source.status) {
        target.status = source.status;
    }
    if (source.headers) {
        for (const [name, value] of source.headers){
            target.headers.set(name, value);
        }
    }
} //# sourceMappingURL=runHttpQuery.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureError": (()=>ensureError),
    "ensureGraphQLError": (()=>ensureGraphQLError),
    "normalizeAndFormatErrors": (()=>normalizeAndFormatErrors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
function normalizeAndFormatErrors(errors, options = {}) {
    const formatError = options.formatError ?? ((error)=>error);
    const httpFromErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])();
    return {
        httpFromErrors,
        formattedErrors: errors.map((error)=>{
            try {
                return formatError(enrichError(error), error);
            } catch (formattingError) {
                if (options.includeStacktraceInErrorResponses) {
                    return enrichError(formattingError);
                } else {
                    return {
                        message: 'Internal server error',
                        extensions: {
                            code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].INTERNAL_SERVER_ERROR
                        }
                    };
                }
            }
        })
    };
    "TURBOPACK unreachable";
    function enrichError(maybeError) {
        const graphqlError = ensureGraphQLError(maybeError);
        const extensions = {
            ...graphqlError.extensions,
            code: graphqlError.extensions.code ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].INTERNAL_SERVER_ERROR
        };
        if (isPartialHTTPGraphQLHead(extensions.http)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(httpFromErrors, {
                headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"](),
                ...extensions.http
            });
            delete extensions.http;
        }
        if (options.includeStacktraceInErrorResponses) {
            extensions.stacktrace = graphqlError.stack?.split('\n');
        }
        return {
            ...graphqlError.toJSON(),
            extensions
        };
    }
}
function ensureError(maybeError) {
    return maybeError instanceof Error ? maybeError : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('Unexpected error value: ' + String(maybeError));
}
function ensureGraphQLError(maybeError, messagePrefixIfNotGraphQLError = '') {
    const error = ensureError(maybeError);
    return error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] ? error : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](messagePrefixIfNotGraphQLError + error.message, {
        originalError: error
    });
}
function isPartialHTTPGraphQLHead(x) {
    return !!x && typeof x === 'object' && (!('status' in x) || typeof x.status === 'number') && (!('headers' in x) || x.headers instanceof Map);
} //# sourceMappingURL=errorNormalize.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/httpBatching.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "runPotentiallyBatchedHttpQuery": (()=>runPotentiallyBatchedHttpQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
;
;
async function runBatchedHttpQuery({ server, batchRequest, body, contextValue, schemaDerivedData, internals }) {
    if (body.length === 0) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('No operations found in request.');
    }
    const sharedResponseHTTPGraphQLHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])();
    const responseBodies = await Promise.all(body.map(async (bodyPiece)=>{
        const singleRequest = {
            ...batchRequest,
            body: bodyPiece
        };
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runHttpQuery"])({
            server,
            httpRequest: singleRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead
        });
        if (response.body.kind === 'chunked') {
            throw Error('Incremental delivery is not implemented for batch requests');
        }
        return response.body.string;
    }));
    return {
        ...sharedResponseHTTPGraphQLHead,
        body: {
            kind: 'complete',
            string: `[${responseBodies.join(',')}]`
        }
    };
}
async function runPotentiallyBatchedHttpQuery(server, httpGraphQLRequest, contextValue, schemaDerivedData, internals) {
    if (!(httpGraphQLRequest.method === 'POST' && Array.isArray(httpGraphQLRequest.body))) {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runHttpQuery"])({
            server,
            httpRequest: httpGraphQLRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead: null
        });
    }
    if (internals.allowBatchedHttpRequests) {
        return await runBatchedHttpQuery({
            server,
            batchRequest: httpGraphQLRequest,
            body: httpGraphQLRequest.body,
            contextValue,
            schemaDerivedData,
            internals
        });
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Operation batching disabled.');
} //# sourceMappingURL=httpBatching.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/internalPlugin.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "internalPlugin": (()=>internalPlugin),
    "pluginIsInternal": (()=>pluginIsInternal)
});
function internalPlugin(p) {
    return p;
}
function pluginIsInternal(plugin) {
    return '__internal_plugin_id__' in plugin;
} //# sourceMappingURL=internalPlugin.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/preventCsrf.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "preventCsrf": (()=>preventCsrf),
    "recommendedCsrfPreventionRequestHeaders": (()=>recommendedCsrfPreventionRequestHeaders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$mimetype$2f$lib$2f$mime$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/whatwg-mimetype/lib/mime-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
;
;
const recommendedCsrfPreventionRequestHeaders = [
    'x-apollo-operation-name',
    'apollo-require-preflight'
];
const NON_PREFLIGHTED_CONTENT_TYPES = [
    'application/x-www-form-urlencoded',
    'multipart/form-data',
    'text/plain'
];
function preventCsrf(headers, csrfPreventionRequestHeaders) {
    const contentType = headers.get('content-type');
    if (contentType !== undefined) {
        const contentTypeParsed = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$mimetype$2f$lib$2f$mime$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parse(contentType);
        if (contentTypeParsed === null) {
            return;
        }
        if (!NON_PREFLIGHTED_CONTENT_TYPES.includes(contentTypeParsed.essence)) {
            return;
        }
    }
    if (csrfPreventionRequestHeaders.some((header)=>{
        const value = headers.get(header);
        return value !== undefined && value.length > 0;
    })) {
        return;
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`This operation has been blocked as a potential Cross-Site Request Forgery ` + `(CSRF). Please either specify a 'content-type' header (with a type that ` + `is not one of ${NON_PREFLIGHTED_CONTENT_TYPES.join(', ')}) or provide ` + `a non-empty value for one of the following headers: ${csrfPreventionRequestHeaders.join(', ')}\n`);
} //# sourceMappingURL=preventCsrf.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/schemaInstrumentation.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "enablePluginsForSchemaResolvers": (()=>enablePluginsForSchemaResolvers),
    "pluginsEnabledForSchemaResolvers": (()=>pluginsEnabledForSchemaResolvers),
    "symbolExecutionDispatcherWillResolveField": (()=>symbolExecutionDispatcherWillResolveField),
    "symbolUserFieldResolver": (()=>symbolUserFieldResolver),
    "whenResultIsFinished": (()=>whenResultIsFinished)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/execution/execute.mjs [app-route] (ecmascript)");
;
const symbolExecutionDispatcherWillResolveField = Symbol('apolloServerExecutionDispatcherWillResolveField');
const symbolUserFieldResolver = Symbol('apolloServerUserFieldResolver');
const symbolPluginsEnabled = Symbol('apolloServerPluginsEnabled');
function enablePluginsForSchemaResolvers(schema) {
    if (pluginsEnabledForSchemaResolvers(schema)) {
        return schema;
    }
    Object.defineProperty(schema, symbolPluginsEnabled, {
        value: true
    });
    const typeMap = schema.getTypeMap();
    Object.values(typeMap).forEach((type)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(type).name.startsWith('__') && type instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"]) {
            const fields = type.getFields();
            Object.values(fields).forEach((field)=>{
                wrapField(field);
            });
        }
    });
    return schema;
}
function pluginsEnabledForSchemaResolvers(schema) {
    return !!schema[symbolPluginsEnabled];
}
function wrapField(field) {
    const originalFieldResolve = field.resolve;
    field.resolve = (source, args, contextValue, info)=>{
        const willResolveField = contextValue?.[symbolExecutionDispatcherWillResolveField];
        const userFieldResolver = contextValue?.[symbolUserFieldResolver];
        const didResolveField = typeof willResolveField === 'function' && willResolveField({
            source,
            args,
            contextValue,
            info
        });
        const fieldResolver = originalFieldResolve || userFieldResolver || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultFieldResolver"];
        try {
            const result = fieldResolver(source, args, contextValue, info);
            if (typeof didResolveField === 'function') {
                whenResultIsFinished(result, didResolveField);
            }
            return result;
        } catch (error) {
            if (typeof didResolveField === 'function') {
                didResolveField(error);
            }
            throw error;
        }
    };
}
function isPromise(x) {
    return x && typeof x.then === 'function';
}
function whenResultIsFinished(result, callback) {
    if (isPromise(result)) {
        result.then((r)=>whenResultIsFinished(r, callback), (err)=>callback(err));
    } else if (Array.isArray(result)) {
        if (result.some(isPromise)) {
            Promise.all(result).then((r)=>callback(null, r), (err)=>callback(err));
        } else {
            callback(null, result);
        }
    } else {
        callback(null, result);
    }
} //# sourceMappingURL=schemaInstrumentation.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isDefined": (()=>isDefined)
});
function isDefined(t) {
    return t != null;
} //# sourceMappingURL=isDefined.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/invokeHooks.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "invokeDidStartHook": (()=>invokeDidStartHook),
    "invokeHooksUntilDefinedAndNonNull": (()=>invokeHooksUntilDefinedAndNonNull),
    "invokeSyncDidStartHook": (()=>invokeSyncDidStartHook)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
;
async function invokeDidStartHook(targets, hook) {
    const didEndHooks = (await Promise.all(targets.map((target)=>hook(target)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    didEndHooks.reverse();
    return async (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
function invokeSyncDidStartHook(targets, hook) {
    const didEndHooks = targets.map((target)=>hook(target)).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    didEndHooks.reverse();
    return (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
async function invokeHooksUntilDefinedAndNonNull(targets, hook) {
    for (const target of targets){
        const value = await hook(target);
        if (value != null) {
            return value;
        }
    }
    return null;
} //# sourceMappingURL=invokeHooks.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makeGatewayGraphQLRequestContext": (()=>makeGatewayGraphQLRequestContext)
});
function makeGatewayGraphQLRequestContext(as4RequestContext, server, internals) {
    const request = {};
    if ('query' in as4RequestContext.request) {
        request.query = as4RequestContext.request.query;
    }
    if ('operationName' in as4RequestContext.request) {
        request.operationName = as4RequestContext.request.operationName;
    }
    if ('variables' in as4RequestContext.request) {
        request.variables = as4RequestContext.request.variables;
    }
    if ('extensions' in as4RequestContext.request) {
        request.extensions = as4RequestContext.request.extensions;
    }
    if (as4RequestContext.request.http) {
        const as4http = as4RequestContext.request.http;
        const needQuestion = as4http.search !== '' && !as4http.search.startsWith('?');
        request.http = {
            method: as4http.method,
            url: `https://unknown-url.invalid/${needQuestion ? '?' : ''}${as4http.search}`,
            headers: new FetcherHeadersForHeaderMap(as4http.headers)
        };
    }
    const response = {
        http: {
            headers: new FetcherHeadersForHeaderMap(as4RequestContext.response.http.headers),
            get status () {
                return as4RequestContext.response.http.status;
            },
            set status (newStatus){
                as4RequestContext.response.http.status = newStatus;
            }
        }
    };
    return {
        request,
        response,
        logger: server.logger,
        schema: as4RequestContext.schema,
        schemaHash: 'schemaHash no longer exists in Apollo Server 4',
        context: as4RequestContext.contextValue,
        cache: server.cache,
        queryHash: as4RequestContext.queryHash,
        document: as4RequestContext.document,
        source: as4RequestContext.source,
        operationName: as4RequestContext.operationName,
        operation: as4RequestContext.operation,
        errors: as4RequestContext.errors,
        metrics: as4RequestContext.metrics,
        debug: internals.includeStacktraceInErrorResponses,
        overallCachePolicy: as4RequestContext.overallCachePolicy,
        requestIsBatched: as4RequestContext.requestIsBatched
    };
}
class FetcherHeadersForHeaderMap {
    constructor(map){
        this.map = map;
    }
    append(name, value) {
        if (this.map.has(name)) {
            this.map.set(name, this.map.get(name) + ', ' + value);
        } else {
            this.map.set(name, value);
        }
    }
    delete(name) {
        this.map.delete(name);
    }
    get(name) {
        return this.map.get(name) ?? null;
    }
    has(name) {
        return this.map.has(name);
    }
    set(name, value) {
        this.map.set(name, value);
    }
    entries() {
        return this.map.entries();
    }
    keys() {
        return this.map.keys();
    }
    values() {
        return this.map.values();
    }
    [Symbol.iterator]() {
        return this.map.entries();
    }
} //# sourceMappingURL=makeGatewayGraphQLRequestContext.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/incrementalDeliveryPolyfill.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "executeIncrementally": (()=>executeIncrementally)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/execution/execute.mjs [app-route] (ecmascript)");
;
let graphqlExperimentalExecuteIncrementally = undefined;
async function tryToLoadGraphQL17() {
    if (graphqlExperimentalExecuteIncrementally !== undefined) {
        return;
    }
    const graphql = await __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
    if ('experimentalExecuteIncrementally' in graphql) {
        graphqlExperimentalExecuteIncrementally = graphql.experimentalExecuteIncrementally;
    } else {
        graphqlExperimentalExecuteIncrementally = null;
    }
}
async function executeIncrementally(args) {
    await tryToLoadGraphQL17();
    if (graphqlExperimentalExecuteIncrementally) {
        return graphqlExperimentalExecuteIncrementally(args);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["execute"])(args);
} //# sourceMappingURL=incrementalDeliveryPolyfill.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/requestPipeline.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "APQ_CACHE_PREFIX": (()=>APQ_CACHE_PREFIX),
    "processGraphQLRequest": (()=>processGraphQLRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$specifiedRules$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/validation/specifiedRules.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$getOperationAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/getOperationAST.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/validation/validate.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/schemaInstrumentation.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/invokeHooks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$makeGatewayGraphQLRequestContext$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$incrementalDeliveryPolyfill$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/incrementalDeliveryPolyfill.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const APQ_CACHE_PREFIX = 'apq:';
function computeQueryHash(query) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update(query).digest('hex');
}
function isBadUserInputGraphQLError(error) {
    return error.nodes?.length === 1 && error.nodes[0].kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].VARIABLE_DEFINITION && (error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" got invalid value `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of required type `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of non-null type `));
}
async function processGraphQLRequest(schemaDerivedData, server, internals, requestContext) {
    const requestListeners = (await Promise.all(internals.plugins.map((p)=>p.requestDidStart?.(requestContext)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    const request = requestContext.request;
    let { query, extensions } = request;
    let queryHash;
    requestContext.metrics.persistedQueryHit = false;
    requestContext.metrics.persistedQueryRegister = false;
    if (extensions?.persistedQuery) {
        if (!internals.persistedQueries) {
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PersistedQueryNotSupportedError"]()
            ]);
        } else if (extensions.persistedQuery.version !== 1) {
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('Unsupported persisted query version', {
                    extensions: {
                        http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400)
                    }
                })
            ]);
        }
        queryHash = extensions.persistedQuery.sha256Hash;
        if (query === undefined) {
            query = await internals.persistedQueries.cache.get(queryHash);
            if (query) {
                requestContext.metrics.persistedQueryHit = true;
            } else {
                return await sendErrorResponse([
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PersistedQueryNotFoundError"]()
                ]);
            }
        } else {
            const computedQueryHash = computeQueryHash(query);
            if (queryHash !== computedQueryHash) {
                return await sendErrorResponse([
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('provided sha does not match query', {
                        extensions: {
                            http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400)
                        }
                    })
                ]);
            }
            requestContext.metrics.persistedQueryRegister = true;
        }
    } else if (query) {
        queryHash = computeQueryHash(query);
    } else {
        return await sendErrorResponse([
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('GraphQL operations must contain a non-empty `query` or a `persistedQuery` extension.')
        ]);
    }
    requestContext.queryHash = queryHash;
    requestContext.source = query;
    await Promise.all(requestListeners.map((l)=>l.didResolveSource?.(requestContext)));
    if (schemaDerivedData.documentStore) {
        try {
            requestContext.document = await schemaDerivedData.documentStore.get(schemaDerivedData.documentStoreKeyPrefix + queryHash);
        } catch (err) {
            server.logger.warn('An error occurred while attempting to read from the documentStore. ' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(err).message);
        }
    }
    if (!requestContext.document) {
        const parsingDidEnd = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeDidStartHook"])(requestListeners, async (l)=>l.parsingDidStart?.(requestContext));
        try {
            requestContext.document = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(query, internals.parseOptions);
        } catch (syntaxMaybeError) {
            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(syntaxMaybeError);
            await parsingDidEnd(error);
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SyntaxError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(error))
            ]);
        }
        await parsingDidEnd();
        if (internals.dangerouslyDisableValidation !== true) {
            const validationDidEnd = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeDidStartHook"])(requestListeners, async (l)=>l.validationDidStart?.(requestContext));
            let validationErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validate"])(schemaDerivedData.schema, requestContext.document, [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$specifiedRules$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["specifiedRules"],
                ...internals.validationRules
            ]);
            if (validationErrors.length === 0 && internals.laterValidationRules) {
                validationErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validate"])(schemaDerivedData.schema, requestContext.document, internals.laterValidationRules);
            }
            if (validationErrors.length === 0) {
                await validationDidEnd();
            } else {
                await validationDidEnd(validationErrors);
                return await sendErrorResponse(validationErrors.map((error)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ValidationError"](error)));
            }
        }
        if (schemaDerivedData.documentStore) {
            Promise.resolve(schemaDerivedData.documentStore.set(schemaDerivedData.documentStoreKeyPrefix + queryHash, requestContext.document)).catch((err)=>server.logger.warn('Could not store validated document. ' + err?.message || err));
        }
    }
    const operation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$getOperationAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOperationAST"])(requestContext.document, request.operationName);
    requestContext.operation = operation || undefined;
    requestContext.operationName = operation?.name?.value || null;
    if (request.http?.method === 'GET' && operation?.operation && operation.operation !== 'query') {
        return await sendErrorResponse([
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`GET requests only support query operations, not ${operation.operation} operations`, {
                extensions: {
                    http: {
                        status: 405,
                        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                            [
                                'allow',
                                'POST'
                            ]
                        ])
                    }
                }
            })
        ]);
    }
    try {
        await Promise.all(requestListeners.map((l)=>l.didResolveOperation?.(requestContext)));
    } catch (err) {
        return await sendErrorResponse([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(err)
        ]);
    }
    if (requestContext.metrics.persistedQueryRegister && internals.persistedQueries) {
        const ttl = internals.persistedQueries?.ttl;
        Promise.resolve(internals.persistedQueries.cache.set(queryHash, query, ttl !== undefined ? {
            ttl: internals.persistedQueries?.ttl
        } : undefined)).catch(server.logger.warn);
    }
    const responseFromPlugin = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeHooksUntilDefinedAndNonNull"])(requestListeners, async (l)=>await l.responseForOperation?.(requestContext));
    if (responseFromPlugin !== null) {
        requestContext.response.body = responseFromPlugin.body;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, responseFromPlugin.http);
    } else {
        const executionListeners = (await Promise.all(requestListeners.map((l)=>l.executionDidStart?.(requestContext)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
        executionListeners.reverse();
        if (executionListeners.some((l)=>l.willResolveField)) {
            const invokeWillResolveField = (...args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeSyncDidStartHook"])(executionListeners, (l)=>l.willResolveField?.(...args));
            Object.defineProperty(requestContext.contextValue, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["symbolExecutionDispatcherWillResolveField"], {
                value: invokeWillResolveField
            });
            if (internals.fieldResolver) {
                Object.defineProperty(requestContext.contextValue, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["symbolUserFieldResolver"], {
                    value: internals.fieldResolver
                });
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enablePluginsForSchemaResolvers"])(schemaDerivedData.schema);
        }
        try {
            const fullResult = await execute(requestContext);
            const result = 'singleResult' in fullResult ? fullResult.singleResult : fullResult.initialResult;
            if (!requestContext.operation) {
                if (!result.errors?.length) {
                    throw new Error('Unexpected error: Apollo Server did not resolve an operation but execute did not return errors');
                }
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OperationResolutionError"](result.errors[0]);
            }
            const resultErrors = result.errors?.map((e)=>{
                if (isBadUserInputGraphQLError(e) && e.extensions?.code == null) {
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserInputError"](e);
                }
                return e;
            });
            if (resultErrors) {
                await didEncounterErrors(resultErrors);
            }
            const { formattedErrors, httpFromErrors } = resultErrors ? formatErrors(resultErrors) : {
                formattedErrors: undefined,
                httpFromErrors: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])()
            };
            if (internals.status400ForVariableCoercionErrors && resultErrors?.length && result.data === undefined && !httpFromErrors.status) {
                httpFromErrors.status = 400;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, httpFromErrors);
            if ('singleResult' in fullResult) {
                requestContext.response.body = {
                    kind: 'single',
                    singleResult: {
                        ...result,
                        errors: formattedErrors
                    }
                };
            } else {
                requestContext.response.body = {
                    kind: 'incremental',
                    initialResult: {
                        ...fullResult.initialResult,
                        errors: formattedErrors
                    },
                    subsequentResults: fullResult.subsequentResults
                };
            }
        } catch (executionMaybeError) {
            const executionError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(executionMaybeError);
            await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.(executionError)));
            return await sendErrorResponse([
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(executionError)
            ]);
        }
        await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.()));
    }
    await invokeWillSendResponse();
    if (!requestContext.response.body) {
        throw Error('got to end of processGraphQLRequest without setting body?');
    }
    return requestContext.response;
    "TURBOPACK unreachable";
    async function execute(requestContext) {
        const { request, document } = requestContext;
        if (internals.__testing_incrementalExecutionResults) {
            return internals.__testing_incrementalExecutionResults;
        } else if (internals.gatewayExecutor) {
            const result = await internals.gatewayExecutor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$makeGatewayGraphQLRequestContext$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeGatewayGraphQLRequestContext"])(requestContext, server, internals));
            return {
                singleResult: result
            };
        } else {
            const resultOrResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$incrementalDeliveryPolyfill$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeIncrementally"])({
                schema: schemaDerivedData.schema,
                document,
                rootValue: typeof internals.rootValue === 'function' ? internals.rootValue(document) : internals.rootValue,
                contextValue: requestContext.contextValue,
                variableValues: request.variables,
                operationName: request.operationName,
                fieldResolver: internals.fieldResolver
            });
            if ('initialResult' in resultOrResults) {
                return {
                    initialResult: resultOrResults.initialResult,
                    subsequentResults: formatErrorsInSubsequentResults(resultOrResults.subsequentResults)
                };
            } else {
                return {
                    singleResult: resultOrResults
                };
            }
        }
    }
    async function* formatErrorsInSubsequentResults(results) {
        for await (const result of results){
            const payload = result.incremental ? {
                ...result,
                incremental: await seriesAsyncMap(result.incremental, async (incrementalResult)=>{
                    const { errors } = incrementalResult;
                    if (errors) {
                        await Promise.all(requestListeners.map((l)=>l.didEncounterSubsequentErrors?.(requestContext, errors)));
                        return {
                            ...incrementalResult,
                            errors: formatErrors(errors).formattedErrors
                        };
                    }
                    return incrementalResult;
                })
            } : result;
            await Promise.all(requestListeners.map((l)=>l.willSendSubsequentPayload?.(requestContext, payload)));
            yield payload;
        }
    }
    async function invokeWillSendResponse() {
        await Promise.all(requestListeners.map((l)=>l.willSendResponse?.(requestContext)));
    }
    async function didEncounterErrors(errors) {
        requestContext.errors = errors;
        return await Promise.all(requestListeners.map((l)=>l.didEncounterErrors?.(requestContext)));
    }
    async function sendErrorResponse(errors) {
        await didEncounterErrors(errors);
        const { formattedErrors, httpFromErrors } = formatErrors(errors);
        requestContext.response.body = {
            kind: 'single',
            singleResult: {
                errors: formattedErrors
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, httpFromErrors);
        if (!requestContext.response.http.status) {
            requestContext.response.http.status = 500;
        }
        await invokeWillSendResponse();
        return requestContext.response;
    }
    function formatErrors(errors) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeAndFormatErrors"])(errors, {
            formatError: internals.formatError,
            includeStacktraceInErrorResponses: internals.includeStacktraceInErrorResponses
        });
    }
}
async function seriesAsyncMap(ts, fn) {
    const us = [];
    for (const t of ts){
        const u = await fn(t);
        us.push(u);
    }
    return us;
} //# sourceMappingURL=requestPipeline.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/UnreachableCaseError.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UnreachableCaseError": (()=>UnreachableCaseError)
});
class UnreachableCaseError extends Error {
    constructor(val){
        super(`Unreachable case: ${val}`);
    }
} //# sourceMappingURL=UnreachableCaseError.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/computeCoreSchemaHash.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "computeCoreSchemaHash": (()=>computeCoreSchemaHash)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
;
function computeCoreSchemaHash(schema) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update(schema).digest('hex');
} //# sourceMappingURL=computeCoreSchemaHash.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/schemaManager.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SchemaManager": (()=>SchemaManager)
});
class SchemaManager {
    constructor(options){
        this.onSchemaLoadOrUpdateListeners = new Set();
        this.isStopped = false;
        this.logger = options.logger;
        this.schemaDerivedDataProvider = options.schemaDerivedDataProvider;
        if ('gateway' in options) {
            this.modeSpecificState = {
                mode: 'gateway',
                gateway: options.gateway,
                apolloConfig: options.apolloConfig
            };
        } else {
            this.modeSpecificState = {
                mode: 'schema',
                apiSchema: options.apiSchema,
                schemaDerivedData: options.schemaDerivedDataProvider(options.apiSchema)
            };
        }
    }
    async start() {
        if (this.modeSpecificState.mode === 'gateway') {
            const gateway = this.modeSpecificState.gateway;
            if (gateway.onSchemaLoadOrUpdate) {
                this.modeSpecificState.unsubscribeFromGateway = gateway.onSchemaLoadOrUpdate((schemaContext)=>{
                    this.processSchemaLoadOrUpdateEvent(schemaContext);
                });
            } else {
                throw new Error("Unexpectedly couldn't find onSchemaLoadOrUpdate on gateway");
            }
            const config = await this.modeSpecificState.gateway.load({
                apollo: this.modeSpecificState.apolloConfig
            });
            return config.executor;
        } else {
            this.processSchemaLoadOrUpdateEvent({
                apiSchema: this.modeSpecificState.apiSchema
            }, this.modeSpecificState.schemaDerivedData);
            return null;
        }
    }
    onSchemaLoadOrUpdate(callback) {
        if (!this.schemaContext) {
            throw new Error('You must call start() before onSchemaLoadOrUpdate()');
        }
        if (!this.isStopped) {
            try {
                callback(this.schemaContext);
            } catch (e) {
                throw new Error(`An error was thrown from an 'onSchemaLoadOrUpdate' listener: ${e.message}`);
            }
        }
        this.onSchemaLoadOrUpdateListeners.add(callback);
        return ()=>{
            this.onSchemaLoadOrUpdateListeners.delete(callback);
        };
    }
    getSchemaDerivedData() {
        if (!this.schemaDerivedData) {
            throw new Error('You must call start() before getSchemaDerivedData()');
        }
        return this.schemaDerivedData;
    }
    async stop() {
        this.isStopped = true;
        if (this.modeSpecificState.mode === 'gateway') {
            this.modeSpecificState.unsubscribeFromGateway?.();
            await this.modeSpecificState.gateway.stop?.();
        }
    }
    processSchemaLoadOrUpdateEvent(schemaContext, schemaDerivedData) {
        if (!this.isStopped) {
            this.schemaDerivedData = schemaDerivedData ?? this.schemaDerivedDataProvider(schemaContext.apiSchema);
            this.schemaContext = schemaContext;
            this.onSchemaLoadOrUpdateListeners.forEach((listener)=>{
                try {
                    listener(schemaContext);
                } catch (e) {
                    this.logger.error("An error was thrown from an 'onSchemaLoadOrUpdate' listener");
                    this.logger.error(e);
                }
            });
        }
    }
} //# sourceMappingURL=schemaManager.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NoIntrospection": (()=>NoIntrospection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
;
;
const NoIntrospection = (context)=>({
        Field (node) {
            if (node.name.value === '__schema' || node.name.value === '__type') {
                context.reportError(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('GraphQL introspection is not allowed by Apollo Server, but the query contained __schema or __type. To enable introspection, pass introspection: true to ApolloServer in production', {
                    nodes: [
                        node
                    ],
                    extensions: {
                        validationErrorCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerValidationErrorCode"].INTROSPECTION_DISABLED
                    }
                }));
            }
        }
    }); //# sourceMappingURL=NoIntrospection.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_MAX_RECURSIVE_SELECTIONS": (()=>DEFAULT_MAX_RECURSIVE_SELECTIONS),
    "createMaxRecursiveSelectionsRule": (()=>createMaxRecursiveSelectionsRule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
;
;
const DEFAULT_MAX_RECURSIVE_SELECTIONS = 10000000;
class RecursiveSelectionValidationContext {
    constructor(selectionCountLimit, context){
        this.selectionCountLimit = selectionCountLimit;
        this.context = context;
        this.fragmentInfo = new Map();
        this.operationInfo = new Map();
        this.fragmentRecursiveSelectionCount = new Map();
    }
    getExecutionDefinitionInfo() {
        if (this.currentFragment !== undefined) {
            let entry = this.fragmentInfo.get(this.currentFragment);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.fragmentInfo.set(this.currentFragment, entry);
            }
            return entry;
        }
        if (this.currentOperation !== undefined) {
            let entry = this.operationInfo.get(this.currentOperation);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.operationInfo.set(this.currentOperation, entry);
            }
            return entry;
        }
        return undefined;
    }
    processSelection(fragmentSpreadName) {
        const definitionInfo = this.getExecutionDefinitionInfo();
        if (!definitionInfo) {
            return;
        }
        definitionInfo.selectionCount++;
        if (fragmentSpreadName !== undefined) {
            let spreadCount = (definitionInfo.fragmentSpreads.get(fragmentSpreadName) ?? 0) + 1;
            definitionInfo.fragmentSpreads.set(fragmentSpreadName, spreadCount);
        }
    }
    enterFragment(fragment) {
        this.currentFragment = fragment;
    }
    leaveFragment() {
        this.currentFragment = undefined;
    }
    enterOperation(operation) {
        this.currentOperation = operation;
    }
    leaveOperation() {
        this.currentOperation = undefined;
    }
    computeFragmentRecursiveSelectionsCount(fragment) {
        const cachedCount = this.fragmentRecursiveSelectionCount.get(fragment);
        if (cachedCount === null) {
            return 0;
        }
        if (cachedCount !== undefined) {
            return cachedCount;
        }
        this.fragmentRecursiveSelectionCount.set(fragment, null);
        const definitionInfo = this.fragmentInfo.get(fragment);
        let count = 0;
        if (definitionInfo) {
            count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
        }
        this.fragmentRecursiveSelectionCount.set(fragment, count);
        return count;
    }
    reportError(operation) {
        const operationName = operation ? `Operation "${operation}"` : 'Anonymous operation';
        this.context.reportError(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](`${operationName} recursively requests too many selections.`, {
            nodes: [],
            extensions: {
                validationErrorCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerValidationErrorCode"].MAX_RECURSIVE_SELECTIONS_EXCEEDED
            }
        }));
    }
    checkLimitExceeded() {
        for (const [operation, definitionInfo] of this.operationInfo){
            let count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
            if (count > this.selectionCountLimit) {
                this.reportError(operation);
            }
        }
    }
}
function createMaxRecursiveSelectionsRule(limit) {
    return (context)=>{
        const selectionContext = new RecursiveSelectionValidationContext(limit, context);
        return {
            Field () {
                selectionContext.processSelection();
            },
            InlineFragment () {
                selectionContext.processSelection();
            },
            FragmentSpread (node) {
                selectionContext.processSelection(node.name.value);
            },
            FragmentDefinition: {
                enter (node) {
                    selectionContext.enterFragment(node.name.value);
                },
                leave () {
                    selectionContext.leaveFragment();
                }
            },
            OperationDefinition: {
                enter (node) {
                    selectionContext.enterOperation(node.name?.value ?? null);
                },
                leave () {
                    selectionContext.leaveOperation();
                }
            },
            Document: {
                leave () {
                    selectionContext.checkLimitExceeded();
                }
            }
        };
    };
} //# sourceMappingURL=RecursiveSelectionsLimit.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <locals>");
}}),
"[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApolloServer": (()=>ApolloServer),
    "MEDIA_TYPES": (()=>MEDIA_TYPES),
    "chooseContentTypeForSingleResultResponse": (()=>chooseContentTypeForSingleResultResponse),
    "internalExecuteOperation": (()=>internalExecuteOperation),
    "isImplicitlyInstallablePlugin": (()=>isImplicitlyInstallablePlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$isnodelike$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.isnodelike/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.keyvaluecache/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/resolvable.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/validate.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/printer.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$printSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/printSchema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/loglevel/lib/loglevel.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$cachePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/cachePolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$determineApolloConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/determineApolloConfig.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$httpBatching$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/httpBatching.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalPlugin.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/preventCsrf.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/requestPipeline.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/UnreachableCaseError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$computeCoreSchemaHash$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/computeCoreSchemaHash.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/schemaManager.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function defaultLogger() {
    const loglevelLogger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getLogger('apollo-server');
    loglevelLogger.setLevel(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].levels.INFO);
    return loglevelLogger;
}
class ApolloServer {
    constructor(config){
        const nodeEnv = config.nodeEnv ?? ("TURBOPACK compile-time value", "development") ?? '';
        this.logger = config.logger ?? defaultLogger();
        const apolloConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$determineApolloConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["determineApolloConfig"])(config.apollo, this.logger);
        const isDev = nodeEnv !== 'production';
        if (config.cache && config.cache !== 'bounded' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrefixingKeyValueCache"].prefixesAreUnnecessaryForIsolation(config.cache)) {
            throw new Error('You cannot pass a cache returned from ' + '`PrefixingKeyValueCache.cacheDangerouslyDoesNotNeedPrefixesForIsolation`' + 'to `new ApolloServer({ cache })`, because Apollo Server may use it for ' + 'multiple features whose cache keys must be distinct from each other.');
        }
        const state = config.gateway ? {
            phase: 'initialized',
            schemaManager: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SchemaManager"]({
                gateway: config.gateway,
                apolloConfig,
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        } : {
            phase: 'initialized',
            schemaManager: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SchemaManager"]({
                apiSchema: ApolloServer.constructSchema(config),
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        };
        const introspectionEnabled = config.introspection ?? isDev;
        const hideSchemaDetailsFromClientErrors = config.hideSchemaDetailsFromClientErrors ?? false;
        this.cache = config.cache === undefined || config.cache === 'bounded' ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InMemoryLRUCache"]() : config.cache;
        const maxRecursiveSelectionsRule = config.maxRecursiveSelections === true ? [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMaxRecursiveSelectionsRule"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_MAX_RECURSIVE_SELECTIONS"])
        ] : typeof config.maxRecursiveSelections === 'number' ? [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMaxRecursiveSelectionsRule"])(config.maxRecursiveSelections)
        ] : [];
        const validationRules = [
            ...introspectionEnabled ? [] : [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NoIntrospection"]
            ],
            ...maxRecursiveSelectionsRule
        ];
        let laterValidationRules;
        if (maxRecursiveSelectionsRule.length > 0) {
            laterValidationRules = config.validationRules;
        } else {
            validationRules.push(...config.validationRules ?? []);
        }
        this.internals = {
            formatError: config.formatError,
            rootValue: config.rootValue,
            validationRules,
            laterValidationRules,
            hideSchemaDetailsFromClientErrors,
            dangerouslyDisableValidation: config.dangerouslyDisableValidation ?? false,
            fieldResolver: config.fieldResolver,
            includeStacktraceInErrorResponses: config.includeStacktraceInErrorResponses ?? (nodeEnv !== 'production' && nodeEnv !== 'test'),
            persistedQueries: config.persistedQueries === false ? undefined : {
                ...config.persistedQueries,
                cache: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrefixingKeyValueCache"](config.persistedQueries?.cache ?? this.cache, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APQ_CACHE_PREFIX"])
            },
            nodeEnv,
            allowBatchedHttpRequests: config.allowBatchedHttpRequests ?? false,
            apolloConfig,
            plugins: config.plugins ?? [],
            parseOptions: config.parseOptions ?? {},
            state,
            stopOnTerminationSignals: config.stopOnTerminationSignals,
            gatewayExecutor: null,
            csrfPreventionRequestHeaders: config.csrfPrevention === true || config.csrfPrevention === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["recommendedCsrfPreventionRequestHeaders"] : config.csrfPrevention === false ? null : config.csrfPrevention.requestHeaders ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["recommendedCsrfPreventionRequestHeaders"],
            status400ForVariableCoercionErrors: config.status400ForVariableCoercionErrors ?? false,
            __testing_incrementalExecutionResults: config.__testing_incrementalExecutionResults,
            stringifyResult: config.stringifyResult ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prettyJSONStringify"]
        };
    }
    async start() {
        return await this._start(false);
    }
    startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests() {
        this._start(true).catch((e)=>this.logStartupError(e));
    }
    async _start(startedInBackground) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error(`You should only call 'start()' or ` + `'startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests()' ` + `once on your ApolloServer.`);
        }
        const schemaManager = this.internals.state.schemaManager;
        const barrier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        this.internals.state = {
            phase: 'starting',
            barrier,
            schemaManager,
            startedInBackground
        };
        try {
            await this.addDefaultPlugins();
            const toDispose = [];
            const executor = await schemaManager.start();
            if (executor) {
                this.internals.gatewayExecutor = executor;
            }
            toDispose.push(async ()=>{
                await schemaManager.stop();
            });
            const schemaDerivedData = schemaManager.getSchemaDerivedData();
            const service = {
                logger: this.logger,
                cache: this.cache,
                schema: schemaDerivedData.schema,
                apollo: this.internals.apolloConfig,
                startedInBackground
            };
            const taggedServerListeners = (await Promise.all(this.internals.plugins.map(async (plugin)=>({
                    serverListener: plugin.serverWillStart && await plugin.serverWillStart(service),
                    installedImplicitly: isImplicitlyInstallablePlugin(plugin) && plugin.__internal_installed_implicitly__
                })))).filter((maybeTaggedServerListener)=>typeof maybeTaggedServerListener.serverListener === 'object');
            taggedServerListeners.forEach(({ serverListener: { schemaDidLoadOrUpdate } })=>{
                if (schemaDidLoadOrUpdate) {
                    schemaManager.onSchemaLoadOrUpdate(schemaDidLoadOrUpdate);
                }
            });
            const serverWillStops = taggedServerListeners.map((l)=>l.serverListener.serverWillStop).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
            if (serverWillStops.length) {
                toDispose.push(async ()=>{
                    await Promise.all(serverWillStops.map((serverWillStop)=>serverWillStop()));
                });
            }
            const drainServerCallbacks = taggedServerListeners.map((l)=>l.serverListener.drainServer).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
            const drainServers = drainServerCallbacks.length ? async ()=>{
                await Promise.all(drainServerCallbacks.map((drainServer)=>drainServer()));
            } : null;
            let taggedServerListenersWithRenderLandingPage = taggedServerListeners.filter((l)=>l.serverListener.renderLandingPage);
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                taggedServerListenersWithRenderLandingPage = taggedServerListenersWithRenderLandingPage.filter((l)=>!l.installedImplicitly);
            }
            let landingPage = null;
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                throw Error('Only one plugin can implement renderLandingPage.');
            } else if (taggedServerListenersWithRenderLandingPage.length) {
                landingPage = await taggedServerListenersWithRenderLandingPage[0].serverListener.renderLandingPage();
            }
            const toDisposeLast = this.maybeRegisterTerminationSignalHandlers([
                'SIGINT',
                'SIGTERM'
            ], startedInBackground);
            this.internals.state = {
                phase: 'started',
                schemaManager,
                drainServers,
                landingPage,
                toDispose,
                toDisposeLast
            };
        } catch (maybeError) {
            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
            try {
                await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.startupDidFail?.({
                        error
                    })));
            } catch (pluginError) {
                this.logger.error(`startupDidFail hook threw: ${pluginError}`);
            }
            this.internals.state = {
                phase: 'failed to start',
                error
            };
            throw error;
        } finally{
            barrier.resolve();
        }
    }
    maybeRegisterTerminationSignalHandlers(signals, startedInBackground) {
        const toDisposeLast = [];
        if (this.internals.stopOnTerminationSignals === false || this.internals.stopOnTerminationSignals === undefined && !(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$isnodelike$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"] && this.internals.nodeEnv !== 'test' && !startedInBackground)) {
            return toDisposeLast;
        }
        let receivedSignal = false;
        const signalHandler = async (signal)=>{
            if (receivedSignal) {
                return;
            }
            receivedSignal = true;
            try {
                await this.stop();
            } catch (e) {
                this.logger.error(`stop() threw during ${signal} shutdown`);
                this.logger.error(e);
                process.exit(1);
            }
            process.kill(process.pid, signal);
        };
        signals.forEach((signal)=>{
            process.on(signal, signalHandler);
            toDisposeLast.push(async ()=>{
                process.removeListener(signal, signalHandler);
            });
        });
        return toDisposeLast;
    }
    async _ensureStarted() {
        while(true){
            switch(this.internals.state.phase){
                case 'initialized':
                    throw new Error('You need to call `server.start()` before using your Apollo Server.');
                case 'starting':
                    await this.internals.state.barrier;
                    break;
                case 'failed to start':
                    this.logStartupError(this.internals.state.error);
                    throw new Error('This data graph is missing a valid configuration. More details may be available in the server logs.');
                case 'started':
                case 'draining':
                    return this.internals.state;
                case 'stopping':
                case 'stopped':
                    this.logger.warn('A GraphQL operation was received during server shutdown. The ' + 'operation will fail. Consider draining the HTTP server on shutdown; ' + 'see https://go.apollo.dev/s/drain for details.');
                    throw new Error(`Cannot execute GraphQL operations ${this.internals.state.phase === 'stopping' ? 'while the server is stopping' : 'after the server has stopped'}.'`);
                default:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnreachableCaseError"](this.internals.state);
            }
        }
    }
    assertStarted(expressionForError) {
        if (this.internals.state.phase !== 'started' && this.internals.state.phase !== 'draining' && !(this.internals.state.phase === 'starting' && this.internals.state.startedInBackground)) {
            throw new Error('You must `await server.start()` before calling `' + expressionForError + '`');
        }
    }
    logStartupError(err) {
        this.logger.error('An error occurred during Apollo Server startup. All GraphQL requests ' + 'will now fail. The startup error was: ' + (err?.message || err));
    }
    static constructSchema(config) {
        if (config.schema) {
            return config.schema;
        }
        const { typeDefs, resolvers } = config;
        const augmentedTypeDefs = Array.isArray(typeDefs) ? typeDefs : [
            typeDefs
        ];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeExecutableSchema"])({
            typeDefs: augmentedTypeDefs,
            resolvers
        });
    }
    static generateSchemaDerivedData(schema, providedDocumentStore) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertValidSchema"])(schema);
        return {
            schema,
            documentStore: providedDocumentStore === undefined ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InMemoryLRUCache"]() : providedDocumentStore,
            documentStoreKeyPrefix: providedDocumentStore ? `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$computeCoreSchemaHash$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["computeCoreSchemaHash"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$printSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printSchema"])(schema))}:` : ''
        };
    }
    async stop() {
        switch(this.internals.state.phase){
            case 'initialized':
            case 'starting':
            case 'failed to start':
                throw Error('apolloServer.stop() should only be called after `await apolloServer.start()` has succeeded');
            case 'stopped':
                if (this.internals.state.stopError) {
                    throw this.internals.state.stopError;
                }
                return;
            case 'stopping':
            case 'draining':
                {
                    await this.internals.state.barrier;
                    const state = this.internals.state;
                    if (state.phase !== 'stopped') {
                        throw Error(`Surprising post-stopping state ${state.phase}`);
                    }
                    if (state.stopError) {
                        throw state.stopError;
                    }
                    return;
                }
            case 'started':
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnreachableCaseError"](this.internals.state);
        }
        const barrier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { schemaManager, drainServers, landingPage, toDispose, toDisposeLast } = this.internals.state;
        this.internals.state = {
            phase: 'draining',
            barrier,
            schemaManager,
            landingPage
        };
        try {
            await drainServers?.();
            this.internals.state = {
                phase: 'stopping',
                barrier
            };
            await Promise.all([
                ...toDispose
            ].map((dispose)=>dispose()));
            await Promise.all([
                ...toDisposeLast
            ].map((dispose)=>dispose()));
        } catch (stopError) {
            this.internals.state = {
                phase: 'stopped',
                stopError: stopError
            };
            barrier.resolve();
            throw stopError;
        }
        this.internals.state = {
            phase: 'stopped',
            stopError: null
        };
    }
    async addDefaultPlugins() {
        const { plugins, apolloConfig, nodeEnv, hideSchemaDetailsFromClientErrors } = this.internals;
        const isDev = nodeEnv !== 'production';
        const alreadyHavePluginWithInternalId = (id)=>plugins.some((p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pluginIsInternal"])(p) && p.__internal_plugin_id__ === id);
        const pluginsByInternalID = new Map();
        for (const p of plugins){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pluginIsInternal"])(p)) {
                const id = p.__internal_plugin_id__;
                if (!pluginsByInternalID.has(id)) {
                    pluginsByInternalID.set(id, {
                        sawDisabled: false,
                        sawNonDisabled: false
                    });
                }
                const seen = pluginsByInternalID.get(id);
                if (p.__is_disabled_plugin__) {
                    seen.sawDisabled = true;
                } else {
                    seen.sawNonDisabled = true;
                }
                if (seen.sawDisabled && seen.sawNonDisabled) {
                    throw new Error(`You have tried to install both ApolloServerPlugin${id} and ` + `ApolloServerPlugin${id}Disabled in your server. Please choose ` + `whether or not you want to disable the feature and install the ` + `appropriate plugin for your use case.`);
                }
            }
        }
        {
            if (!alreadyHavePluginWithInternalId('CacheControl')) {
                const { ApolloServerPluginCacheControl } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginCacheControl());
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('UsageReporting');
            if (!alreadyHavePlugin && apolloConfig.key) {
                if (apolloConfig.graphRef) {
                    const { ApolloServerPluginUsageReporting } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                    plugins.unshift(ApolloServerPluginUsageReporting({
                        __onlyIfSchemaIsNotSubgraph: true
                    }));
                } else {
                    this.logger.warn('You have specified an Apollo key but have not specified a graph ref; usage ' + 'reporting is disabled. To enable usage reporting, set the `APOLLO_GRAPH_REF` ' + 'environment variable to `your-graph-id@your-graph-variant`. To disable this ' + 'warning, install `ApolloServerPluginUsageReportingDisabled`.');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('SchemaReporting');
            const enabledViaEnvVar = process.env.APOLLO_SCHEMA_REPORTING === 'true';
            if (!alreadyHavePlugin && enabledViaEnvVar) {
                if (apolloConfig.key) {
                    const { ApolloServerPluginSchemaReporting } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                    plugins.push(ApolloServerPluginSchemaReporting());
                } else {
                    throw new Error("You've enabled schema reporting by setting the APOLLO_SCHEMA_REPORTING " + 'environment variable to true, but you also need to provide your ' + 'Apollo API key, via the APOLLO_KEY environment ' + 'variable or via `new ApolloServer({apollo: {key})');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('InlineTrace');
            if (!alreadyHavePlugin) {
                const { ApolloServerPluginInlineTrace } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginInlineTrace({
                    __onlyIfSchemaIsSubgraph: true
                }));
            }
        }
        const alreadyHavePlugin = alreadyHavePluginWithInternalId('LandingPageDisabled');
        if (!alreadyHavePlugin) {
            const { ApolloServerPluginLandingPageLocalDefault, ApolloServerPluginLandingPageProductionDefault } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const plugin = isDev ? ApolloServerPluginLandingPageLocalDefault() : ApolloServerPluginLandingPageProductionDefault();
            if (!isImplicitlyInstallablePlugin(plugin)) {
                throw Error('default landing page plugin should be implicitly installable?');
            }
            plugin.__internal_installed_implicitly__ = true;
            plugins.push(plugin);
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('DisableSuggestions');
            if (hideSchemaDetailsFromClientErrors && !alreadyHavePlugin) {
                const { ApolloServerPluginDisableSuggestions } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginDisableSuggestions());
            }
        }
    }
    addPlugin(plugin) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error("Can't add plugins after the server has started");
        }
        this.internals.plugins.push(plugin);
    }
    async executeHTTPGraphQLRequest({ httpGraphQLRequest, context }) {
        try {
            let runningServerState;
            try {
                runningServerState = await this._ensureStarted();
            } catch (error) {
                return await this.errorResponse(error, httpGraphQLRequest);
            }
            if (runningServerState.landingPage && this.prefersHTML(httpGraphQLRequest)) {
                let renderedHtml;
                if (typeof runningServerState.landingPage.html === 'string') {
                    renderedHtml = runningServerState.landingPage.html;
                } else {
                    try {
                        renderedHtml = await runningServerState.landingPage.html();
                    } catch (maybeError) {
                        const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
                        this.logger.error(`Landing page \`html\` function threw: ${error}`);
                        return await this.errorResponse(error, httpGraphQLRequest);
                    }
                }
                return {
                    headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                        [
                            'content-type',
                            'text/html'
                        ]
                    ]),
                    body: {
                        kind: 'complete',
                        string: renderedHtml
                    }
                };
            }
            if (this.internals.csrfPreventionRequestHeaders) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["preventCsrf"])(httpGraphQLRequest.headers, this.internals.csrfPreventionRequestHeaders);
            }
            let contextValue;
            try {
                contextValue = await context();
            } catch (maybeError) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.contextCreationDidFail?.({
                            error
                        })));
                } catch (pluginError) {
                    this.logger.error(`contextCreationDidFail hook threw: ${pluginError}`);
                }
                return await this.errorResponse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(error, 'Context creation failed: '), httpGraphQLRequest);
            }
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$httpBatching$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runPotentiallyBatchedHttpQuery"])(this, httpGraphQLRequest, contextValue, runningServerState.schemaManager.getSchemaDerivedData(), this.internals);
        } catch (maybeError_) {
            const maybeError = maybeError_;
            if (maybeError instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] && maybeError.extensions.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_REQUEST) {
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.invalidRequestWasReceived?.({
                            error: maybeError
                        })));
                } catch (pluginError) {
                    this.logger.error(`invalidRequestWasReceived hook threw: ${pluginError}`);
                }
            }
            return await this.errorResponse(maybeError, httpGraphQLRequest);
        }
    }
    async errorResponse(error, requestHead) {
        const { formattedErrors, httpFromErrors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeAndFormatErrors"])([
            error
        ], {
            includeStacktraceInErrorResponses: this.internals.includeStacktraceInErrorResponses,
            formatError: this.internals.formatError
        });
        return {
            status: httpFromErrors.status ?? 500,
            headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                ...httpFromErrors.headers,
                [
                    'content-type',
                    chooseContentTypeForSingleResultResponse(requestHead) ?? MEDIA_TYPES.APPLICATION_JSON
                ]
            ]),
            body: {
                kind: 'complete',
                string: await this.internals.stringifyResult({
                    errors: formattedErrors
                })
            }
        };
    }
    prefersHTML(request) {
        const acceptHeader = request.headers.get('accept');
        return request.method === 'GET' && !!acceptHeader && new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            headers: {
                accept: acceptHeader
            }
        }).mediaType([
            MEDIA_TYPES.APPLICATION_JSON,
            MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            MEDIA_TYPES.MULTIPART_MIXED_EXPERIMENTAL,
            MEDIA_TYPES.MULTIPART_MIXED_NO_DEFER_SPEC,
            MEDIA_TYPES.TEXT_HTML
        ]) === MEDIA_TYPES.TEXT_HTML;
    }
    async executeOperation(request, options = {}) {
        if (this.internals.state.phase === 'initialized') {
            await this.start();
        }
        const schemaDerivedData = (await this._ensureStarted()).schemaManager.getSchemaDerivedData();
        const graphQLRequest = {
            ...request,
            query: request.query && typeof request.query !== 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])(request.query) : request.query
        };
        const response = await internalExecuteOperation({
            server: this,
            graphQLRequest,
            internals: this.internals,
            schemaDerivedData,
            sharedResponseHTTPGraphQLHead: null
        }, options);
        return response;
    }
}
async function internalExecuteOperation({ server, graphQLRequest, internals, schemaDerivedData, sharedResponseHTTPGraphQLHead }, options) {
    const requestContext = {
        logger: server.logger,
        cache: server.cache,
        schema: schemaDerivedData.schema,
        request: graphQLRequest,
        response: {
            http: sharedResponseHTTPGraphQLHead ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])()
        },
        contextValue: cloneObject(options?.contextValue ?? {}),
        metrics: {},
        overallCachePolicy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$cachePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newCachePolicy"])(),
        requestIsBatched: sharedResponseHTTPGraphQLHead !== null
    };
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processGraphQLRequest"])(schemaDerivedData, server, internals, requestContext);
    } catch (maybeError) {
        const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
        await Promise.all(internals.plugins.map(async (plugin)=>plugin.unexpectedErrorProcessingRequest?.({
                requestContext,
                error
            })));
        server.logger.error(`Unexpected error processing request: ${error}`);
        throw new Error('Internal server error');
    }
}
function isImplicitlyInstallablePlugin(p) {
    return '__internal_installed_implicitly__' in p;
}
const MEDIA_TYPES = {
    APPLICATION_JSON: 'application/json; charset=utf-8',
    APPLICATION_JSON_GRAPHQL_CALLBACK: 'application/json; callbackSpec=1.0; charset=utf-8',
    APPLICATION_GRAPHQL_RESPONSE_JSON: 'application/graphql-response+json; charset=utf-8',
    MULTIPART_MIXED_NO_DEFER_SPEC: 'multipart/mixed',
    MULTIPART_MIXED_EXPERIMENTAL: 'multipart/mixed; deferSpec=20220824',
    TEXT_HTML: 'text/html'
};
function chooseContentTypeForSingleResultResponse(head) {
    const acceptHeader = head.headers.get('accept');
    if (!acceptHeader) {
        return MEDIA_TYPES.APPLICATION_JSON;
    } else {
        const preferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            headers: {
                accept: head.headers.get('accept')
            }
        }).mediaType([
            MEDIA_TYPES.APPLICATION_JSON,
            MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            MEDIA_TYPES.APPLICATION_JSON_GRAPHQL_CALLBACK
        ]);
        if (preferred) {
            return preferred;
        } else {
            return null;
        }
    }
}
function cloneObject(object) {
    return Object.assign(Object.create(Object.getPrototypeOf(object)), object);
} //# sourceMappingURL=ApolloServer.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$externalTypes$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
}}),
"[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$externalTypes$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=node_modules_%40apollo_server_dist_esm_f34ef4b7._.js.map