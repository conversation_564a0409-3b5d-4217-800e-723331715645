{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "file": "getEmbeddedHTML.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/getEmbeddedHTML.ts"], "names": [], "mappings": ";;;;AAcA,SAAS,sBAAsB,CAAC,MAAc;IAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC7B,CAAC;AAEM,MAAM,uBAAuB,GAAG,CACrC,kBAA0B,EAC1B,MAAqE,EACrE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IA2BF,MAAM,yCAAyC,GAAG;QAChD,cAAc,EAAE,CAAA,CAAE;QAClB,oBAAoB,EAAE,KAAK;QAC3B,YAAY,EAAE,IAAI;QAClB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;KAC3D,CAAC;IACF,MAAM,sBAAsB,GAGF;QACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,qBAAqB;QAC7B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE;gBACd,GAAG,yCAAyC,CAAC,cAAc;aAC5D;SACF;QACD,oBAAoB,EAClB,yCAAyC,CAAC,oBAAoB;QAChE,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,yCAAyC,CAAC,YAAY;QACpE,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAEF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,yDAAA,EAA4D,kBAAkB,CAChG,kBAAkB,CACnB,CAAA,mDAAA,EAAsD,kBAAkB,CACvE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;iCAEW,sBAAsB,CACnD,sBAAsB,CACvB,CAAA;;;;;;CAMF,CAAC;AACF,CAAC,CAAC;AAEK,MAAM,sBAAsB,GAAG,CACpC,iBAAyB,EACzB,MAAgE,EAChE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,oCAAoC,GAAG;QAC3C,YAAY,EAAE,IAAI;QAClB,kBAAkB,EAAE,KAAK;QACzB,YAAY,EAAE,CAAA,CAAE;QAChB,GAAG,AAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC,AAAC,MAAM,CAAC,KAAK,IAAI,CAAA,CAAE,AAAC,CAAC;KACnE,CAAC;IACF,MAAM,qBAAqB,GAAG;QAC5B,MAAM,EAAE,oBAAoB;QAC5B,YAAY,EAAE;YACZ,GAAG,AAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM,GACpE;gBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,GACD,CAAA,CAAE,CAAC;YACP,GAAG,AAAC,cAAc,IAAI,MAAM,GACxB;gBACE,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,GACD,CAAA,CAAE,CAAC;YACP,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,GAAG,oCAAoC,CAAC,YAAY;SACrD;QACD,gBAAgB,EAAE,KAAK;QACvB,kBAAkB,EAAE,oCAAoC,CAAC,kBAAkB;QAC3E,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,oCAAoC,CAAC,YAAY;QAC/D,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IACF,OAAO,CAAA;;;;;eAKM,KAAK,CAAA;;;;;;;;;;;;;;;iBAeH,KAAK,CAAA,wDAAA,EAA2D,kBAAkB,CAC/F,iBAAiB,CAClB,CAAA,kDAAA,EAAqD,kBAAkB,CACtE,mBAAmB,CACpB,CAAA;iBACc,KAAK,CAAA;;gCAEU,sBAAsB,CAAC,qBAAqB,CAAC,CAAA;;;;;;;;CAQ5E,CAAC;AACF,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "file": "packageVersion.js", "sourceRoot": "", "sources": ["../../../src/generated/packageVersion.ts"], "names": [], "mappings": ";;;AAAO,MAAM,cAAc,GAAG,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/native.js"], "sourcesContent": ["import crypto from 'crypto';\nexport default {\n  randomUUID: crypto.randomUUID\n};"], "names": [], "mappings": ";;;AAAA;;uCACe;IACb,YAAY,qGAAA,CAAA,UAAM,CAAC,UAAU;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/rng.js"], "sourcesContent": ["import crypto from 'crypto';\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\n\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    crypto.randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW,MAAM,qCAAqC;AAE5E,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACtB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACnC,qGAAA,CAAA,UAAM,CAAC,cAAc,CAAC;QACtB,UAAU;IACZ;IAEA,OAAO,UAAU,KAAK,CAAC,SAAS,WAAW;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,SAAS,IAAI;IACpB,OAAO,OAAO,SAAS,YAAY,sJAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAChD;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/stringify.js"], "sourcesContent": ["import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;"], "names": [], "mappings": ";;;;AAAA;;AACA;;;CAGC,GAED,MAAM,YAAY,EAAE;AAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC5B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAChD;AAEO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC7C,uEAAuE;IACvE,oFAAoF;IACpF,OAAO,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC;AACpf;AAEA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAChC,MAAM,OAAO,gBAAgB,KAAK,SAAS,4EAA4E;IACvH,oBAAoB;IACpB,wEAAwE;IACxE,2BAA2B;IAC3B,mEAAmE;IAEnE,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACnB,MAAM,UAAU;IAClB;IAEA,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/uuid/dist/esm-node/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC9B,IAAI,uJAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACzC,OAAO,uJAAA,CAAA,UAAM,CAAC,UAAU;IAC1B;IAEA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,oJAAA,CAAA,UAAG,KAAK,gEAAgE;IAEvH,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO;IAC3B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,OAAO,MAAM,oCAAoC;IAErE,IAAI,KAAK;QACP,SAAS,UAAU;QAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YAC3B,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC3B;QAEA,OAAO;IACT;IAEA,OAAO,CAAA,GAAA,0JAAA,CAAA,kBAAe,AAAD,EAAE;AACzB;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/index.ts"], "names": [], "mappings": ";;;;;;;AAUA,OAAO,EACL,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AACtE,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAC;;;;;AAO9B,SAAU,yCAAyC,CACvD,UAA4D,CAAA,CAAE;IAE9D,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG;QAEzD,KAAK,EAAE,IAAa;QACpB,GAAG,OAAO;KACX,CAAC;IACF,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,KAAK;QACb,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAEK,SAAU,8CAA8C,CAC5D,UAAiE,CAAA,CAAE;IAEnE,MAAM,EAAE,OAAO,EAAE,4BAA4B,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IACnE,OAAO,oCAAoC,CAAC,OAAO,EAAE;QACnD,MAAM,EAAE,IAAI;QACZ,eAAe,EAAE,4BAA4B;QAC7C,GAAG,IAAI;KACR,CAAC,CAAC;AACL,CAAC;AAUD,SAAS,YAAY,CAAC,MAAyB;IAC7C,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,MAAM,6BAA6B,GAAG,CACpC,UAAkB,EAClB,MAAyB,EACzB,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAE3C,OAAO,CAAA;;;;;iBAKQ,KAAK,CAAA,uBAAA,EAA0B,aAAa,CAAA;iBAC5C,KAAK,CAAA,gEAAA,EAAmE,kBAAkB,CACvG,UAAU,CACX,CAAA,2BAAA,EAA8B,mBAAmB,CAAA,WAAA,CAAa,CAAC;AAClE,CAAC,CAAC;AAEK,MAAM,iCAAiC,GAAG,IAAI,CAAC;AAC/C,MAAM,gCAAgC,GAAG,IAAI,CAAC;AAC9C,MAAM,0CAA0C,GAAG,SAAS,CAAC;AAGpE,SAAS,oCAAoC,CAC3C,YAAgC,EAChC,MAGC;IAED,MAAM,eAAe,GAAG,YAAY,IAAI,iCAAiC,CAAC;IAC1E,MAAM,cAAc,GAAG,YAAY,IAAI,gCAAgC,CAAC;IACxE,MAAM,8BAA8B,GAClC,YAAY,IAAI,0CAA0C,CAAC;IAC7D,MAAM,mBAAmB,GAAG,CAAA,eAAA,qLAAkB,iBAAc,EAAE,CAAC;IAE/D,MAAM,cAAc,GAAG;QACrB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;KACpD,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,aAAa,GAAG;QACpB,0DAA0D;QAC1D,kDAAkD;QAClD,mDAAmD;QACnD,8BAA8B;KAC/B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACZ,MAAM,cAAc,GAAG;QACrB,0CAA0C;QAC1C,yCAAyC;QACzC,iCAAiC;KAClC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEZ,OAAO;QACL,iCAAiC,EAAE,KAAK;QACxC,KAAK,CAAC,eAAe,EAAC,MAAM;YAC1B,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAChB,kOAAkO,CACnO,CAAC;YACJ,CAAC;YACD,OAAO;gBACL,KAAK,CAAC,iBAAiB;oBACrB,MAAM,2BAA2B,GAAG,kBAAkB,CACpD,8BAA8B,CAC/B,CAAC;oBACF,KAAK,UAAU,IAAI;wBACjB,MAAM,KAAK,GACT,MAAM,CAAC,gBAAgB,2KACvB,aAAA,AAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,KAAC,0LAAA,AAAM,EAAE,CAAC,EAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACtD,MAAM,SAAS,GAAG,CAAA,yBAAA,EAA4B,KAAK,CAAA,EAAA,EAAK,cAAc,EAAE,CAAC;wBACzE,MAAM,QAAQ,GAAG,CAAA,iBAAA,EAAoB,KAAK,CAAA,EAAA,EAAK,aAAa,EAAE,CAAC;wBAC/D,MAAM,QAAQ,GAAG,CAAA,gEAAA,CAAkE,CAAC;wBACpF,MAAM,WAAW,GAAG,CAAA,qEAAA,CAAuE,CAAC;wBAC5F,MAAM,QAAQ,GAAG,CAAA,UAAA,EAAa,cAAc,EAAE,CAAC;wBAC/C,OAAO,CAAA;;;;;0DAKuC,SAAS,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,QAAQ,CAAA,EAAA,EAAK,WAAW,CAAA,EAAA,EAAK,QAAQ,CAAA;;;uEAGnD,2BAA2B,CAAA;;;;;;;;;;;;uEAY3B,2BAA2B,CAAA;;;;uEAI3B,2BAA2B,CAAA;;;;;;;qBAO7E,KAAK,CAAA;;;;;;;;;;;;;;;;;;MAmBpB,MAAM,CAAC,KAAK,GACR,UAAU,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,kNACrC,0BAAA,AAAuB,EACrB,eAAe,EACf,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,IACrB,uOAAA,AAAsB,EACpB,cAAc,EACd,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACD,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CACN,GACL,6BAA6B,CAC3B,8BAA8B,EAC9B,MAAM,EACN,mBAAmB,EACnB,KAAK,CAEb,CAAA;;;;WAIO,CAAC;oBACF,CAAC;oBACD,OAAO;wBAAE,IAAI;oBAAA,CAAE,CAAC;gBAClB,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "debugId": null}}]}