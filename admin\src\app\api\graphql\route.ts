import { ApolloServer } from '@apollo/server';
import { startServerAndCreateNextHandler } from '@as-integrations/next';
import { makeExecutableSchema } from '@graphql-tools/schema';
import { typeDefs } from '@/lib/graphql/schema';
import { resolvers } from '@/lib/graphql/resolvers';
import { NextRequest } from 'next/server';

// Create the GraphQL schema
const schema = makeExecutableSchema({
  typeDefs,
  resolvers,
});

// Create Apollo Server instance
const server = new ApolloServer({
  schema,
  introspection: process.env.NODE_ENV !== 'production',
  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',
  // Security: Limit query complexity and depth (Apollo best practice)
  plugins: [
    // Add query complexity analysis in production
    ...(process.env.NODE_ENV === 'production' ? [] : []),
  ],
});

// Create the Next.js handler
const handler = startServerAndCreateNextHandler<NextRequest>(server, {
  context: async (req) => {
    // Extract authentication from request headers or cookies
    // This follows Apollo's recommendation for putting user info in context
    const authHeader = req.headers.get('authorization');
    const sessionCookie = req.cookies.get('session')?.value;

    return {
      req,
      // Add user context for authorization in resolvers
      authHeader,
      sessionCookie,
    };
  },
});

export { handler as GET, handler as POST };
