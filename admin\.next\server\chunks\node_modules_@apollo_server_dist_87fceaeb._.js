module.exports = {

"[project]/node_modules/@apollo/server/dist/esm/utils/resolvable.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = ()=>{
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject)=>{
        resolve = _resolve;
        reject = _reject;
    });
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
};
 //# sourceMappingURL=resolvable.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/cachePolicy.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "newCachePolicy": (()=>newCachePolicy)
});
function newCachePolicy() {
    return {
        maxAge: undefined,
        scope: undefined,
        restrict (hint) {
            if (hint.maxAge !== undefined && (this.maxAge === undefined || hint.maxAge < this.maxAge)) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined && this.scope !== 'PRIVATE') {
                this.scope = hint.scope;
            }
        },
        replace (hint) {
            if (hint.maxAge !== undefined) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined) {
                this.scope = hint.scope;
            }
        },
        policyIfCacheable () {
            if (this.maxAge === undefined || this.maxAge === 0) {
                return null;
            }
            return {
                maxAge: this.maxAge,
                scope: this.scope ?? 'PUBLIC'
            };
        }
    };
} //# sourceMappingURL=cachePolicy.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/determineApolloConfig.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "determineApolloConfig": (()=>determineApolloConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
;
function determineApolloConfig(input, logger) {
    const apolloConfig = {};
    const { APOLLO_KEY, APOLLO_GRAPH_REF, APOLLO_GRAPH_ID, APOLLO_GRAPH_VARIANT } = process.env;
    if (input?.key) {
        apolloConfig.key = input.key.trim();
    } else if (APOLLO_KEY) {
        apolloConfig.key = APOLLO_KEY.trim();
    }
    if ((input?.key ?? APOLLO_KEY) !== apolloConfig.key) {
        logger.warn('The provided API key has unexpected leading or trailing whitespace. ' + 'Apollo Server will trim the key value before use.');
    }
    if (apolloConfig.key) {
        assertValidHeaderValue(apolloConfig.key);
    }
    if (apolloConfig.key) {
        apolloConfig.keyHash = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha512').update(apolloConfig.key).digest('hex');
    }
    if (input?.graphRef) {
        apolloConfig.graphRef = input.graphRef;
    } else if (APOLLO_GRAPH_REF) {
        apolloConfig.graphRef = APOLLO_GRAPH_REF;
    }
    const graphId = input?.graphId ?? APOLLO_GRAPH_ID;
    const graphVariant = input?.graphVariant ?? APOLLO_GRAPH_VARIANT;
    if (apolloConfig.graphRef) {
        if (graphId) {
            throw new Error('Cannot specify both graph ref and graph ID. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph ID.');
        }
        if (graphVariant) {
            throw new Error('Cannot specify both graph ref and graph variant. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph variant.');
        }
    } else if (graphId) {
        apolloConfig.graphRef = graphVariant ? `${graphId}@${graphVariant}` : graphId;
    }
    return apolloConfig;
}
function assertValidHeaderValue(value) {
    const invalidHeaderCharRegex = /[^\t\x20-\x7e\x80-\xff]/g;
    if (invalidHeaderCharRegex.test(value)) {
        const invalidChars = value.match(invalidHeaderCharRegex);
        throw new Error(`The API key provided to Apollo Server contains characters which are invalid as HTTP header values. The following characters found in the key are invalid: ${invalidChars.join(', ')}. Valid header values may only contain ASCII visible characters. If you think there is an issue with your key, please contact Apollo support.`);
    }
} //# sourceMappingURL=determineApolloConfig.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApolloServerErrorCode": (()=>ApolloServerErrorCode),
    "ApolloServerValidationErrorCode": (()=>ApolloServerValidationErrorCode),
    "unwrapResolverError": (()=>unwrapResolverError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
;
var ApolloServerErrorCode;
(function(ApolloServerErrorCode) {
    ApolloServerErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ApolloServerErrorCode["GRAPHQL_PARSE_FAILED"] = "GRAPHQL_PARSE_FAILED";
    ApolloServerErrorCode["GRAPHQL_VALIDATION_FAILED"] = "GRAPHQL_VALIDATION_FAILED";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_FOUND"] = "PERSISTED_QUERY_NOT_FOUND";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_SUPPORTED"] = "PERSISTED_QUERY_NOT_SUPPORTED";
    ApolloServerErrorCode["BAD_USER_INPUT"] = "BAD_USER_INPUT";
    ApolloServerErrorCode["OPERATION_RESOLUTION_FAILURE"] = "OPERATION_RESOLUTION_FAILURE";
    ApolloServerErrorCode["BAD_REQUEST"] = "BAD_REQUEST";
})(ApolloServerErrorCode || (ApolloServerErrorCode = {}));
var ApolloServerValidationErrorCode;
(function(ApolloServerValidationErrorCode) {
    ApolloServerValidationErrorCode["INTROSPECTION_DISABLED"] = "INTROSPECTION_DISABLED";
    ApolloServerValidationErrorCode["MAX_RECURSIVE_SELECTIONS_EXCEEDED"] = "MAX_RECURSIVE_SELECTIONS_EXCEEDED";
})(ApolloServerValidationErrorCode || (ApolloServerValidationErrorCode = {}));
function unwrapResolverError(error) {
    if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] && error.path && error.originalError) {
        return error.originalError;
    }
    return error;
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HeaderMap": (()=>HeaderMap)
});
class HeaderMap extends Map {
    constructor(){
        super(...arguments);
        this.__identity = Symbol('HeaderMap');
    }
    set(key, value) {
        return super.set(key.toLowerCase(), value);
    }
    get(key) {
        return super.get(key.toLowerCase());
    }
    delete(key) {
        return super.delete(key.toLowerCase());
    }
    has(key) {
        return super.has(key.toLowerCase());
    }
} //# sourceMappingURL=HeaderMap.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BadRequestError": (()=>BadRequestError),
    "OperationResolutionError": (()=>OperationResolutionError),
    "PersistedQueryNotFoundError": (()=>PersistedQueryNotFoundError),
    "PersistedQueryNotSupportedError": (()=>PersistedQueryNotSupportedError),
    "SyntaxError": (()=>SyntaxError),
    "UserInputError": (()=>UserInputError),
    "ValidationError": (()=>ValidationError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
class GraphQLErrorWithCode extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] {
    constructor(message, code, options){
        super(message, {
            ...options,
            extensions: {
                ...options?.extensions,
                code
            }
        });
        this.name = this.constructor.name;
    }
}
class SyntaxError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].GRAPHQL_PARSE_FAILED, {
            source: graphqlError.source,
            positions: graphqlError.positions,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError
        });
    }
}
class ValidationError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].GRAPHQL_VALIDATION_FAILED, {
            nodes: graphqlError.nodes,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError.originalError ?? graphqlError
        });
    }
}
const getPersistedQueryErrorHttp = ()=>({
        status: 200,
        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
            [
                'cache-control',
                'private, no-cache, must-revalidate'
            ]
        ])
    });
class PersistedQueryNotFoundError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotFound', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].PERSISTED_QUERY_NOT_FOUND, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
class PersistedQueryNotSupportedError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotSupported', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].PERSISTED_QUERY_NOT_SUPPORTED, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
class UserInputError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_USER_INPUT, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: graphqlError.extensions
        });
    }
}
class OperationResolutionError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].OPERATION_RESOLUTION_FAILURE, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...graphqlError.extensions
            }
        });
    }
}
class BadRequestError extends GraphQLErrorWithCode {
    constructor(message, options){
        super(message, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_REQUEST, {
            ...options,
            extensions: {
                http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400),
                ...options?.extensions
            }
        });
    }
} //# sourceMappingURL=internalErrorClasses.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeHTTPGraphQLHead": (()=>mergeHTTPGraphQLHead),
    "newHTTPGraphQLHead": (()=>newHTTPGraphQLHead),
    "prettyJSONStringify": (()=>prettyJSONStringify),
    "runHttpQuery": (()=>runHttpQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
;
function fieldIfString(o, fieldName) {
    const value = o[fieldName];
    if (typeof value === 'string') {
        return value;
    }
    return undefined;
}
function searchParamIfSpecifiedOnce(searchParams, paramName) {
    const values = searchParams.getAll(paramName);
    switch(values.length){
        case 0:
            return undefined;
        case 1:
            return values[0];
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The '${paramName}' search parameter may only be specified once.`);
    }
}
function jsonParsedSearchParamIfSpecifiedOnce(searchParams, fieldName) {
    const value = searchParamIfSpecifiedOnce(searchParams, fieldName);
    if (value === undefined) {
        return undefined;
    }
    let hopefullyRecord;
    try {
        hopefullyRecord = JSON.parse(value);
    } catch  {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The ${fieldName} search parameter contains invalid JSON.`);
    }
    if (!isStringRecord(hopefullyRecord)) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`The ${fieldName} search parameter should contain a JSON-encoded object.`);
    }
    return hopefullyRecord;
}
function fieldIfRecord(o, fieldName) {
    const value = o[fieldName];
    if (isStringRecord(value)) {
        return value;
    }
    return undefined;
}
function isStringRecord(o) {
    return !!o && typeof o === 'object' && !Buffer.isBuffer(o) && !Array.isArray(o);
}
function isNonEmptyStringRecord(o) {
    return isStringRecord(o) && Object.keys(o).length > 0;
}
function ensureQueryIsStringOrMissing(query) {
    if (!query || typeof query === 'string') {
        return;
    }
    if (query.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]("GraphQL queries must be strings. It looks like you're sending the " + 'internal graphql-js representation of a parsed query in your ' + 'request instead of a request in the GraphQL query language. You ' + 'can convert an AST to a string using the `print` function from ' + '`graphql`, or use a client like `apollo-client` which converts ' + 'the internal representation to a string for you.');
    } else {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('GraphQL queries must be strings.');
    }
}
async function runHttpQuery({ server, httpRequest, contextValue, schemaDerivedData, internals, sharedResponseHTTPGraphQLHead }) {
    let graphQLRequest;
    switch(httpRequest.method){
        case 'POST':
            {
                if (!isNonEmptyStringRecord(httpRequest.body)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('POST body missing, invalid Content-Type, or JSON object has no keys.');
                }
                ensureQueryIsStringOrMissing(httpRequest.body.query);
                if (typeof httpRequest.body.variables === 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`variables` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if (typeof httpRequest.body.extensions === 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`extensions` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if ('extensions' in httpRequest.body && httpRequest.body.extensions !== null && !isStringRecord(httpRequest.body.extensions)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`extensions` in a POST body must be an object if provided.');
                }
                if ('variables' in httpRequest.body && httpRequest.body.variables !== null && !isStringRecord(httpRequest.body.variables)) {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`variables` in a POST body must be an object if provided.');
                }
                if ('operationName' in httpRequest.body && httpRequest.body.operationName !== null && typeof httpRequest.body.operationName !== 'string') {
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('`operationName` in a POST body must be a string if provided.');
                }
                graphQLRequest = {
                    query: fieldIfString(httpRequest.body, 'query'),
                    operationName: fieldIfString(httpRequest.body, 'operationName'),
                    variables: fieldIfRecord(httpRequest.body, 'variables'),
                    extensions: fieldIfRecord(httpRequest.body, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        case 'GET':
            {
                const searchParams = new URLSearchParams(httpRequest.search);
                graphQLRequest = {
                    query: searchParamIfSpecifiedOnce(searchParams, 'query'),
                    operationName: searchParamIfSpecifiedOnce(searchParams, 'operationName'),
                    variables: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'variables'),
                    extensions: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Apollo Server supports only GET/POST requests.', {
                extensions: {
                    http: {
                        status: 405,
                        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                            [
                                'allow',
                                'GET, POST'
                            ]
                        ])
                    }
                }
            });
    }
    const graphQLResponse = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["internalExecuteOperation"])({
        server,
        graphQLRequest,
        internals,
        schemaDerivedData,
        sharedResponseHTTPGraphQLHead
    }, {
        contextValue
    });
    if (graphQLResponse.body.kind === 'single') {
        if (!graphQLResponse.http.headers.get('content-type')) {
            const contentType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chooseContentTypeForSingleResultResponse"])(httpRequest);
            if (contentType === null) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`An 'accept' header was provided for this request which does not accept ` + `${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].APPLICATION_JSON} or ${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].APPLICATION_GRAPHQL_RESPONSE_JSON}`, {
                    extensions: {
                        http: {
                            status: 406
                        }
                    }
                });
            }
            graphQLResponse.http.headers.set('content-type', contentType);
        }
        return {
            ...graphQLResponse.http,
            body: {
                kind: 'complete',
                string: await internals.stringifyResult(orderExecutionResultFields(graphQLResponse.body.singleResult))
            }
        };
    }
    const acceptHeader = httpRequest.headers.get('accept');
    if (!(acceptHeader && new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
        headers: {
            accept: httpRequest.headers.get('accept')
        }
    }).mediaType([
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_NO_DEFER_SPEC,
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_EXPERIMENTAL
    ]) === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MEDIA_TYPES"].MULTIPART_MIXED_EXPERIMENTAL)) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Apollo server received an operation that uses incremental delivery ' + '(@defer or @stream), but the client does not accept multipart/mixed ' + 'HTTP responses. To enable incremental delivery support, add the HTTP ' + "header 'Accept: multipart/mixed; deferSpec=20220824'.", {
            extensions: {
                http: {
                    status: 406
                }
            }
        });
    }
    graphQLResponse.http.headers.set('content-type', 'multipart/mixed; boundary="-"; deferSpec=20220824');
    return {
        ...graphQLResponse.http,
        body: {
            kind: 'chunked',
            asyncIterator: writeMultipartBody(graphQLResponse.body.initialResult, graphQLResponse.body.subsequentResults)
        }
    };
}
async function* writeMultipartBody(initialResult, subsequentResults) {
    yield `\r\n---\r\ncontent-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderInitialIncrementalExecutionResultFields(initialResult))}\r\n---${initialResult.hasNext ? '' : '--'}\r\n`;
    for await (const result of subsequentResults){
        yield `content-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderSubsequentIncrementalExecutionResultFields(result))}\r\n---${result.hasNext ? '' : '--'}\r\n`;
    }
}
function orderExecutionResultFields(result) {
    return {
        errors: result.errors,
        data: result.data,
        extensions: result.extensions
    };
}
function orderInitialIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        errors: result.errors,
        data: result.data,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderSubsequentIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderIncrementalResultFields(incremental) {
    return incremental?.map((i)=>({
            hasNext: i.hasNext,
            errors: i.errors,
            path: i.path,
            label: i.label,
            data: i.data,
            items: i.items,
            extensions: i.extensions
        }));
}
function prettyJSONStringify(value) {
    return JSON.stringify(value) + '\n';
}
function newHTTPGraphQLHead(status) {
    return {
        status,
        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]()
    };
}
function mergeHTTPGraphQLHead(target, source) {
    if (source.status) {
        target.status = source.status;
    }
    if (source.headers) {
        for (const [name, value] of source.headers){
            target.headers.set(name, value);
        }
    }
} //# sourceMappingURL=runHttpQuery.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureError": (()=>ensureError),
    "ensureGraphQLError": (()=>ensureGraphQLError),
    "normalizeAndFormatErrors": (()=>normalizeAndFormatErrors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
function normalizeAndFormatErrors(errors, options = {}) {
    const formatError = options.formatError ?? ((error)=>error);
    const httpFromErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])();
    return {
        httpFromErrors,
        formattedErrors: errors.map((error)=>{
            try {
                return formatError(enrichError(error), error);
            } catch (formattingError) {
                if (options.includeStacktraceInErrorResponses) {
                    return enrichError(formattingError);
                } else {
                    return {
                        message: 'Internal server error',
                        extensions: {
                            code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].INTERNAL_SERVER_ERROR
                        }
                    };
                }
            }
        })
    };
    "TURBOPACK unreachable";
    function enrichError(maybeError) {
        const graphqlError = ensureGraphQLError(maybeError);
        const extensions = {
            ...graphqlError.extensions,
            code: graphqlError.extensions.code ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].INTERNAL_SERVER_ERROR
        };
        if (isPartialHTTPGraphQLHead(extensions.http)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(httpFromErrors, {
                headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"](),
                ...extensions.http
            });
            delete extensions.http;
        }
        if (options.includeStacktraceInErrorResponses) {
            extensions.stacktrace = graphqlError.stack?.split('\n');
        }
        return {
            ...graphqlError.toJSON(),
            extensions
        };
    }
}
function ensureError(maybeError) {
    return maybeError instanceof Error ? maybeError : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('Unexpected error value: ' + String(maybeError));
}
function ensureGraphQLError(maybeError, messagePrefixIfNotGraphQLError = '') {
    const error = ensureError(maybeError);
    return error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] ? error : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](messagePrefixIfNotGraphQLError + error.message, {
        originalError: error
    });
}
function isPartialHTTPGraphQLHead(x) {
    return !!x && typeof x === 'object' && (!('status' in x) || typeof x.status === 'number') && (!('headers' in x) || x.headers instanceof Map);
} //# sourceMappingURL=errorNormalize.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/httpBatching.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "runPotentiallyBatchedHttpQuery": (()=>runPotentiallyBatchedHttpQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
;
;
async function runBatchedHttpQuery({ server, batchRequest, body, contextValue, schemaDerivedData, internals }) {
    if (body.length === 0) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('No operations found in request.');
    }
    const sharedResponseHTTPGraphQLHead = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])();
    const responseBodies = await Promise.all(body.map(async (bodyPiece)=>{
        const singleRequest = {
            ...batchRequest,
            body: bodyPiece
        };
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runHttpQuery"])({
            server,
            httpRequest: singleRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead
        });
        if (response.body.kind === 'chunked') {
            throw Error('Incremental delivery is not implemented for batch requests');
        }
        return response.body.string;
    }));
    return {
        ...sharedResponseHTTPGraphQLHead,
        body: {
            kind: 'complete',
            string: `[${responseBodies.join(',')}]`
        }
    };
}
async function runPotentiallyBatchedHttpQuery(server, httpGraphQLRequest, contextValue, schemaDerivedData, internals) {
    if (!(httpGraphQLRequest.method === 'POST' && Array.isArray(httpGraphQLRequest.body))) {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runHttpQuery"])({
            server,
            httpRequest: httpGraphQLRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead: null
        });
    }
    if (internals.allowBatchedHttpRequests) {
        return await runBatchedHttpQuery({
            server,
            batchRequest: httpGraphQLRequest,
            body: httpGraphQLRequest.body,
            contextValue,
            schemaDerivedData,
            internals
        });
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('Operation batching disabled.');
} //# sourceMappingURL=httpBatching.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/internalPlugin.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "internalPlugin": (()=>internalPlugin),
    "pluginIsInternal": (()=>pluginIsInternal)
});
function internalPlugin(p) {
    return p;
}
function pluginIsInternal(plugin) {
    return '__internal_plugin_id__' in plugin;
} //# sourceMappingURL=internalPlugin.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/preventCsrf.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "preventCsrf": (()=>preventCsrf),
    "recommendedCsrfPreventionRequestHeaders": (()=>recommendedCsrfPreventionRequestHeaders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$mimetype$2f$lib$2f$mime$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/whatwg-mimetype/lib/mime-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
;
;
const recommendedCsrfPreventionRequestHeaders = [
    'x-apollo-operation-name',
    'apollo-require-preflight'
];
const NON_PREFLIGHTED_CONTENT_TYPES = [
    'application/x-www-form-urlencoded',
    'multipart/form-data',
    'text/plain'
];
function preventCsrf(headers, csrfPreventionRequestHeaders) {
    const contentType = headers.get('content-type');
    if (contentType !== undefined) {
        const contentTypeParsed = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$whatwg$2d$mimetype$2f$lib$2f$mime$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].parse(contentType);
        if (contentTypeParsed === null) {
            return;
        }
        if (!NON_PREFLIGHTED_CONTENT_TYPES.includes(contentTypeParsed.essence)) {
            return;
        }
    }
    if (csrfPreventionRequestHeaders.some((header)=>{
        const value = headers.get(header);
        return value !== undefined && value.length > 0;
    })) {
        return;
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`This operation has been blocked as a potential Cross-Site Request Forgery ` + `(CSRF). Please either specify a 'content-type' header (with a type that ` + `is not one of ${NON_PREFLIGHTED_CONTENT_TYPES.join(', ')}) or provide ` + `a non-empty value for one of the following headers: ${csrfPreventionRequestHeaders.join(', ')}\n`);
} //# sourceMappingURL=preventCsrf.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/schemaInstrumentation.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "enablePluginsForSchemaResolvers": (()=>enablePluginsForSchemaResolvers),
    "pluginsEnabledForSchemaResolvers": (()=>pluginsEnabledForSchemaResolvers),
    "symbolExecutionDispatcherWillResolveField": (()=>symbolExecutionDispatcherWillResolveField),
    "symbolUserFieldResolver": (()=>symbolUserFieldResolver),
    "whenResultIsFinished": (()=>whenResultIsFinished)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/definition.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/execution/execute.mjs [app-route] (ecmascript)");
;
const symbolExecutionDispatcherWillResolveField = Symbol('apolloServerExecutionDispatcherWillResolveField');
const symbolUserFieldResolver = Symbol('apolloServerUserFieldResolver');
const symbolPluginsEnabled = Symbol('apolloServerPluginsEnabled');
function enablePluginsForSchemaResolvers(schema) {
    if (pluginsEnabledForSchemaResolvers(schema)) {
        return schema;
    }
    Object.defineProperty(schema, symbolPluginsEnabled, {
        value: true
    });
    const typeMap = schema.getTypeMap();
    Object.values(typeMap).forEach((type)=>{
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getNamedType"])(type).name.startsWith('__') && type instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$definition$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLObjectType"]) {
            const fields = type.getFields();
            Object.values(fields).forEach((field)=>{
                wrapField(field);
            });
        }
    });
    return schema;
}
function pluginsEnabledForSchemaResolvers(schema) {
    return !!schema[symbolPluginsEnabled];
}
function wrapField(field) {
    const originalFieldResolve = field.resolve;
    field.resolve = (source, args, contextValue, info)=>{
        const willResolveField = contextValue?.[symbolExecutionDispatcherWillResolveField];
        const userFieldResolver = contextValue?.[symbolUserFieldResolver];
        const didResolveField = typeof willResolveField === 'function' && willResolveField({
            source,
            args,
            contextValue,
            info
        });
        const fieldResolver = originalFieldResolve || userFieldResolver || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultFieldResolver"];
        try {
            const result = fieldResolver(source, args, contextValue, info);
            if (typeof didResolveField === 'function') {
                whenResultIsFinished(result, didResolveField);
            }
            return result;
        } catch (error) {
            if (typeof didResolveField === 'function') {
                didResolveField(error);
            }
            throw error;
        }
    };
}
function isPromise(x) {
    return x && typeof x.then === 'function';
}
function whenResultIsFinished(result, callback) {
    if (isPromise(result)) {
        result.then((r)=>whenResultIsFinished(r, callback), (err)=>callback(err));
    } else if (Array.isArray(result)) {
        if (result.some(isPromise)) {
            Promise.all(result).then((r)=>callback(null, r), (err)=>callback(err));
        } else {
            callback(null, result);
        }
    } else {
        callback(null, result);
    }
} //# sourceMappingURL=schemaInstrumentation.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isDefined": (()=>isDefined)
});
function isDefined(t) {
    return t != null;
} //# sourceMappingURL=isDefined.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/invokeHooks.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "invokeDidStartHook": (()=>invokeDidStartHook),
    "invokeHooksUntilDefinedAndNonNull": (()=>invokeHooksUntilDefinedAndNonNull),
    "invokeSyncDidStartHook": (()=>invokeSyncDidStartHook)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
;
async function invokeDidStartHook(targets, hook) {
    const didEndHooks = (await Promise.all(targets.map((target)=>hook(target)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    didEndHooks.reverse();
    return async (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
function invokeSyncDidStartHook(targets, hook) {
    const didEndHooks = targets.map((target)=>hook(target)).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    didEndHooks.reverse();
    return (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
async function invokeHooksUntilDefinedAndNonNull(targets, hook) {
    for (const target of targets){
        const value = await hook(target);
        if (value != null) {
            return value;
        }
    }
    return null;
} //# sourceMappingURL=invokeHooks.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makeGatewayGraphQLRequestContext": (()=>makeGatewayGraphQLRequestContext)
});
function makeGatewayGraphQLRequestContext(as4RequestContext, server, internals) {
    const request = {};
    if ('query' in as4RequestContext.request) {
        request.query = as4RequestContext.request.query;
    }
    if ('operationName' in as4RequestContext.request) {
        request.operationName = as4RequestContext.request.operationName;
    }
    if ('variables' in as4RequestContext.request) {
        request.variables = as4RequestContext.request.variables;
    }
    if ('extensions' in as4RequestContext.request) {
        request.extensions = as4RequestContext.request.extensions;
    }
    if (as4RequestContext.request.http) {
        const as4http = as4RequestContext.request.http;
        const needQuestion = as4http.search !== '' && !as4http.search.startsWith('?');
        request.http = {
            method: as4http.method,
            url: `https://unknown-url.invalid/${needQuestion ? '?' : ''}${as4http.search}`,
            headers: new FetcherHeadersForHeaderMap(as4http.headers)
        };
    }
    const response = {
        http: {
            headers: new FetcherHeadersForHeaderMap(as4RequestContext.response.http.headers),
            get status () {
                return as4RequestContext.response.http.status;
            },
            set status (newStatus){
                as4RequestContext.response.http.status = newStatus;
            }
        }
    };
    return {
        request,
        response,
        logger: server.logger,
        schema: as4RequestContext.schema,
        schemaHash: 'schemaHash no longer exists in Apollo Server 4',
        context: as4RequestContext.contextValue,
        cache: server.cache,
        queryHash: as4RequestContext.queryHash,
        document: as4RequestContext.document,
        source: as4RequestContext.source,
        operationName: as4RequestContext.operationName,
        operation: as4RequestContext.operation,
        errors: as4RequestContext.errors,
        metrics: as4RequestContext.metrics,
        debug: internals.includeStacktraceInErrorResponses,
        overallCachePolicy: as4RequestContext.overallCachePolicy,
        requestIsBatched: as4RequestContext.requestIsBatched
    };
}
class FetcherHeadersForHeaderMap {
    constructor(map){
        this.map = map;
    }
    append(name, value) {
        if (this.map.has(name)) {
            this.map.set(name, this.map.get(name) + ', ' + value);
        } else {
            this.map.set(name, value);
        }
    }
    delete(name) {
        this.map.delete(name);
    }
    get(name) {
        return this.map.get(name) ?? null;
    }
    has(name) {
        return this.map.has(name);
    }
    set(name, value) {
        this.map.set(name, value);
    }
    entries() {
        return this.map.entries();
    }
    keys() {
        return this.map.keys();
    }
    values() {
        return this.map.values();
    }
    [Symbol.iterator]() {
        return this.map.entries();
    }
} //# sourceMappingURL=makeGatewayGraphQLRequestContext.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/incrementalDeliveryPolyfill.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "executeIncrementally": (()=>executeIncrementally)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/execution/execute.mjs [app-route] (ecmascript)");
;
let graphqlExperimentalExecuteIncrementally = undefined;
async function tryToLoadGraphQL17() {
    if (graphqlExperimentalExecuteIncrementally !== undefined) {
        return;
    }
    const graphql = await __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
    if ('experimentalExecuteIncrementally' in graphql) {
        graphqlExperimentalExecuteIncrementally = graphql.experimentalExecuteIncrementally;
    } else {
        graphqlExperimentalExecuteIncrementally = null;
    }
}
async function executeIncrementally(args) {
    await tryToLoadGraphQL17();
    if (graphqlExperimentalExecuteIncrementally) {
        return graphqlExperimentalExecuteIncrementally(args);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$execution$2f$execute$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["execute"])(args);
} //# sourceMappingURL=incrementalDeliveryPolyfill.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/requestPipeline.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "APQ_CACHE_PREFIX": (()=>APQ_CACHE_PREFIX),
    "processGraphQLRequest": (()=>processGraphQLRequest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$specifiedRules$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/validation/specifiedRules.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$getOperationAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/getOperationAST.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/validation/validate.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/schemaInstrumentation.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalErrorClasses.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/invokeHooks.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$makeGatewayGraphQLRequestContext$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$incrementalDeliveryPolyfill$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/incrementalDeliveryPolyfill.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const APQ_CACHE_PREFIX = 'apq:';
function computeQueryHash(query) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update(query).digest('hex');
}
function isBadUserInputGraphQLError(error) {
    return error.nodes?.length === 1 && error.nodes[0].kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].VARIABLE_DEFINITION && (error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" got invalid value `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of required type `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of non-null type `));
}
async function processGraphQLRequest(schemaDerivedData, server, internals, requestContext) {
    const requestListeners = (await Promise.all(internals.plugins.map((p)=>p.requestDidStart?.(requestContext)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
    const request = requestContext.request;
    let { query, extensions } = request;
    let queryHash;
    requestContext.metrics.persistedQueryHit = false;
    requestContext.metrics.persistedQueryRegister = false;
    if (extensions?.persistedQuery) {
        if (!internals.persistedQueries) {
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PersistedQueryNotSupportedError"]()
            ]);
        } else if (extensions.persistedQuery.version !== 1) {
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('Unsupported persisted query version', {
                    extensions: {
                        http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400)
                    }
                })
            ]);
        }
        queryHash = extensions.persistedQuery.sha256Hash;
        if (query === undefined) {
            query = await internals.persistedQueries.cache.get(queryHash);
            if (query) {
                requestContext.metrics.persistedQueryHit = true;
            } else {
                return await sendErrorResponse([
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PersistedQueryNotFoundError"]()
                ]);
            }
        } else {
            const computedQueryHash = computeQueryHash(query);
            if (queryHash !== computedQueryHash) {
                return await sendErrorResponse([
                    new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('provided sha does not match query', {
                        extensions: {
                            http: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])(400)
                        }
                    })
                ]);
            }
            requestContext.metrics.persistedQueryRegister = true;
        }
    } else if (query) {
        queryHash = computeQueryHash(query);
    } else {
        return await sendErrorResponse([
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"]('GraphQL operations must contain a non-empty `query` or a `persistedQuery` extension.')
        ]);
    }
    requestContext.queryHash = queryHash;
    requestContext.source = query;
    await Promise.all(requestListeners.map((l)=>l.didResolveSource?.(requestContext)));
    if (schemaDerivedData.documentStore) {
        try {
            requestContext.document = await schemaDerivedData.documentStore.get(schemaDerivedData.documentStoreKeyPrefix + queryHash);
        } catch (err) {
            server.logger.warn('An error occurred while attempting to read from the documentStore. ' + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(err).message);
        }
    }
    if (!requestContext.document) {
        const parsingDidEnd = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeDidStartHook"])(requestListeners, async (l)=>l.parsingDidStart?.(requestContext));
        try {
            requestContext.document = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(query, internals.parseOptions);
        } catch (syntaxMaybeError) {
            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(syntaxMaybeError);
            await parsingDidEnd(error);
            return await sendErrorResponse([
                new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SyntaxError"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(error))
            ]);
        }
        await parsingDidEnd();
        if (internals.dangerouslyDisableValidation !== true) {
            const validationDidEnd = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeDidStartHook"])(requestListeners, async (l)=>l.validationDidStart?.(requestContext));
            let validationErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validate"])(schemaDerivedData.schema, requestContext.document, [
                ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$specifiedRules$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["specifiedRules"],
                ...internals.validationRules
            ]);
            if (validationErrors.length === 0 && internals.laterValidationRules) {
                validationErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$validation$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validate"])(schemaDerivedData.schema, requestContext.document, internals.laterValidationRules);
            }
            if (validationErrors.length === 0) {
                await validationDidEnd();
            } else {
                await validationDidEnd(validationErrors);
                return await sendErrorResponse(validationErrors.map((error)=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ValidationError"](error)));
            }
        }
        if (schemaDerivedData.documentStore) {
            Promise.resolve(schemaDerivedData.documentStore.set(schemaDerivedData.documentStoreKeyPrefix + queryHash, requestContext.document)).catch((err)=>server.logger.warn('Could not store validated document. ' + err?.message || err));
        }
    }
    const operation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$getOperationAST$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOperationAST"])(requestContext.document, request.operationName);
    requestContext.operation = operation || undefined;
    requestContext.operationName = operation?.name?.value || null;
    if (request.http?.method === 'GET' && operation?.operation && operation.operation !== 'query') {
        return await sendErrorResponse([
            new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BadRequestError"](`GET requests only support query operations, not ${operation.operation} operations`, {
                extensions: {
                    http: {
                        status: 405,
                        headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                            [
                                'allow',
                                'POST'
                            ]
                        ])
                    }
                }
            })
        ]);
    }
    try {
        await Promise.all(requestListeners.map((l)=>l.didResolveOperation?.(requestContext)));
    } catch (err) {
        return await sendErrorResponse([
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(err)
        ]);
    }
    if (requestContext.metrics.persistedQueryRegister && internals.persistedQueries) {
        const ttl = internals.persistedQueries?.ttl;
        Promise.resolve(internals.persistedQueries.cache.set(queryHash, query, ttl !== undefined ? {
            ttl: internals.persistedQueries?.ttl
        } : undefined)).catch(server.logger.warn);
    }
    const responseFromPlugin = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeHooksUntilDefinedAndNonNull"])(requestListeners, async (l)=>await l.responseForOperation?.(requestContext));
    if (responseFromPlugin !== null) {
        requestContext.response.body = responseFromPlugin.body;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, responseFromPlugin.http);
    } else {
        const executionListeners = (await Promise.all(requestListeners.map((l)=>l.executionDidStart?.(requestContext)))).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
        executionListeners.reverse();
        if (executionListeners.some((l)=>l.willResolveField)) {
            const invokeWillResolveField = (...args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$invokeHooks$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["invokeSyncDidStartHook"])(executionListeners, (l)=>l.willResolveField?.(...args));
            Object.defineProperty(requestContext.contextValue, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["symbolExecutionDispatcherWillResolveField"], {
                value: invokeWillResolveField
            });
            if (internals.fieldResolver) {
                Object.defineProperty(requestContext.contextValue, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["symbolUserFieldResolver"], {
                    value: internals.fieldResolver
                });
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaInstrumentation$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["enablePluginsForSchemaResolvers"])(schemaDerivedData.schema);
        }
        try {
            const fullResult = await execute(requestContext);
            const result = 'singleResult' in fullResult ? fullResult.singleResult : fullResult.initialResult;
            if (!requestContext.operation) {
                if (!result.errors?.length) {
                    throw new Error('Unexpected error: Apollo Server did not resolve an operation but execute did not return errors');
                }
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OperationResolutionError"](result.errors[0]);
            }
            const resultErrors = result.errors?.map((e)=>{
                if (isBadUserInputGraphQLError(e) && e.extensions?.code == null) {
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalErrorClasses$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserInputError"](e);
                }
                return e;
            });
            if (resultErrors) {
                await didEncounterErrors(resultErrors);
            }
            const { formattedErrors, httpFromErrors } = resultErrors ? formatErrors(resultErrors) : {
                formattedErrors: undefined,
                httpFromErrors: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])()
            };
            if (internals.status400ForVariableCoercionErrors && resultErrors?.length && result.data === undefined && !httpFromErrors.status) {
                httpFromErrors.status = 400;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, httpFromErrors);
            if ('singleResult' in fullResult) {
                requestContext.response.body = {
                    kind: 'single',
                    singleResult: {
                        ...result,
                        errors: formattedErrors
                    }
                };
            } else {
                requestContext.response.body = {
                    kind: 'incremental',
                    initialResult: {
                        ...fullResult.initialResult,
                        errors: formattedErrors
                    },
                    subsequentResults: fullResult.subsequentResults
                };
            }
        } catch (executionMaybeError) {
            const executionError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(executionMaybeError);
            await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.(executionError)));
            return await sendErrorResponse([
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(executionError)
            ]);
        }
        await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.()));
    }
    await invokeWillSendResponse();
    if (!requestContext.response.body) {
        throw Error('got to end of processGraphQLRequest without setting body?');
    }
    return requestContext.response;
    "TURBOPACK unreachable";
    async function execute(requestContext) {
        const { request, document } = requestContext;
        if (internals.__testing_incrementalExecutionResults) {
            return internals.__testing_incrementalExecutionResults;
        } else if (internals.gatewayExecutor) {
            const result = await internals.gatewayExecutor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$makeGatewayGraphQLRequestContext$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeGatewayGraphQLRequestContext"])(requestContext, server, internals));
            return {
                singleResult: result
            };
        } else {
            const resultOrResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$incrementalDeliveryPolyfill$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeIncrementally"])({
                schema: schemaDerivedData.schema,
                document,
                rootValue: typeof internals.rootValue === 'function' ? internals.rootValue(document) : internals.rootValue,
                contextValue: requestContext.contextValue,
                variableValues: request.variables,
                operationName: request.operationName,
                fieldResolver: internals.fieldResolver
            });
            if ('initialResult' in resultOrResults) {
                return {
                    initialResult: resultOrResults.initialResult,
                    subsequentResults: formatErrorsInSubsequentResults(resultOrResults.subsequentResults)
                };
            } else {
                return {
                    singleResult: resultOrResults
                };
            }
        }
    }
    async function* formatErrorsInSubsequentResults(results) {
        for await (const result of results){
            const payload = result.incremental ? {
                ...result,
                incremental: await seriesAsyncMap(result.incremental, async (incrementalResult)=>{
                    const { errors } = incrementalResult;
                    if (errors) {
                        await Promise.all(requestListeners.map((l)=>l.didEncounterSubsequentErrors?.(requestContext, errors)));
                        return {
                            ...incrementalResult,
                            errors: formatErrors(errors).formattedErrors
                        };
                    }
                    return incrementalResult;
                })
            } : result;
            await Promise.all(requestListeners.map((l)=>l.willSendSubsequentPayload?.(requestContext, payload)));
            yield payload;
        }
    }
    async function invokeWillSendResponse() {
        await Promise.all(requestListeners.map((l)=>l.willSendResponse?.(requestContext)));
    }
    async function didEncounterErrors(errors) {
        requestContext.errors = errors;
        return await Promise.all(requestListeners.map((l)=>l.didEncounterErrors?.(requestContext)));
    }
    async function sendErrorResponse(errors) {
        await didEncounterErrors(errors);
        const { formattedErrors, httpFromErrors } = formatErrors(errors);
        requestContext.response.body = {
            kind: 'single',
            singleResult: {
                errors: formattedErrors
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeHTTPGraphQLHead"])(requestContext.response.http, httpFromErrors);
        if (!requestContext.response.http.status) {
            requestContext.response.http.status = 500;
        }
        await invokeWillSendResponse();
        return requestContext.response;
    }
    function formatErrors(errors) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeAndFormatErrors"])(errors, {
            formatError: internals.formatError,
            includeStacktraceInErrorResponses: internals.includeStacktraceInErrorResponses
        });
    }
}
async function seriesAsyncMap(ts, fn) {
    const us = [];
    for (const t of ts){
        const u = await fn(t);
        us.push(u);
    }
    return us;
} //# sourceMappingURL=requestPipeline.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/UnreachableCaseError.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UnreachableCaseError": (()=>UnreachableCaseError)
});
class UnreachableCaseError extends Error {
    constructor(val){
        super(`Unreachable case: ${val}`);
    }
} //# sourceMappingURL=UnreachableCaseError.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/computeCoreSchemaHash.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "computeCoreSchemaHash": (()=>computeCoreSchemaHash)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
;
function computeCoreSchemaHash(schema) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$createhash$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createHash"])('sha256').update(schema).digest('hex');
} //# sourceMappingURL=computeCoreSchemaHash.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/utils/schemaManager.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SchemaManager": (()=>SchemaManager)
});
class SchemaManager {
    constructor(options){
        this.onSchemaLoadOrUpdateListeners = new Set();
        this.isStopped = false;
        this.logger = options.logger;
        this.schemaDerivedDataProvider = options.schemaDerivedDataProvider;
        if ('gateway' in options) {
            this.modeSpecificState = {
                mode: 'gateway',
                gateway: options.gateway,
                apolloConfig: options.apolloConfig
            };
        } else {
            this.modeSpecificState = {
                mode: 'schema',
                apiSchema: options.apiSchema,
                schemaDerivedData: options.schemaDerivedDataProvider(options.apiSchema)
            };
        }
    }
    async start() {
        if (this.modeSpecificState.mode === 'gateway') {
            const gateway = this.modeSpecificState.gateway;
            if (gateway.onSchemaLoadOrUpdate) {
                this.modeSpecificState.unsubscribeFromGateway = gateway.onSchemaLoadOrUpdate((schemaContext)=>{
                    this.processSchemaLoadOrUpdateEvent(schemaContext);
                });
            } else {
                throw new Error("Unexpectedly couldn't find onSchemaLoadOrUpdate on gateway");
            }
            const config = await this.modeSpecificState.gateway.load({
                apollo: this.modeSpecificState.apolloConfig
            });
            return config.executor;
        } else {
            this.processSchemaLoadOrUpdateEvent({
                apiSchema: this.modeSpecificState.apiSchema
            }, this.modeSpecificState.schemaDerivedData);
            return null;
        }
    }
    onSchemaLoadOrUpdate(callback) {
        if (!this.schemaContext) {
            throw new Error('You must call start() before onSchemaLoadOrUpdate()');
        }
        if (!this.isStopped) {
            try {
                callback(this.schemaContext);
            } catch (e) {
                throw new Error(`An error was thrown from an 'onSchemaLoadOrUpdate' listener: ${e.message}`);
            }
        }
        this.onSchemaLoadOrUpdateListeners.add(callback);
        return ()=>{
            this.onSchemaLoadOrUpdateListeners.delete(callback);
        };
    }
    getSchemaDerivedData() {
        if (!this.schemaDerivedData) {
            throw new Error('You must call start() before getSchemaDerivedData()');
        }
        return this.schemaDerivedData;
    }
    async stop() {
        this.isStopped = true;
        if (this.modeSpecificState.mode === 'gateway') {
            this.modeSpecificState.unsubscribeFromGateway?.();
            await this.modeSpecificState.gateway.stop?.();
        }
    }
    processSchemaLoadOrUpdateEvent(schemaContext, schemaDerivedData) {
        if (!this.isStopped) {
            this.schemaDerivedData = schemaDerivedData ?? this.schemaDerivedDataProvider(schemaContext.apiSchema);
            this.schemaContext = schemaContext;
            this.onSchemaLoadOrUpdateListeners.forEach((listener)=>{
                try {
                    listener(schemaContext);
                } catch (e) {
                    this.logger.error("An error was thrown from an 'onSchemaLoadOrUpdate' listener");
                    this.logger.error(e);
                }
            });
        }
    }
} //# sourceMappingURL=schemaManager.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NoIntrospection": (()=>NoIntrospection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
;
;
const NoIntrospection = (context)=>({
        Field (node) {
            if (node.name.value === '__schema' || node.name.value === '__type') {
                context.reportError(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"]('GraphQL introspection is not allowed by Apollo Server, but the query contained __schema or __type. To enable introspection, pass introspection: true to ApolloServer in production', {
                    nodes: [
                        node
                    ],
                    extensions: {
                        validationErrorCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerValidationErrorCode"].INTROSPECTION_DISABLED
                    }
                }));
            }
        }
    }); //# sourceMappingURL=NoIntrospection.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_MAX_RECURSIVE_SELECTIONS": (()=>DEFAULT_MAX_RECURSIVE_SELECTIONS),
    "createMaxRecursiveSelectionsRule": (()=>createMaxRecursiveSelectionsRule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
;
;
const DEFAULT_MAX_RECURSIVE_SELECTIONS = 10000000;
class RecursiveSelectionValidationContext {
    constructor(selectionCountLimit, context){
        this.selectionCountLimit = selectionCountLimit;
        this.context = context;
        this.fragmentInfo = new Map();
        this.operationInfo = new Map();
        this.fragmentRecursiveSelectionCount = new Map();
    }
    getExecutionDefinitionInfo() {
        if (this.currentFragment !== undefined) {
            let entry = this.fragmentInfo.get(this.currentFragment);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.fragmentInfo.set(this.currentFragment, entry);
            }
            return entry;
        }
        if (this.currentOperation !== undefined) {
            let entry = this.operationInfo.get(this.currentOperation);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.operationInfo.set(this.currentOperation, entry);
            }
            return entry;
        }
        return undefined;
    }
    processSelection(fragmentSpreadName) {
        const definitionInfo = this.getExecutionDefinitionInfo();
        if (!definitionInfo) {
            return;
        }
        definitionInfo.selectionCount++;
        if (fragmentSpreadName !== undefined) {
            let spreadCount = (definitionInfo.fragmentSpreads.get(fragmentSpreadName) ?? 0) + 1;
            definitionInfo.fragmentSpreads.set(fragmentSpreadName, spreadCount);
        }
    }
    enterFragment(fragment) {
        this.currentFragment = fragment;
    }
    leaveFragment() {
        this.currentFragment = undefined;
    }
    enterOperation(operation) {
        this.currentOperation = operation;
    }
    leaveOperation() {
        this.currentOperation = undefined;
    }
    computeFragmentRecursiveSelectionsCount(fragment) {
        const cachedCount = this.fragmentRecursiveSelectionCount.get(fragment);
        if (cachedCount === null) {
            return 0;
        }
        if (cachedCount !== undefined) {
            return cachedCount;
        }
        this.fragmentRecursiveSelectionCount.set(fragment, null);
        const definitionInfo = this.fragmentInfo.get(fragment);
        let count = 0;
        if (definitionInfo) {
            count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
        }
        this.fragmentRecursiveSelectionCount.set(fragment, count);
        return count;
    }
    reportError(operation) {
        const operationName = operation ? `Operation "${operation}"` : 'Anonymous operation';
        this.context.reportError(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"](`${operationName} recursively requests too many selections.`, {
            nodes: [],
            extensions: {
                validationErrorCode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerValidationErrorCode"].MAX_RECURSIVE_SELECTIONS_EXCEEDED
            }
        }));
    }
    checkLimitExceeded() {
        for (const [operation, definitionInfo] of this.operationInfo){
            let count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
            if (count > this.selectionCountLimit) {
                this.reportError(operation);
            }
        }
    }
}
function createMaxRecursiveSelectionsRule(limit) {
    return (context)=>{
        const selectionContext = new RecursiveSelectionValidationContext(limit, context);
        return {
            Field () {
                selectionContext.processSelection();
            },
            InlineFragment () {
                selectionContext.processSelection();
            },
            FragmentSpread (node) {
                selectionContext.processSelection(node.name.value);
            },
            FragmentDefinition: {
                enter (node) {
                    selectionContext.enterFragment(node.name.value);
                },
                leave () {
                    selectionContext.leaveFragment();
                }
            },
            OperationDefinition: {
                enter (node) {
                    selectionContext.enterOperation(node.name?.value ?? null);
                },
                leave () {
                    selectionContext.leaveOperation();
                }
            },
            Document: {
                leave () {
                    selectionContext.checkLimitExceeded();
                }
            }
        };
    };
} //# sourceMappingURL=RecursiveSelectionsLimit.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
}}),
"[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <locals>");
}}),
"[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApolloServer": (()=>ApolloServer),
    "MEDIA_TYPES": (()=>MEDIA_TYPES),
    "chooseContentTypeForSingleResultResponse": (()=>chooseContentTypeForSingleResultResponse),
    "internalExecuteOperation": (()=>internalExecuteOperation),
    "isImplicitlyInstallablePlugin": (()=>isImplicitlyInstallablePlugin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$isnodelike$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.isnodelike/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/utils.keyvaluecache/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/resolvable.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/error/GraphQLError.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/validate.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/printer.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$printSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/utilities/printSchema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/loglevel/lib/loglevel.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$cachePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/cachePolicy.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$determineApolloConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/determineApolloConfig.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errorNormalize.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/errors/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$httpBatching$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/httpBatching.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/internalPlugin.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/preventCsrf.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/requestPipeline.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/runHttpQuery.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/UnreachableCaseError.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$computeCoreSchemaHash$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/computeCoreSchemaHash.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/isDefined.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/schemaManager.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/NoIntrospection.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function defaultLogger() {
    const loglevelLogger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getLogger('apollo-server');
    loglevelLogger.setLevel(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$loglevel$2f$lib$2f$loglevel$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].levels.INFO);
    return loglevelLogger;
}
class ApolloServer {
    constructor(config){
        const nodeEnv = config.nodeEnv ?? ("TURBOPACK compile-time value", "development") ?? '';
        this.logger = config.logger ?? defaultLogger();
        const apolloConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$determineApolloConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["determineApolloConfig"])(config.apollo, this.logger);
        const isDev = nodeEnv !== 'production';
        if (config.cache && config.cache !== 'bounded' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrefixingKeyValueCache"].prefixesAreUnnecessaryForIsolation(config.cache)) {
            throw new Error('You cannot pass a cache returned from ' + '`PrefixingKeyValueCache.cacheDangerouslyDoesNotNeedPrefixesForIsolation`' + 'to `new ApolloServer({ cache })`, because Apollo Server may use it for ' + 'multiple features whose cache keys must be distinct from each other.');
        }
        const state = config.gateway ? {
            phase: 'initialized',
            schemaManager: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SchemaManager"]({
                gateway: config.gateway,
                apolloConfig,
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        } : {
            phase: 'initialized',
            schemaManager: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$schemaManager$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SchemaManager"]({
                apiSchema: ApolloServer.constructSchema(config),
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        };
        const introspectionEnabled = config.introspection ?? isDev;
        const hideSchemaDetailsFromClientErrors = config.hideSchemaDetailsFromClientErrors ?? false;
        this.cache = config.cache === undefined || config.cache === 'bounded' ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InMemoryLRUCache"]() : config.cache;
        const maxRecursiveSelectionsRule = config.maxRecursiveSelections === true ? [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMaxRecursiveSelectionsRule"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_MAX_RECURSIVE_SELECTIONS"])
        ] : typeof config.maxRecursiveSelections === 'number' ? [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$RecursiveSelectionsLimit$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMaxRecursiveSelectionsRule"])(config.maxRecursiveSelections)
        ] : [];
        const validationRules = [
            ...introspectionEnabled ? [] : [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$validationRules$2f$NoIntrospection$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NoIntrospection"]
            ],
            ...maxRecursiveSelectionsRule
        ];
        let laterValidationRules;
        if (maxRecursiveSelectionsRule.length > 0) {
            laterValidationRules = config.validationRules;
        } else {
            validationRules.push(...config.validationRules ?? []);
        }
        this.internals = {
            formatError: config.formatError,
            rootValue: config.rootValue,
            validationRules,
            laterValidationRules,
            hideSchemaDetailsFromClientErrors,
            dangerouslyDisableValidation: config.dangerouslyDisableValidation ?? false,
            fieldResolver: config.fieldResolver,
            includeStacktraceInErrorResponses: config.includeStacktraceInErrorResponses ?? (nodeEnv !== 'production' && nodeEnv !== 'test'),
            persistedQueries: config.persistedQueries === false ? undefined : {
                ...config.persistedQueries,
                cache: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrefixingKeyValueCache"](config.persistedQueries?.cache ?? this.cache, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APQ_CACHE_PREFIX"])
            },
            nodeEnv,
            allowBatchedHttpRequests: config.allowBatchedHttpRequests ?? false,
            apolloConfig,
            plugins: config.plugins ?? [],
            parseOptions: config.parseOptions ?? {},
            state,
            stopOnTerminationSignals: config.stopOnTerminationSignals,
            gatewayExecutor: null,
            csrfPreventionRequestHeaders: config.csrfPrevention === true || config.csrfPrevention === undefined ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["recommendedCsrfPreventionRequestHeaders"] : config.csrfPrevention === false ? null : config.csrfPrevention.requestHeaders ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["recommendedCsrfPreventionRequestHeaders"],
            status400ForVariableCoercionErrors: config.status400ForVariableCoercionErrors ?? false,
            __testing_incrementalExecutionResults: config.__testing_incrementalExecutionResults,
            stringifyResult: config.stringifyResult ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prettyJSONStringify"]
        };
    }
    async start() {
        return await this._start(false);
    }
    startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests() {
        this._start(true).catch((e)=>this.logStartupError(e));
    }
    async _start(startedInBackground) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error(`You should only call 'start()' or ` + `'startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests()' ` + `once on your ApolloServer.`);
        }
        const schemaManager = this.internals.state.schemaManager;
        const barrier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        this.internals.state = {
            phase: 'starting',
            barrier,
            schemaManager,
            startedInBackground
        };
        try {
            await this.addDefaultPlugins();
            const toDispose = [];
            const executor = await schemaManager.start();
            if (executor) {
                this.internals.gatewayExecutor = executor;
            }
            toDispose.push(async ()=>{
                await schemaManager.stop();
            });
            const schemaDerivedData = schemaManager.getSchemaDerivedData();
            const service = {
                logger: this.logger,
                cache: this.cache,
                schema: schemaDerivedData.schema,
                apollo: this.internals.apolloConfig,
                startedInBackground
            };
            const taggedServerListeners = (await Promise.all(this.internals.plugins.map(async (plugin)=>({
                    serverListener: plugin.serverWillStart && await plugin.serverWillStart(service),
                    installedImplicitly: isImplicitlyInstallablePlugin(plugin) && plugin.__internal_installed_implicitly__
                })))).filter((maybeTaggedServerListener)=>typeof maybeTaggedServerListener.serverListener === 'object');
            taggedServerListeners.forEach(({ serverListener: { schemaDidLoadOrUpdate } })=>{
                if (schemaDidLoadOrUpdate) {
                    schemaManager.onSchemaLoadOrUpdate(schemaDidLoadOrUpdate);
                }
            });
            const serverWillStops = taggedServerListeners.map((l)=>l.serverListener.serverWillStop).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
            if (serverWillStops.length) {
                toDispose.push(async ()=>{
                    await Promise.all(serverWillStops.map((serverWillStop)=>serverWillStop()));
                });
            }
            const drainServerCallbacks = taggedServerListeners.map((l)=>l.serverListener.drainServer).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$isDefined$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefined"]);
            const drainServers = drainServerCallbacks.length ? async ()=>{
                await Promise.all(drainServerCallbacks.map((drainServer)=>drainServer()));
            } : null;
            let taggedServerListenersWithRenderLandingPage = taggedServerListeners.filter((l)=>l.serverListener.renderLandingPage);
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                taggedServerListenersWithRenderLandingPage = taggedServerListenersWithRenderLandingPage.filter((l)=>!l.installedImplicitly);
            }
            let landingPage = null;
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                throw Error('Only one plugin can implement renderLandingPage.');
            } else if (taggedServerListenersWithRenderLandingPage.length) {
                landingPage = await taggedServerListenersWithRenderLandingPage[0].serverListener.renderLandingPage();
            }
            const toDisposeLast = this.maybeRegisterTerminationSignalHandlers([
                'SIGINT',
                'SIGTERM'
            ], startedInBackground);
            this.internals.state = {
                phase: 'started',
                schemaManager,
                drainServers,
                landingPage,
                toDispose,
                toDisposeLast
            };
        } catch (maybeError) {
            const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
            try {
                await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.startupDidFail?.({
                        error
                    })));
            } catch (pluginError) {
                this.logger.error(`startupDidFail hook threw: ${pluginError}`);
            }
            this.internals.state = {
                phase: 'failed to start',
                error
            };
            throw error;
        } finally{
            barrier.resolve();
        }
    }
    maybeRegisterTerminationSignalHandlers(signals, startedInBackground) {
        const toDisposeLast = [];
        if (this.internals.stopOnTerminationSignals === false || this.internals.stopOnTerminationSignals === undefined && !(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$isnodelike$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNodeLike"] && this.internals.nodeEnv !== 'test' && !startedInBackground)) {
            return toDisposeLast;
        }
        let receivedSignal = false;
        const signalHandler = async (signal)=>{
            if (receivedSignal) {
                return;
            }
            receivedSignal = true;
            try {
                await this.stop();
            } catch (e) {
                this.logger.error(`stop() threw during ${signal} shutdown`);
                this.logger.error(e);
                process.exit(1);
            }
            process.kill(process.pid, signal);
        };
        signals.forEach((signal)=>{
            process.on(signal, signalHandler);
            toDisposeLast.push(async ()=>{
                process.removeListener(signal, signalHandler);
            });
        });
        return toDisposeLast;
    }
    async _ensureStarted() {
        while(true){
            switch(this.internals.state.phase){
                case 'initialized':
                    throw new Error('You need to call `server.start()` before using your Apollo Server.');
                case 'starting':
                    await this.internals.state.barrier;
                    break;
                case 'failed to start':
                    this.logStartupError(this.internals.state.error);
                    throw new Error('This data graph is missing a valid configuration. More details may be available in the server logs.');
                case 'started':
                case 'draining':
                    return this.internals.state;
                case 'stopping':
                case 'stopped':
                    this.logger.warn('A GraphQL operation was received during server shutdown. The ' + 'operation will fail. Consider draining the HTTP server on shutdown; ' + 'see https://go.apollo.dev/s/drain for details.');
                    throw new Error(`Cannot execute GraphQL operations ${this.internals.state.phase === 'stopping' ? 'while the server is stopping' : 'after the server has stopped'}.'`);
                default:
                    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnreachableCaseError"](this.internals.state);
            }
        }
    }
    assertStarted(expressionForError) {
        if (this.internals.state.phase !== 'started' && this.internals.state.phase !== 'draining' && !(this.internals.state.phase === 'starting' && this.internals.state.startedInBackground)) {
            throw new Error('You must `await server.start()` before calling `' + expressionForError + '`');
        }
    }
    logStartupError(err) {
        this.logger.error('An error occurred during Apollo Server startup. All GraphQL requests ' + 'will now fail. The startup error was: ' + (err?.message || err));
    }
    static constructSchema(config) {
        if (config.schema) {
            return config.schema;
        }
        const { typeDefs, resolvers } = config;
        const augmentedTypeDefs = Array.isArray(typeDefs) ? typeDefs : [
            typeDefs
        ];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeExecutableSchema"])({
            typeDefs: augmentedTypeDefs,
            resolvers
        });
    }
    static generateSchemaDerivedData(schema, providedDocumentStore) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$validate$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["assertValidSchema"])(schema);
        return {
            schema,
            documentStore: providedDocumentStore === undefined ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$utils$2e$keyvaluecache$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["InMemoryLRUCache"]() : providedDocumentStore,
            documentStoreKeyPrefix: providedDocumentStore ? `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$computeCoreSchemaHash$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["computeCoreSchemaHash"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$utilities$2f$printSchema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printSchema"])(schema))}:` : ''
        };
    }
    async stop() {
        switch(this.internals.state.phase){
            case 'initialized':
            case 'starting':
            case 'failed to start':
                throw Error('apolloServer.stop() should only be called after `await apolloServer.start()` has succeeded');
            case 'stopped':
                if (this.internals.state.stopError) {
                    throw this.internals.state.stopError;
                }
                return;
            case 'stopping':
            case 'draining':
                {
                    await this.internals.state.barrier;
                    const state = this.internals.state;
                    if (state.phase !== 'stopped') {
                        throw Error(`Surprising post-stopping state ${state.phase}`);
                    }
                    if (state.stopError) {
                        throw state.stopError;
                    }
                    return;
                }
            case 'started':
                break;
            default:
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$UnreachableCaseError$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UnreachableCaseError"](this.internals.state);
        }
        const barrier = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$resolvable$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { schemaManager, drainServers, landingPage, toDispose, toDisposeLast } = this.internals.state;
        this.internals.state = {
            phase: 'draining',
            barrier,
            schemaManager,
            landingPage
        };
        try {
            await drainServers?.();
            this.internals.state = {
                phase: 'stopping',
                barrier
            };
            await Promise.all([
                ...toDispose
            ].map((dispose)=>dispose()));
            await Promise.all([
                ...toDisposeLast
            ].map((dispose)=>dispose()));
        } catch (stopError) {
            this.internals.state = {
                phase: 'stopped',
                stopError: stopError
            };
            barrier.resolve();
            throw stopError;
        }
        this.internals.state = {
            phase: 'stopped',
            stopError: null
        };
    }
    async addDefaultPlugins() {
        const { plugins, apolloConfig, nodeEnv, hideSchemaDetailsFromClientErrors } = this.internals;
        const isDev = nodeEnv !== 'production';
        const alreadyHavePluginWithInternalId = (id)=>plugins.some((p)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pluginIsInternal"])(p) && p.__internal_plugin_id__ === id);
        const pluginsByInternalID = new Map();
        for (const p of plugins){
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$internalPlugin$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pluginIsInternal"])(p)) {
                const id = p.__internal_plugin_id__;
                if (!pluginsByInternalID.has(id)) {
                    pluginsByInternalID.set(id, {
                        sawDisabled: false,
                        sawNonDisabled: false
                    });
                }
                const seen = pluginsByInternalID.get(id);
                if (p.__is_disabled_plugin__) {
                    seen.sawDisabled = true;
                } else {
                    seen.sawNonDisabled = true;
                }
                if (seen.sawDisabled && seen.sawNonDisabled) {
                    throw new Error(`You have tried to install both ApolloServerPlugin${id} and ` + `ApolloServerPlugin${id}Disabled in your server. Please choose ` + `whether or not you want to disable the feature and install the ` + `appropriate plugin for your use case.`);
                }
            }
        }
        {
            if (!alreadyHavePluginWithInternalId('CacheControl')) {
                const { ApolloServerPluginCacheControl } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/cacheControl/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginCacheControl());
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('UsageReporting');
            if (!alreadyHavePlugin && apolloConfig.key) {
                if (apolloConfig.graphRef) {
                    const { ApolloServerPluginUsageReporting } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/usageReporting/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                    plugins.unshift(ApolloServerPluginUsageReporting({
                        __onlyIfSchemaIsNotSubgraph: true
                    }));
                } else {
                    this.logger.warn('You have specified an Apollo key but have not specified a graph ref; usage ' + 'reporting is disabled. To enable usage reporting, set the `APOLLO_GRAPH_REF` ' + 'environment variable to `your-graph-id@your-graph-variant`. To disable this ' + 'warning, install `ApolloServerPluginUsageReportingDisabled`.');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('SchemaReporting');
            const enabledViaEnvVar = process.env.APOLLO_SCHEMA_REPORTING === 'true';
            if (!alreadyHavePlugin && enabledViaEnvVar) {
                if (apolloConfig.key) {
                    const { ApolloServerPluginSchemaReporting } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/schemaReporting/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                    plugins.push(ApolloServerPluginSchemaReporting());
                } else {
                    throw new Error("You've enabled schema reporting by setting the APOLLO_SCHEMA_REPORTING " + 'environment variable to true, but you also need to provide your ' + 'Apollo API key, via the APOLLO_KEY environment ' + 'variable or via `new ApolloServer({apollo: {key})');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('InlineTrace');
            if (!alreadyHavePlugin) {
                const { ApolloServerPluginInlineTrace } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/inlineTrace/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginInlineTrace({
                    __onlyIfSchemaIsSubgraph: true
                }));
            }
        }
        const alreadyHavePlugin = alreadyHavePluginWithInternalId('LandingPageDisabled');
        if (!alreadyHavePlugin) {
            const { ApolloServerPluginLandingPageLocalDefault, ApolloServerPluginLandingPageProductionDefault } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/landingPage/default/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            const plugin = isDev ? ApolloServerPluginLandingPageLocalDefault() : ApolloServerPluginLandingPageProductionDefault();
            if (!isImplicitlyInstallablePlugin(plugin)) {
                throw Error('default landing page plugin should be implicitly installable?');
            }
            plugin.__internal_installed_implicitly__ = true;
            plugins.push(plugin);
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('DisableSuggestions');
            if (hideSchemaDetailsFromClientErrors && !alreadyHavePlugin) {
                const { ApolloServerPluginDisableSuggestions } = await __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/esm/plugin/disableSuggestions/index.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                plugins.push(ApolloServerPluginDisableSuggestions());
            }
        }
    }
    addPlugin(plugin) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error("Can't add plugins after the server has started");
        }
        this.internals.plugins.push(plugin);
    }
    async executeHTTPGraphQLRequest({ httpGraphQLRequest, context }) {
        try {
            let runningServerState;
            try {
                runningServerState = await this._ensureStarted();
            } catch (error) {
                return await this.errorResponse(error, httpGraphQLRequest);
            }
            if (runningServerState.landingPage && this.prefersHTML(httpGraphQLRequest)) {
                let renderedHtml;
                if (typeof runningServerState.landingPage.html === 'string') {
                    renderedHtml = runningServerState.landingPage.html;
                } else {
                    try {
                        renderedHtml = await runningServerState.landingPage.html();
                    } catch (maybeError) {
                        const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
                        this.logger.error(`Landing page \`html\` function threw: ${error}`);
                        return await this.errorResponse(error, httpGraphQLRequest);
                    }
                }
                return {
                    headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                        [
                            'content-type',
                            'text/html'
                        ]
                    ]),
                    body: {
                        kind: 'complete',
                        string: renderedHtml
                    }
                };
            }
            if (this.internals.csrfPreventionRequestHeaders) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$preventCsrf$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["preventCsrf"])(httpGraphQLRequest.headers, this.internals.csrfPreventionRequestHeaders);
            }
            let contextValue;
            try {
                contextValue = await context();
            } catch (maybeError) {
                const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.contextCreationDidFail?.({
                            error
                        })));
                } catch (pluginError) {
                    this.logger.error(`contextCreationDidFail hook threw: ${pluginError}`);
                }
                return await this.errorResponse((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureGraphQLError"])(error, 'Context creation failed: '), httpGraphQLRequest);
            }
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$httpBatching$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["runPotentiallyBatchedHttpQuery"])(this, httpGraphQLRequest, contextValue, runningServerState.schemaManager.getSchemaDerivedData(), this.internals);
        } catch (maybeError_) {
            const maybeError = maybeError_;
            if (maybeError instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$error$2f$GraphQLError$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GraphQLError"] && maybeError.extensions.code === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errors$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServerErrorCode"].BAD_REQUEST) {
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.invalidRequestWasReceived?.({
                            error: maybeError
                        })));
                } catch (pluginError) {
                    this.logger.error(`invalidRequestWasReceived hook threw: ${pluginError}`);
                }
            }
            return await this.errorResponse(maybeError, httpGraphQLRequest);
        }
    }
    async errorResponse(error, requestHead) {
        const { formattedErrors, httpFromErrors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeAndFormatErrors"])([
            error
        ], {
            includeStacktraceInErrorResponses: this.internals.includeStacktraceInErrorResponses,
            formatError: this.internals.formatError
        });
        return {
            status: httpFromErrors.status ?? 500,
            headers: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["HeaderMap"]([
                ...httpFromErrors.headers,
                [
                    'content-type',
                    chooseContentTypeForSingleResultResponse(requestHead) ?? MEDIA_TYPES.APPLICATION_JSON
                ]
            ]),
            body: {
                kind: 'complete',
                string: await this.internals.stringifyResult({
                    errors: formattedErrors
                })
            }
        };
    }
    prefersHTML(request) {
        const acceptHeader = request.headers.get('accept');
        return request.method === 'GET' && !!acceptHeader && new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            headers: {
                accept: acceptHeader
            }
        }).mediaType([
            MEDIA_TYPES.APPLICATION_JSON,
            MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            MEDIA_TYPES.MULTIPART_MIXED_EXPERIMENTAL,
            MEDIA_TYPES.MULTIPART_MIXED_NO_DEFER_SPEC,
            MEDIA_TYPES.TEXT_HTML
        ]) === MEDIA_TYPES.TEXT_HTML;
    }
    async executeOperation(request, options = {}) {
        if (this.internals.state.phase === 'initialized') {
            await this.start();
        }
        const schemaDerivedData = (await this._ensureStarted()).schemaManager.getSchemaDerivedData();
        const graphQLRequest = {
            ...request,
            query: request.query && typeof request.query !== 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])(request.query) : request.query
        };
        const response = await internalExecuteOperation({
            server: this,
            graphQLRequest,
            internals: this.internals,
            schemaDerivedData,
            sharedResponseHTTPGraphQLHead: null
        }, options);
        return response;
    }
}
async function internalExecuteOperation({ server, graphQLRequest, internals, schemaDerivedData, sharedResponseHTTPGraphQLHead }, options) {
    const requestContext = {
        logger: server.logger,
        cache: server.cache,
        schema: schemaDerivedData.schema,
        request: graphQLRequest,
        response: {
            http: sharedResponseHTTPGraphQLHead ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$runHttpQuery$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newHTTPGraphQLHead"])()
        },
        contextValue: cloneObject(options?.contextValue ?? {}),
        metrics: {},
        overallCachePolicy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$cachePolicy$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["newCachePolicy"])(),
        requestIsBatched: sharedResponseHTTPGraphQLHead !== null
    };
    try {
        return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$requestPipeline$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processGraphQLRequest"])(schemaDerivedData, server, internals, requestContext);
    } catch (maybeError) {
        const error = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$errorNormalize$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureError"])(maybeError);
        await Promise.all(internals.plugins.map(async (plugin)=>plugin.unexpectedErrorProcessingRequest?.({
                requestContext,
                error
            })));
        server.logger.error(`Unexpected error processing request: ${error}`);
        throw new Error('Internal server error');
    }
}
function isImplicitlyInstallablePlugin(p) {
    return '__internal_installed_implicitly__' in p;
}
const MEDIA_TYPES = {
    APPLICATION_JSON: 'application/json; charset=utf-8',
    APPLICATION_JSON_GRAPHQL_CALLBACK: 'application/json; callbackSpec=1.0; charset=utf-8',
    APPLICATION_GRAPHQL_RESPONSE_JSON: 'application/graphql-response+json; charset=utf-8',
    MULTIPART_MIXED_NO_DEFER_SPEC: 'multipart/mixed',
    MULTIPART_MIXED_EXPERIMENTAL: 'multipart/mixed; deferSpec=20220824',
    TEXT_HTML: 'text/html'
};
function chooseContentTypeForSingleResultResponse(head) {
    const acceptHeader = head.headers.get('accept');
    if (!acceptHeader) {
        return MEDIA_TYPES.APPLICATION_JSON;
    } else {
        const preferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$negotiator$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
            headers: {
                accept: head.headers.get('accept')
            }
        }).mediaType([
            MEDIA_TYPES.APPLICATION_JSON,
            MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            MEDIA_TYPES.APPLICATION_JSON_GRAPHQL_CALLBACK
        ]);
        if (preferred) {
            return preferred;
        } else {
            return null;
        }
    }
}
function cloneObject(object) {
    return Object.assign(Object.create(Object.getPrototypeOf(object)), object);
} //# sourceMappingURL=ApolloServer.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$externalTypes$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
}}),
"[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$utils$2f$HeaderMap$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/utils/HeaderMap.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$externalTypes$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/externalTypes/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <locals>");
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/resolvable.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.default = ()=>{
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject)=>{
        resolve = _resolve;
        reject = _reject;
    });
    promise.resolve = resolve;
    promise.reject = reject;
    return promise;
}; //# sourceMappingURL=resolvable.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/cachePolicy.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.newCachePolicy = void 0;
function newCachePolicy() {
    return {
        maxAge: undefined,
        scope: undefined,
        restrict (hint) {
            if (hint.maxAge !== undefined && (this.maxAge === undefined || hint.maxAge < this.maxAge)) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined && this.scope !== 'PRIVATE') {
                this.scope = hint.scope;
            }
        },
        replace (hint) {
            if (hint.maxAge !== undefined) {
                this.maxAge = hint.maxAge;
            }
            if (hint.scope !== undefined) {
                this.scope = hint.scope;
            }
        },
        policyIfCacheable () {
            if (this.maxAge === undefined || this.maxAge === 0) {
                return null;
            }
            return {
                maxAge: this.maxAge,
                scope: this.scope ?? 'PUBLIC'
            };
        }
    };
}
exports.newCachePolicy = newCachePolicy; //# sourceMappingURL=cachePolicy.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/determineApolloConfig.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.determineApolloConfig = void 0;
const utils_createhash_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
function determineApolloConfig(input, logger) {
    const apolloConfig = {};
    const { APOLLO_KEY, APOLLO_GRAPH_REF, APOLLO_GRAPH_ID, APOLLO_GRAPH_VARIANT } = process.env;
    if (input?.key) {
        apolloConfig.key = input.key.trim();
    } else if (APOLLO_KEY) {
        apolloConfig.key = APOLLO_KEY.trim();
    }
    if ((input?.key ?? APOLLO_KEY) !== apolloConfig.key) {
        logger.warn('The provided API key has unexpected leading or trailing whitespace. ' + 'Apollo Server will trim the key value before use.');
    }
    if (apolloConfig.key) {
        assertValidHeaderValue(apolloConfig.key);
    }
    if (apolloConfig.key) {
        apolloConfig.keyHash = (0, utils_createhash_1.createHash)('sha512').update(apolloConfig.key).digest('hex');
    }
    if (input?.graphRef) {
        apolloConfig.graphRef = input.graphRef;
    } else if (APOLLO_GRAPH_REF) {
        apolloConfig.graphRef = APOLLO_GRAPH_REF;
    }
    const graphId = input?.graphId ?? APOLLO_GRAPH_ID;
    const graphVariant = input?.graphVariant ?? APOLLO_GRAPH_VARIANT;
    if (apolloConfig.graphRef) {
        if (graphId) {
            throw new Error('Cannot specify both graph ref and graph ID. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph ID.');
        }
        if (graphVariant) {
            throw new Error('Cannot specify both graph ref and graph variant. Please use ' + '`apollo.graphRef` or `APOLLO_GRAPH_REF` without also setting the graph variant.');
        }
    } else if (graphId) {
        apolloConfig.graphRef = graphVariant ? `${graphId}@${graphVariant}` : graphId;
    }
    return apolloConfig;
}
exports.determineApolloConfig = determineApolloConfig;
function assertValidHeaderValue(value) {
    const invalidHeaderCharRegex = /[^\t\x20-\x7e\x80-\xff]/g;
    if (invalidHeaderCharRegex.test(value)) {
        const invalidChars = value.match(invalidHeaderCharRegex);
        throw new Error(`The API key provided to Apollo Server contains characters which are invalid as HTTP header values. The following characters found in the key are invalid: ${invalidChars.join(', ')}. Valid header values may only contain ASCII visible characters. If you think there is an issue with your key, please contact Apollo support.`);
    }
} //# sourceMappingURL=determineApolloConfig.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.unwrapResolverError = exports.ApolloServerValidationErrorCode = exports.ApolloServerErrorCode = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
var ApolloServerErrorCode;
(function(ApolloServerErrorCode) {
    ApolloServerErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ApolloServerErrorCode["GRAPHQL_PARSE_FAILED"] = "GRAPHQL_PARSE_FAILED";
    ApolloServerErrorCode["GRAPHQL_VALIDATION_FAILED"] = "GRAPHQL_VALIDATION_FAILED";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_FOUND"] = "PERSISTED_QUERY_NOT_FOUND";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_SUPPORTED"] = "PERSISTED_QUERY_NOT_SUPPORTED";
    ApolloServerErrorCode["BAD_USER_INPUT"] = "BAD_USER_INPUT";
    ApolloServerErrorCode["OPERATION_RESOLUTION_FAILURE"] = "OPERATION_RESOLUTION_FAILURE";
    ApolloServerErrorCode["BAD_REQUEST"] = "BAD_REQUEST";
})(ApolloServerErrorCode || (exports.ApolloServerErrorCode = ApolloServerErrorCode = {}));
var ApolloServerValidationErrorCode;
(function(ApolloServerValidationErrorCode) {
    ApolloServerValidationErrorCode["INTROSPECTION_DISABLED"] = "INTROSPECTION_DISABLED";
    ApolloServerValidationErrorCode["MAX_RECURSIVE_SELECTIONS_EXCEEDED"] = "MAX_RECURSIVE_SELECTIONS_EXCEEDED";
})(ApolloServerValidationErrorCode || (exports.ApolloServerValidationErrorCode = ApolloServerValidationErrorCode = {}));
function unwrapResolverError(error) {
    if (error instanceof graphql_1.GraphQLError && error.path && error.originalError) {
        return error.originalError;
    }
    return error;
}
exports.unwrapResolverError = unwrapResolverError; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.HeaderMap = void 0;
class HeaderMap extends Map {
    constructor(){
        super(...arguments);
        this.__identity = Symbol('HeaderMap');
    }
    set(key, value) {
        return super.set(key.toLowerCase(), value);
    }
    get(key) {
        return super.get(key.toLowerCase());
    }
    delete(key) {
        return super.delete(key.toLowerCase());
    }
    has(key) {
        return super.has(key.toLowerCase());
    }
}
exports.HeaderMap = HeaderMap; //# sourceMappingURL=HeaderMap.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/internalErrorClasses.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.BadRequestError = exports.OperationResolutionError = exports.UserInputError = exports.PersistedQueryNotSupportedError = exports.PersistedQueryNotFoundError = exports.ValidationError = exports.SyntaxError = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const index_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)");
const runHttpQuery_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)");
const HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
class GraphQLErrorWithCode extends graphql_1.GraphQLError {
    constructor(message, code, options){
        super(message, {
            ...options,
            extensions: {
                ...options?.extensions,
                code
            }
        });
        this.name = this.constructor.name;
    }
}
class SyntaxError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, index_js_1.ApolloServerErrorCode.GRAPHQL_PARSE_FAILED, {
            source: graphqlError.source,
            positions: graphqlError.positions,
            extensions: {
                http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError
        });
    }
}
exports.SyntaxError = SyntaxError;
class ValidationError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, index_js_1.ApolloServerErrorCode.GRAPHQL_VALIDATION_FAILED, {
            nodes: graphqlError.nodes,
            extensions: {
                http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400),
                ...graphqlError.extensions
            },
            originalError: graphqlError.originalError ?? graphqlError
        });
    }
}
exports.ValidationError = ValidationError;
const getPersistedQueryErrorHttp = ()=>({
        status: 200,
        headers: new HeaderMap_js_1.HeaderMap([
            [
                'cache-control',
                'private, no-cache, must-revalidate'
            ]
        ])
    });
class PersistedQueryNotFoundError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotFound', index_js_1.ApolloServerErrorCode.PERSISTED_QUERY_NOT_FOUND, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
exports.PersistedQueryNotFoundError = PersistedQueryNotFoundError;
class PersistedQueryNotSupportedError extends GraphQLErrorWithCode {
    constructor(){
        super('PersistedQueryNotSupported', index_js_1.ApolloServerErrorCode.PERSISTED_QUERY_NOT_SUPPORTED, {
            extensions: {
                http: getPersistedQueryErrorHttp()
            }
        });
    }
}
exports.PersistedQueryNotSupportedError = PersistedQueryNotSupportedError;
class UserInputError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, index_js_1.ApolloServerErrorCode.BAD_USER_INPUT, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: graphqlError.extensions
        });
    }
}
exports.UserInputError = UserInputError;
class OperationResolutionError extends GraphQLErrorWithCode {
    constructor(graphqlError){
        super(graphqlError.message, index_js_1.ApolloServerErrorCode.OPERATION_RESOLUTION_FAILURE, {
            nodes: graphqlError.nodes,
            originalError: graphqlError.originalError ?? graphqlError,
            extensions: {
                http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400),
                ...graphqlError.extensions
            }
        });
    }
}
exports.OperationResolutionError = OperationResolutionError;
class BadRequestError extends GraphQLErrorWithCode {
    constructor(message, options){
        super(message, index_js_1.ApolloServerErrorCode.BAD_REQUEST, {
            ...options,
            extensions: {
                http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400),
                ...options?.extensions
            }
        });
    }
}
exports.BadRequestError = BadRequestError; //# sourceMappingURL=internalErrorClasses.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.mergeHTTPGraphQLHead = exports.newHTTPGraphQLHead = exports.prettyJSONStringify = exports.runHttpQuery = void 0;
const ApolloServer_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/ApolloServer.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const internalErrorClasses_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalErrorClasses.js [app-route] (ecmascript)");
const negotiator_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)"));
const HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
function fieldIfString(o, fieldName) {
    const value = o[fieldName];
    if (typeof value === 'string') {
        return value;
    }
    return undefined;
}
function searchParamIfSpecifiedOnce(searchParams, paramName) {
    const values = searchParams.getAll(paramName);
    switch(values.length){
        case 0:
            return undefined;
        case 1:
            return values[0];
        default:
            throw new internalErrorClasses_js_1.BadRequestError(`The '${paramName}' search parameter may only be specified once.`);
    }
}
function jsonParsedSearchParamIfSpecifiedOnce(searchParams, fieldName) {
    const value = searchParamIfSpecifiedOnce(searchParams, fieldName);
    if (value === undefined) {
        return undefined;
    }
    let hopefullyRecord;
    try {
        hopefullyRecord = JSON.parse(value);
    } catch  {
        throw new internalErrorClasses_js_1.BadRequestError(`The ${fieldName} search parameter contains invalid JSON.`);
    }
    if (!isStringRecord(hopefullyRecord)) {
        throw new internalErrorClasses_js_1.BadRequestError(`The ${fieldName} search parameter should contain a JSON-encoded object.`);
    }
    return hopefullyRecord;
}
function fieldIfRecord(o, fieldName) {
    const value = o[fieldName];
    if (isStringRecord(value)) {
        return value;
    }
    return undefined;
}
function isStringRecord(o) {
    return !!o && typeof o === 'object' && !Buffer.isBuffer(o) && !Array.isArray(o);
}
function isNonEmptyStringRecord(o) {
    return isStringRecord(o) && Object.keys(o).length > 0;
}
function ensureQueryIsStringOrMissing(query) {
    if (!query || typeof query === 'string') {
        return;
    }
    if (query.kind === graphql_1.Kind.DOCUMENT) {
        throw new internalErrorClasses_js_1.BadRequestError("GraphQL queries must be strings. It looks like you're sending the " + 'internal graphql-js representation of a parsed query in your ' + 'request instead of a request in the GraphQL query language. You ' + 'can convert an AST to a string using the `print` function from ' + '`graphql`, or use a client like `apollo-client` which converts ' + 'the internal representation to a string for you.');
    } else {
        throw new internalErrorClasses_js_1.BadRequestError('GraphQL queries must be strings.');
    }
}
async function runHttpQuery({ server, httpRequest, contextValue, schemaDerivedData, internals, sharedResponseHTTPGraphQLHead }) {
    let graphQLRequest;
    switch(httpRequest.method){
        case 'POST':
            {
                if (!isNonEmptyStringRecord(httpRequest.body)) {
                    throw new internalErrorClasses_js_1.BadRequestError('POST body missing, invalid Content-Type, or JSON object has no keys.');
                }
                ensureQueryIsStringOrMissing(httpRequest.body.query);
                if (typeof httpRequest.body.variables === 'string') {
                    throw new internalErrorClasses_js_1.BadRequestError('`variables` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if (typeof httpRequest.body.extensions === 'string') {
                    throw new internalErrorClasses_js_1.BadRequestError('`extensions` in a POST body should be provided as an object, not a recursively JSON-encoded string.');
                }
                if ('extensions' in httpRequest.body && httpRequest.body.extensions !== null && !isStringRecord(httpRequest.body.extensions)) {
                    throw new internalErrorClasses_js_1.BadRequestError('`extensions` in a POST body must be an object if provided.');
                }
                if ('variables' in httpRequest.body && httpRequest.body.variables !== null && !isStringRecord(httpRequest.body.variables)) {
                    throw new internalErrorClasses_js_1.BadRequestError('`variables` in a POST body must be an object if provided.');
                }
                if ('operationName' in httpRequest.body && httpRequest.body.operationName !== null && typeof httpRequest.body.operationName !== 'string') {
                    throw new internalErrorClasses_js_1.BadRequestError('`operationName` in a POST body must be a string if provided.');
                }
                graphQLRequest = {
                    query: fieldIfString(httpRequest.body, 'query'),
                    operationName: fieldIfString(httpRequest.body, 'operationName'),
                    variables: fieldIfRecord(httpRequest.body, 'variables'),
                    extensions: fieldIfRecord(httpRequest.body, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        case 'GET':
            {
                const searchParams = new URLSearchParams(httpRequest.search);
                graphQLRequest = {
                    query: searchParamIfSpecifiedOnce(searchParams, 'query'),
                    operationName: searchParamIfSpecifiedOnce(searchParams, 'operationName'),
                    variables: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'variables'),
                    extensions: jsonParsedSearchParamIfSpecifiedOnce(searchParams, 'extensions'),
                    http: httpRequest
                };
                break;
            }
        default:
            throw new internalErrorClasses_js_1.BadRequestError('Apollo Server supports only GET/POST requests.', {
                extensions: {
                    http: {
                        status: 405,
                        headers: new HeaderMap_js_1.HeaderMap([
                            [
                                'allow',
                                'GET, POST'
                            ]
                        ])
                    }
                }
            });
    }
    const graphQLResponse = await (0, ApolloServer_js_1.internalExecuteOperation)({
        server,
        graphQLRequest,
        internals,
        schemaDerivedData,
        sharedResponseHTTPGraphQLHead
    }, {
        contextValue
    });
    if (graphQLResponse.body.kind === 'single') {
        if (!graphQLResponse.http.headers.get('content-type')) {
            const contentType = (0, ApolloServer_js_1.chooseContentTypeForSingleResultResponse)(httpRequest);
            if (contentType === null) {
                throw new internalErrorClasses_js_1.BadRequestError(`An 'accept' header was provided for this request which does not accept ` + `${ApolloServer_js_1.MEDIA_TYPES.APPLICATION_JSON} or ${ApolloServer_js_1.MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON}`, {
                    extensions: {
                        http: {
                            status: 406
                        }
                    }
                });
            }
            graphQLResponse.http.headers.set('content-type', contentType);
        }
        return {
            ...graphQLResponse.http,
            body: {
                kind: 'complete',
                string: await internals.stringifyResult(orderExecutionResultFields(graphQLResponse.body.singleResult))
            }
        };
    }
    const acceptHeader = httpRequest.headers.get('accept');
    if (!(acceptHeader && new negotiator_1.default({
        headers: {
            accept: httpRequest.headers.get('accept')
        }
    }).mediaType([
        ApolloServer_js_1.MEDIA_TYPES.MULTIPART_MIXED_NO_DEFER_SPEC,
        ApolloServer_js_1.MEDIA_TYPES.MULTIPART_MIXED_EXPERIMENTAL
    ]) === ApolloServer_js_1.MEDIA_TYPES.MULTIPART_MIXED_EXPERIMENTAL)) {
        throw new internalErrorClasses_js_1.BadRequestError('Apollo server received an operation that uses incremental delivery ' + '(@defer or @stream), but the client does not accept multipart/mixed ' + 'HTTP responses. To enable incremental delivery support, add the HTTP ' + "header 'Accept: multipart/mixed; deferSpec=20220824'.", {
            extensions: {
                http: {
                    status: 406
                }
            }
        });
    }
    graphQLResponse.http.headers.set('content-type', 'multipart/mixed; boundary="-"; deferSpec=20220824');
    return {
        ...graphQLResponse.http,
        body: {
            kind: 'chunked',
            asyncIterator: writeMultipartBody(graphQLResponse.body.initialResult, graphQLResponse.body.subsequentResults)
        }
    };
}
exports.runHttpQuery = runHttpQuery;
async function* writeMultipartBody(initialResult, subsequentResults) {
    yield `\r\n---\r\ncontent-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderInitialIncrementalExecutionResultFields(initialResult))}\r\n---${initialResult.hasNext ? '' : '--'}\r\n`;
    for await (const result of subsequentResults){
        yield `content-type: application/json; charset=utf-8\r\n\r\n${JSON.stringify(orderSubsequentIncrementalExecutionResultFields(result))}\r\n---${result.hasNext ? '' : '--'}\r\n`;
    }
}
function orderExecutionResultFields(result) {
    return {
        errors: result.errors,
        data: result.data,
        extensions: result.extensions
    };
}
function orderInitialIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        errors: result.errors,
        data: result.data,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderSubsequentIncrementalExecutionResultFields(result) {
    return {
        hasNext: result.hasNext,
        incremental: orderIncrementalResultFields(result.incremental),
        extensions: result.extensions
    };
}
function orderIncrementalResultFields(incremental) {
    return incremental?.map((i)=>({
            hasNext: i.hasNext,
            errors: i.errors,
            path: i.path,
            label: i.label,
            data: i.data,
            items: i.items,
            extensions: i.extensions
        }));
}
function prettyJSONStringify(value) {
    return JSON.stringify(value) + '\n';
}
exports.prettyJSONStringify = prettyJSONStringify;
function newHTTPGraphQLHead(status) {
    return {
        status,
        headers: new HeaderMap_js_1.HeaderMap()
    };
}
exports.newHTTPGraphQLHead = newHTTPGraphQLHead;
function mergeHTTPGraphQLHead(target, source) {
    if (source.status) {
        target.status = source.status;
    }
    if (source.headers) {
        for (const [name, value] of source.headers){
            target.headers.set(name, value);
        }
    }
}
exports.mergeHTTPGraphQLHead = mergeHTTPGraphQLHead; //# sourceMappingURL=runHttpQuery.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/errorNormalize.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ensureGraphQLError = exports.ensureError = exports.normalizeAndFormatErrors = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const index_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)");
const runHttpQuery_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)");
const HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
function normalizeAndFormatErrors(errors, options = {}) {
    const formatError = options.formatError ?? ((error)=>error);
    const httpFromErrors = (0, runHttpQuery_js_1.newHTTPGraphQLHead)();
    return {
        httpFromErrors,
        formattedErrors: errors.map((error)=>{
            try {
                return formatError(enrichError(error), error);
            } catch (formattingError) {
                if (options.includeStacktraceInErrorResponses) {
                    return enrichError(formattingError);
                } else {
                    return {
                        message: 'Internal server error',
                        extensions: {
                            code: index_js_1.ApolloServerErrorCode.INTERNAL_SERVER_ERROR
                        }
                    };
                }
            }
        })
    };
    "TURBOPACK unreachable";
    function enrichError(maybeError) {
        const graphqlError = ensureGraphQLError(maybeError);
        const extensions = {
            ...graphqlError.extensions,
            code: graphqlError.extensions.code ?? index_js_1.ApolloServerErrorCode.INTERNAL_SERVER_ERROR
        };
        if (isPartialHTTPGraphQLHead(extensions.http)) {
            (0, runHttpQuery_js_1.mergeHTTPGraphQLHead)(httpFromErrors, {
                headers: new HeaderMap_js_1.HeaderMap(),
                ...extensions.http
            });
            delete extensions.http;
        }
        if (options.includeStacktraceInErrorResponses) {
            extensions.stacktrace = graphqlError.stack?.split('\n');
        }
        return {
            ...graphqlError.toJSON(),
            extensions
        };
    }
}
exports.normalizeAndFormatErrors = normalizeAndFormatErrors;
function ensureError(maybeError) {
    return maybeError instanceof Error ? maybeError : new graphql_1.GraphQLError('Unexpected error value: ' + String(maybeError));
}
exports.ensureError = ensureError;
function ensureGraphQLError(maybeError, messagePrefixIfNotGraphQLError = '') {
    const error = ensureError(maybeError);
    return error instanceof graphql_1.GraphQLError ? error : new graphql_1.GraphQLError(messagePrefixIfNotGraphQLError + error.message, {
        originalError: error
    });
}
exports.ensureGraphQLError = ensureGraphQLError;
function isPartialHTTPGraphQLHead(x) {
    return !!x && typeof x === 'object' && (!('status' in x) || typeof x.status === 'number') && (!('headers' in x) || x.headers instanceof Map);
} //# sourceMappingURL=errorNormalize.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/httpBatching.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.runPotentiallyBatchedHttpQuery = void 0;
const runHttpQuery_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)");
const internalErrorClasses_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalErrorClasses.js [app-route] (ecmascript)");
async function runBatchedHttpQuery({ server, batchRequest, body, contextValue, schemaDerivedData, internals }) {
    if (body.length === 0) {
        throw new internalErrorClasses_js_1.BadRequestError('No operations found in request.');
    }
    const sharedResponseHTTPGraphQLHead = (0, runHttpQuery_js_1.newHTTPGraphQLHead)();
    const responseBodies = await Promise.all(body.map(async (bodyPiece)=>{
        const singleRequest = {
            ...batchRequest,
            body: bodyPiece
        };
        const response = await (0, runHttpQuery_js_1.runHttpQuery)({
            server,
            httpRequest: singleRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead
        });
        if (response.body.kind === 'chunked') {
            throw Error('Incremental delivery is not implemented for batch requests');
        }
        return response.body.string;
    }));
    return {
        ...sharedResponseHTTPGraphQLHead,
        body: {
            kind: 'complete',
            string: `[${responseBodies.join(',')}]`
        }
    };
}
async function runPotentiallyBatchedHttpQuery(server, httpGraphQLRequest, contextValue, schemaDerivedData, internals) {
    if (!(httpGraphQLRequest.method === 'POST' && Array.isArray(httpGraphQLRequest.body))) {
        return await (0, runHttpQuery_js_1.runHttpQuery)({
            server,
            httpRequest: httpGraphQLRequest,
            contextValue,
            schemaDerivedData,
            internals,
            sharedResponseHTTPGraphQLHead: null
        });
    }
    if (internals.allowBatchedHttpRequests) {
        return await runBatchedHttpQuery({
            server,
            batchRequest: httpGraphQLRequest,
            body: httpGraphQLRequest.body,
            contextValue,
            schemaDerivedData,
            internals
        });
    }
    throw new internalErrorClasses_js_1.BadRequestError('Operation batching disabled.');
}
exports.runPotentiallyBatchedHttpQuery = runPotentiallyBatchedHttpQuery; //# sourceMappingURL=httpBatching.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.pluginIsInternal = exports.internalPlugin = void 0;
function internalPlugin(p) {
    return p;
}
exports.internalPlugin = internalPlugin;
function pluginIsInternal(plugin) {
    return '__internal_plugin_id__' in plugin;
}
exports.pluginIsInternal = pluginIsInternal; //# sourceMappingURL=internalPlugin.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/preventCsrf.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.preventCsrf = exports.recommendedCsrfPreventionRequestHeaders = void 0;
const whatwg_mimetype_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/whatwg-mimetype/lib/mime-type.js [app-route] (ecmascript)"));
const internalErrorClasses_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalErrorClasses.js [app-route] (ecmascript)");
exports.recommendedCsrfPreventionRequestHeaders = [
    'x-apollo-operation-name',
    'apollo-require-preflight'
];
const NON_PREFLIGHTED_CONTENT_TYPES = [
    'application/x-www-form-urlencoded',
    'multipart/form-data',
    'text/plain'
];
function preventCsrf(headers, csrfPreventionRequestHeaders) {
    const contentType = headers.get('content-type');
    if (contentType !== undefined) {
        const contentTypeParsed = whatwg_mimetype_1.default.parse(contentType);
        if (contentTypeParsed === null) {
            return;
        }
        if (!NON_PREFLIGHTED_CONTENT_TYPES.includes(contentTypeParsed.essence)) {
            return;
        }
    }
    if (csrfPreventionRequestHeaders.some((header)=>{
        const value = headers.get(header);
        return value !== undefined && value.length > 0;
    })) {
        return;
    }
    throw new internalErrorClasses_js_1.BadRequestError(`This operation has been blocked as a potential Cross-Site Request Forgery ` + `(CSRF). Please either specify a 'content-type' header (with a type that ` + `is not one of ${NON_PREFLIGHTED_CONTENT_TYPES.join(', ')}) or provide ` + `a non-empty value for one of the following headers: ${csrfPreventionRequestHeaders.join(', ')}\n`);
}
exports.preventCsrf = preventCsrf; //# sourceMappingURL=preventCsrf.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/schemaInstrumentation.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.whenResultIsFinished = exports.pluginsEnabledForSchemaResolvers = exports.enablePluginsForSchemaResolvers = exports.symbolUserFieldResolver = exports.symbolExecutionDispatcherWillResolveField = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
exports.symbolExecutionDispatcherWillResolveField = Symbol('apolloServerExecutionDispatcherWillResolveField');
exports.symbolUserFieldResolver = Symbol('apolloServerUserFieldResolver');
const symbolPluginsEnabled = Symbol('apolloServerPluginsEnabled');
function enablePluginsForSchemaResolvers(schema) {
    if (pluginsEnabledForSchemaResolvers(schema)) {
        return schema;
    }
    Object.defineProperty(schema, symbolPluginsEnabled, {
        value: true
    });
    const typeMap = schema.getTypeMap();
    Object.values(typeMap).forEach((type)=>{
        if (!(0, graphql_1.getNamedType)(type).name.startsWith('__') && type instanceof graphql_1.GraphQLObjectType) {
            const fields = type.getFields();
            Object.values(fields).forEach((field)=>{
                wrapField(field);
            });
        }
    });
    return schema;
}
exports.enablePluginsForSchemaResolvers = enablePluginsForSchemaResolvers;
function pluginsEnabledForSchemaResolvers(schema) {
    return !!schema[symbolPluginsEnabled];
}
exports.pluginsEnabledForSchemaResolvers = pluginsEnabledForSchemaResolvers;
function wrapField(field) {
    const originalFieldResolve = field.resolve;
    field.resolve = (source, args, contextValue, info)=>{
        const willResolveField = contextValue?.[exports.symbolExecutionDispatcherWillResolveField];
        const userFieldResolver = contextValue?.[exports.symbolUserFieldResolver];
        const didResolveField = typeof willResolveField === 'function' && willResolveField({
            source,
            args,
            contextValue,
            info
        });
        const fieldResolver = originalFieldResolve || userFieldResolver || graphql_1.defaultFieldResolver;
        try {
            const result = fieldResolver(source, args, contextValue, info);
            if (typeof didResolveField === 'function') {
                whenResultIsFinished(result, didResolveField);
            }
            return result;
        } catch (error) {
            if (typeof didResolveField === 'function') {
                didResolveField(error);
            }
            throw error;
        }
    };
}
function isPromise(x) {
    return x && typeof x.then === 'function';
}
function whenResultIsFinished(result, callback) {
    if (isPromise(result)) {
        result.then((r)=>whenResultIsFinished(r, callback), (err)=>callback(err));
    } else if (Array.isArray(result)) {
        if (result.some(isPromise)) {
            Promise.all(result).then((r)=>callback(null, r), (err)=>callback(err));
        } else {
            callback(null, result);
        }
    } else {
        callback(null, result);
    }
}
exports.whenResultIsFinished = whenResultIsFinished; //# sourceMappingURL=schemaInstrumentation.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/isDefined.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isDefined = void 0;
function isDefined(t) {
    return t != null;
}
exports.isDefined = isDefined; //# sourceMappingURL=isDefined.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/invokeHooks.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.invokeHooksUntilDefinedAndNonNull = exports.invokeSyncDidStartHook = exports.invokeDidStartHook = void 0;
const isDefined_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/isDefined.js [app-route] (ecmascript)");
async function invokeDidStartHook(targets, hook) {
    const didEndHooks = (await Promise.all(targets.map((target)=>hook(target)))).filter(isDefined_js_1.isDefined);
    didEndHooks.reverse();
    return async (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
exports.invokeDidStartHook = invokeDidStartHook;
function invokeSyncDidStartHook(targets, hook) {
    const didEndHooks = targets.map((target)=>hook(target)).filter(isDefined_js_1.isDefined);
    didEndHooks.reverse();
    return (...args)=>{
        for (const didEndHook of didEndHooks){
            didEndHook(...args);
        }
    };
}
exports.invokeSyncDidStartHook = invokeSyncDidStartHook;
async function invokeHooksUntilDefinedAndNonNull(targets, hook) {
    for (const target of targets){
        const value = await hook(target);
        if (value != null) {
            return value;
        }
    }
    return null;
}
exports.invokeHooksUntilDefinedAndNonNull = invokeHooksUntilDefinedAndNonNull; //# sourceMappingURL=invokeHooks.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.makeGatewayGraphQLRequestContext = void 0;
function makeGatewayGraphQLRequestContext(as4RequestContext, server, internals) {
    const request = {};
    if ('query' in as4RequestContext.request) {
        request.query = as4RequestContext.request.query;
    }
    if ('operationName' in as4RequestContext.request) {
        request.operationName = as4RequestContext.request.operationName;
    }
    if ('variables' in as4RequestContext.request) {
        request.variables = as4RequestContext.request.variables;
    }
    if ('extensions' in as4RequestContext.request) {
        request.extensions = as4RequestContext.request.extensions;
    }
    if (as4RequestContext.request.http) {
        const as4http = as4RequestContext.request.http;
        const needQuestion = as4http.search !== '' && !as4http.search.startsWith('?');
        request.http = {
            method: as4http.method,
            url: `https://unknown-url.invalid/${needQuestion ? '?' : ''}${as4http.search}`,
            headers: new FetcherHeadersForHeaderMap(as4http.headers)
        };
    }
    const response = {
        http: {
            headers: new FetcherHeadersForHeaderMap(as4RequestContext.response.http.headers),
            get status () {
                return as4RequestContext.response.http.status;
            },
            set status (newStatus){
                as4RequestContext.response.http.status = newStatus;
            }
        }
    };
    return {
        request,
        response,
        logger: server.logger,
        schema: as4RequestContext.schema,
        schemaHash: 'schemaHash no longer exists in Apollo Server 4',
        context: as4RequestContext.contextValue,
        cache: server.cache,
        queryHash: as4RequestContext.queryHash,
        document: as4RequestContext.document,
        source: as4RequestContext.source,
        operationName: as4RequestContext.operationName,
        operation: as4RequestContext.operation,
        errors: as4RequestContext.errors,
        metrics: as4RequestContext.metrics,
        debug: internals.includeStacktraceInErrorResponses,
        overallCachePolicy: as4RequestContext.overallCachePolicy,
        requestIsBatched: as4RequestContext.requestIsBatched
    };
}
exports.makeGatewayGraphQLRequestContext = makeGatewayGraphQLRequestContext;
class FetcherHeadersForHeaderMap {
    constructor(map){
        this.map = map;
    }
    append(name, value) {
        if (this.map.has(name)) {
            this.map.set(name, this.map.get(name) + ', ' + value);
        } else {
            this.map.set(name, value);
        }
    }
    delete(name) {
        this.map.delete(name);
    }
    get(name) {
        return this.map.get(name) ?? null;
    }
    has(name) {
        return this.map.has(name);
    }
    set(name, value) {
        this.map.set(name, value);
    }
    entries() {
        return this.map.entries();
    }
    keys() {
        return this.map.keys();
    }
    values() {
        return this.map.values();
    }
    [Symbol.iterator]() {
        return this.map.entries();
    }
} //# sourceMappingURL=makeGatewayGraphQLRequestContext.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/incrementalDeliveryPolyfill.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.executeIncrementally = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
let graphqlExperimentalExecuteIncrementally = undefined;
async function tryToLoadGraphQL17() {
    if (graphqlExperimentalExecuteIncrementally !== undefined) {
        return;
    }
    const graphql = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)")));
    if ('experimentalExecuteIncrementally' in graphql) {
        graphqlExperimentalExecuteIncrementally = graphql.experimentalExecuteIncrementally;
    } else {
        graphqlExperimentalExecuteIncrementally = null;
    }
}
async function executeIncrementally(args) {
    await tryToLoadGraphQL17();
    if (graphqlExperimentalExecuteIncrementally) {
        return graphqlExperimentalExecuteIncrementally(args);
    }
    return (0, graphql_1.execute)(args);
}
exports.executeIncrementally = executeIncrementally; //# sourceMappingURL=incrementalDeliveryPolyfill.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/requestPipeline.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.processGraphQLRequest = exports.APQ_CACHE_PREFIX = void 0;
const utils_createhash_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const schemaInstrumentation_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/schemaInstrumentation.js [app-route] (ecmascript)");
const internalErrorClasses_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalErrorClasses.js [app-route] (ecmascript)");
const errorNormalize_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errorNormalize.js [app-route] (ecmascript)");
const invokeHooks_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/invokeHooks.js [app-route] (ecmascript)");
const makeGatewayGraphQLRequestContext_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/makeGatewayGraphQLRequestContext.js [app-route] (ecmascript)");
const runHttpQuery_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)");
const isDefined_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/isDefined.js [app-route] (ecmascript)");
const incrementalDeliveryPolyfill_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/incrementalDeliveryPolyfill.js [app-route] (ecmascript)");
const HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
exports.APQ_CACHE_PREFIX = 'apq:';
function computeQueryHash(query) {
    return (0, utils_createhash_1.createHash)('sha256').update(query).digest('hex');
}
function isBadUserInputGraphQLError(error) {
    return error.nodes?.length === 1 && error.nodes[0].kind === graphql_1.Kind.VARIABLE_DEFINITION && (error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" got invalid value `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of required type `) || error.message.startsWith(`Variable "$${error.nodes[0].variable.name.value}" of non-null type `));
}
async function processGraphQLRequest(schemaDerivedData, server, internals, requestContext) {
    const requestListeners = (await Promise.all(internals.plugins.map((p)=>p.requestDidStart?.(requestContext)))).filter(isDefined_js_1.isDefined);
    const request = requestContext.request;
    let { query, extensions } = request;
    let queryHash;
    requestContext.metrics.persistedQueryHit = false;
    requestContext.metrics.persistedQueryRegister = false;
    if (extensions?.persistedQuery) {
        if (!internals.persistedQueries) {
            return await sendErrorResponse([
                new internalErrorClasses_js_1.PersistedQueryNotSupportedError()
            ]);
        } else if (extensions.persistedQuery.version !== 1) {
            return await sendErrorResponse([
                new graphql_1.GraphQLError('Unsupported persisted query version', {
                    extensions: {
                        http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400)
                    }
                })
            ]);
        }
        queryHash = extensions.persistedQuery.sha256Hash;
        if (query === undefined) {
            query = await internals.persistedQueries.cache.get(queryHash);
            if (query) {
                requestContext.metrics.persistedQueryHit = true;
            } else {
                return await sendErrorResponse([
                    new internalErrorClasses_js_1.PersistedQueryNotFoundError()
                ]);
            }
        } else {
            const computedQueryHash = computeQueryHash(query);
            if (queryHash !== computedQueryHash) {
                return await sendErrorResponse([
                    new graphql_1.GraphQLError('provided sha does not match query', {
                        extensions: {
                            http: (0, runHttpQuery_js_1.newHTTPGraphQLHead)(400)
                        }
                    })
                ]);
            }
            requestContext.metrics.persistedQueryRegister = true;
        }
    } else if (query) {
        queryHash = computeQueryHash(query);
    } else {
        return await sendErrorResponse([
            new internalErrorClasses_js_1.BadRequestError('GraphQL operations must contain a non-empty `query` or a `persistedQuery` extension.')
        ]);
    }
    requestContext.queryHash = queryHash;
    requestContext.source = query;
    await Promise.all(requestListeners.map((l)=>l.didResolveSource?.(requestContext)));
    if (schemaDerivedData.documentStore) {
        try {
            requestContext.document = await schemaDerivedData.documentStore.get(schemaDerivedData.documentStoreKeyPrefix + queryHash);
        } catch (err) {
            server.logger.warn('An error occurred while attempting to read from the documentStore. ' + (0, errorNormalize_js_1.ensureError)(err).message);
        }
    }
    if (!requestContext.document) {
        const parsingDidEnd = await (0, invokeHooks_js_1.invokeDidStartHook)(requestListeners, async (l)=>l.parsingDidStart?.(requestContext));
        try {
            requestContext.document = (0, graphql_1.parse)(query, internals.parseOptions);
        } catch (syntaxMaybeError) {
            const error = (0, errorNormalize_js_1.ensureError)(syntaxMaybeError);
            await parsingDidEnd(error);
            return await sendErrorResponse([
                new internalErrorClasses_js_1.SyntaxError((0, errorNormalize_js_1.ensureGraphQLError)(error))
            ]);
        }
        await parsingDidEnd();
        if (internals.dangerouslyDisableValidation !== true) {
            const validationDidEnd = await (0, invokeHooks_js_1.invokeDidStartHook)(requestListeners, async (l)=>l.validationDidStart?.(requestContext));
            let validationErrors = (0, graphql_1.validate)(schemaDerivedData.schema, requestContext.document, [
                ...graphql_1.specifiedRules,
                ...internals.validationRules
            ]);
            if (validationErrors.length === 0 && internals.laterValidationRules) {
                validationErrors = (0, graphql_1.validate)(schemaDerivedData.schema, requestContext.document, internals.laterValidationRules);
            }
            if (validationErrors.length === 0) {
                await validationDidEnd();
            } else {
                await validationDidEnd(validationErrors);
                return await sendErrorResponse(validationErrors.map((error)=>new internalErrorClasses_js_1.ValidationError(error)));
            }
        }
        if (schemaDerivedData.documentStore) {
            Promise.resolve(schemaDerivedData.documentStore.set(schemaDerivedData.documentStoreKeyPrefix + queryHash, requestContext.document)).catch((err)=>server.logger.warn('Could not store validated document. ' + err?.message || err));
        }
    }
    const operation = (0, graphql_1.getOperationAST)(requestContext.document, request.operationName);
    requestContext.operation = operation || undefined;
    requestContext.operationName = operation?.name?.value || null;
    if (request.http?.method === 'GET' && operation?.operation && operation.operation !== 'query') {
        return await sendErrorResponse([
            new internalErrorClasses_js_1.BadRequestError(`GET requests only support query operations, not ${operation.operation} operations`, {
                extensions: {
                    http: {
                        status: 405,
                        headers: new HeaderMap_js_1.HeaderMap([
                            [
                                'allow',
                                'POST'
                            ]
                        ])
                    }
                }
            })
        ]);
    }
    try {
        await Promise.all(requestListeners.map((l)=>l.didResolveOperation?.(requestContext)));
    } catch (err) {
        return await sendErrorResponse([
            (0, errorNormalize_js_1.ensureGraphQLError)(err)
        ]);
    }
    if (requestContext.metrics.persistedQueryRegister && internals.persistedQueries) {
        const ttl = internals.persistedQueries?.ttl;
        Promise.resolve(internals.persistedQueries.cache.set(queryHash, query, ttl !== undefined ? {
            ttl: internals.persistedQueries?.ttl
        } : undefined)).catch(server.logger.warn);
    }
    const responseFromPlugin = await (0, invokeHooks_js_1.invokeHooksUntilDefinedAndNonNull)(requestListeners, async (l)=>await l.responseForOperation?.(requestContext));
    if (responseFromPlugin !== null) {
        requestContext.response.body = responseFromPlugin.body;
        (0, runHttpQuery_js_1.mergeHTTPGraphQLHead)(requestContext.response.http, responseFromPlugin.http);
    } else {
        const executionListeners = (await Promise.all(requestListeners.map((l)=>l.executionDidStart?.(requestContext)))).filter(isDefined_js_1.isDefined);
        executionListeners.reverse();
        if (executionListeners.some((l)=>l.willResolveField)) {
            const invokeWillResolveField = (...args)=>(0, invokeHooks_js_1.invokeSyncDidStartHook)(executionListeners, (l)=>l.willResolveField?.(...args));
            Object.defineProperty(requestContext.contextValue, schemaInstrumentation_js_1.symbolExecutionDispatcherWillResolveField, {
                value: invokeWillResolveField
            });
            if (internals.fieldResolver) {
                Object.defineProperty(requestContext.contextValue, schemaInstrumentation_js_1.symbolUserFieldResolver, {
                    value: internals.fieldResolver
                });
            }
            (0, schemaInstrumentation_js_1.enablePluginsForSchemaResolvers)(schemaDerivedData.schema);
        }
        try {
            const fullResult = await execute(requestContext);
            const result = 'singleResult' in fullResult ? fullResult.singleResult : fullResult.initialResult;
            if (!requestContext.operation) {
                if (!result.errors?.length) {
                    throw new Error('Unexpected error: Apollo Server did not resolve an operation but execute did not return errors');
                }
                throw new internalErrorClasses_js_1.OperationResolutionError(result.errors[0]);
            }
            const resultErrors = result.errors?.map((e)=>{
                if (isBadUserInputGraphQLError(e) && e.extensions?.code == null) {
                    return new internalErrorClasses_js_1.UserInputError(e);
                }
                return e;
            });
            if (resultErrors) {
                await didEncounterErrors(resultErrors);
            }
            const { formattedErrors, httpFromErrors } = resultErrors ? formatErrors(resultErrors) : {
                formattedErrors: undefined,
                httpFromErrors: (0, runHttpQuery_js_1.newHTTPGraphQLHead)()
            };
            if (internals.status400ForVariableCoercionErrors && resultErrors?.length && result.data === undefined && !httpFromErrors.status) {
                httpFromErrors.status = 400;
            }
            (0, runHttpQuery_js_1.mergeHTTPGraphQLHead)(requestContext.response.http, httpFromErrors);
            if ('singleResult' in fullResult) {
                requestContext.response.body = {
                    kind: 'single',
                    singleResult: {
                        ...result,
                        errors: formattedErrors
                    }
                };
            } else {
                requestContext.response.body = {
                    kind: 'incremental',
                    initialResult: {
                        ...fullResult.initialResult,
                        errors: formattedErrors
                    },
                    subsequentResults: fullResult.subsequentResults
                };
            }
        } catch (executionMaybeError) {
            const executionError = (0, errorNormalize_js_1.ensureError)(executionMaybeError);
            await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.(executionError)));
            return await sendErrorResponse([
                (0, errorNormalize_js_1.ensureGraphQLError)(executionError)
            ]);
        }
        await Promise.all(executionListeners.map((l)=>l.executionDidEnd?.()));
    }
    await invokeWillSendResponse();
    if (!requestContext.response.body) {
        throw Error('got to end of processGraphQLRequest without setting body?');
    }
    return requestContext.response;
    "TURBOPACK unreachable";
    async function execute(requestContext) {
        const { request, document } = requestContext;
        if (internals.__testing_incrementalExecutionResults) {
            return internals.__testing_incrementalExecutionResults;
        } else if (internals.gatewayExecutor) {
            const result = await internals.gatewayExecutor((0, makeGatewayGraphQLRequestContext_js_1.makeGatewayGraphQLRequestContext)(requestContext, server, internals));
            return {
                singleResult: result
            };
        } else {
            const resultOrResults = await (0, incrementalDeliveryPolyfill_js_1.executeIncrementally)({
                schema: schemaDerivedData.schema,
                document,
                rootValue: typeof internals.rootValue === 'function' ? internals.rootValue(document) : internals.rootValue,
                contextValue: requestContext.contextValue,
                variableValues: request.variables,
                operationName: request.operationName,
                fieldResolver: internals.fieldResolver
            });
            if ('initialResult' in resultOrResults) {
                return {
                    initialResult: resultOrResults.initialResult,
                    subsequentResults: formatErrorsInSubsequentResults(resultOrResults.subsequentResults)
                };
            } else {
                return {
                    singleResult: resultOrResults
                };
            }
        }
    }
    async function* formatErrorsInSubsequentResults(results) {
        for await (const result of results){
            const payload = result.incremental ? {
                ...result,
                incremental: await seriesAsyncMap(result.incremental, async (incrementalResult)=>{
                    const { errors } = incrementalResult;
                    if (errors) {
                        await Promise.all(requestListeners.map((l)=>l.didEncounterSubsequentErrors?.(requestContext, errors)));
                        return {
                            ...incrementalResult,
                            errors: formatErrors(errors).formattedErrors
                        };
                    }
                    return incrementalResult;
                })
            } : result;
            await Promise.all(requestListeners.map((l)=>l.willSendSubsequentPayload?.(requestContext, payload)));
            yield payload;
        }
    }
    async function invokeWillSendResponse() {
        await Promise.all(requestListeners.map((l)=>l.willSendResponse?.(requestContext)));
    }
    async function didEncounterErrors(errors) {
        requestContext.errors = errors;
        return await Promise.all(requestListeners.map((l)=>l.didEncounterErrors?.(requestContext)));
    }
    async function sendErrorResponse(errors) {
        await didEncounterErrors(errors);
        const { formattedErrors, httpFromErrors } = formatErrors(errors);
        requestContext.response.body = {
            kind: 'single',
            singleResult: {
                errors: formattedErrors
            }
        };
        (0, runHttpQuery_js_1.mergeHTTPGraphQLHead)(requestContext.response.http, httpFromErrors);
        if (!requestContext.response.http.status) {
            requestContext.response.http.status = 500;
        }
        await invokeWillSendResponse();
        return requestContext.response;
    }
    function formatErrors(errors) {
        return (0, errorNormalize_js_1.normalizeAndFormatErrors)(errors, {
            formatError: internals.formatError,
            includeStacktraceInErrorResponses: internals.includeStacktraceInErrorResponses
        });
    }
}
exports.processGraphQLRequest = processGraphQLRequest;
async function seriesAsyncMap(ts, fn) {
    const us = [];
    for (const t of ts){
        const u = await fn(t);
        us.push(u);
    }
    return us;
} //# sourceMappingURL=requestPipeline.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/UnreachableCaseError.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.UnreachableCaseError = void 0;
class UnreachableCaseError extends Error {
    constructor(val){
        super(`Unreachable case: ${val}`);
    }
}
exports.UnreachableCaseError = UnreachableCaseError; //# sourceMappingURL=UnreachableCaseError.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/computeCoreSchemaHash.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.computeCoreSchemaHash = void 0;
const utils_createhash_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
function computeCoreSchemaHash(schema) {
    return (0, utils_createhash_1.createHash)('sha256').update(schema).digest('hex');
}
exports.computeCoreSchemaHash = computeCoreSchemaHash; //# sourceMappingURL=computeCoreSchemaHash.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/utils/schemaManager.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.SchemaManager = void 0;
class SchemaManager {
    constructor(options){
        this.onSchemaLoadOrUpdateListeners = new Set();
        this.isStopped = false;
        this.logger = options.logger;
        this.schemaDerivedDataProvider = options.schemaDerivedDataProvider;
        if ('gateway' in options) {
            this.modeSpecificState = {
                mode: 'gateway',
                gateway: options.gateway,
                apolloConfig: options.apolloConfig
            };
        } else {
            this.modeSpecificState = {
                mode: 'schema',
                apiSchema: options.apiSchema,
                schemaDerivedData: options.schemaDerivedDataProvider(options.apiSchema)
            };
        }
    }
    async start() {
        if (this.modeSpecificState.mode === 'gateway') {
            const gateway = this.modeSpecificState.gateway;
            if (gateway.onSchemaLoadOrUpdate) {
                this.modeSpecificState.unsubscribeFromGateway = gateway.onSchemaLoadOrUpdate((schemaContext)=>{
                    this.processSchemaLoadOrUpdateEvent(schemaContext);
                });
            } else {
                throw new Error("Unexpectedly couldn't find onSchemaLoadOrUpdate on gateway");
            }
            const config = await this.modeSpecificState.gateway.load({
                apollo: this.modeSpecificState.apolloConfig
            });
            return config.executor;
        } else {
            this.processSchemaLoadOrUpdateEvent({
                apiSchema: this.modeSpecificState.apiSchema
            }, this.modeSpecificState.schemaDerivedData);
            return null;
        }
    }
    onSchemaLoadOrUpdate(callback) {
        if (!this.schemaContext) {
            throw new Error('You must call start() before onSchemaLoadOrUpdate()');
        }
        if (!this.isStopped) {
            try {
                callback(this.schemaContext);
            } catch (e) {
                throw new Error(`An error was thrown from an 'onSchemaLoadOrUpdate' listener: ${e.message}`);
            }
        }
        this.onSchemaLoadOrUpdateListeners.add(callback);
        return ()=>{
            this.onSchemaLoadOrUpdateListeners.delete(callback);
        };
    }
    getSchemaDerivedData() {
        if (!this.schemaDerivedData) {
            throw new Error('You must call start() before getSchemaDerivedData()');
        }
        return this.schemaDerivedData;
    }
    async stop() {
        this.isStopped = true;
        if (this.modeSpecificState.mode === 'gateway') {
            this.modeSpecificState.unsubscribeFromGateway?.();
            await this.modeSpecificState.gateway.stop?.();
        }
    }
    processSchemaLoadOrUpdateEvent(schemaContext, schemaDerivedData) {
        if (!this.isStopped) {
            this.schemaDerivedData = schemaDerivedData ?? this.schemaDerivedDataProvider(schemaContext.apiSchema);
            this.schemaContext = schemaContext;
            this.onSchemaLoadOrUpdateListeners.forEach((listener)=>{
                try {
                    listener(schemaContext);
                } catch (e) {
                    this.logger.error("An error was thrown from an 'onSchemaLoadOrUpdate' listener");
                    this.logger.error(e);
                }
            });
        }
    }
}
exports.SchemaManager = SchemaManager; //# sourceMappingURL=schemaManager.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/validationRules/NoIntrospection.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.NoIntrospection = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const index_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)");
const NoIntrospection = (context)=>({
        Field (node) {
            if (node.name.value === '__schema' || node.name.value === '__type') {
                context.reportError(new graphql_1.GraphQLError('GraphQL introspection is not allowed by Apollo Server, but the query contained __schema or __type. To enable introspection, pass introspection: true to ApolloServer in production', {
                    nodes: [
                        node
                    ],
                    extensions: {
                        validationErrorCode: index_js_1.ApolloServerValidationErrorCode.INTROSPECTION_DISABLED
                    }
                }));
            }
        }
    });
exports.NoIntrospection = NoIntrospection; //# sourceMappingURL=NoIntrospection.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createMaxRecursiveSelectionsRule = exports.DEFAULT_MAX_RECURSIVE_SELECTIONS = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const index_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)");
exports.DEFAULT_MAX_RECURSIVE_SELECTIONS = 10000000;
class RecursiveSelectionValidationContext {
    constructor(selectionCountLimit, context){
        this.selectionCountLimit = selectionCountLimit;
        this.context = context;
        this.fragmentInfo = new Map();
        this.operationInfo = new Map();
        this.fragmentRecursiveSelectionCount = new Map();
    }
    getExecutionDefinitionInfo() {
        if (this.currentFragment !== undefined) {
            let entry = this.fragmentInfo.get(this.currentFragment);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.fragmentInfo.set(this.currentFragment, entry);
            }
            return entry;
        }
        if (this.currentOperation !== undefined) {
            let entry = this.operationInfo.get(this.currentOperation);
            if (!entry) {
                entry = {
                    selectionCount: 0,
                    fragmentSpreads: new Map()
                };
                this.operationInfo.set(this.currentOperation, entry);
            }
            return entry;
        }
        return undefined;
    }
    processSelection(fragmentSpreadName) {
        const definitionInfo = this.getExecutionDefinitionInfo();
        if (!definitionInfo) {
            return;
        }
        definitionInfo.selectionCount++;
        if (fragmentSpreadName !== undefined) {
            let spreadCount = (definitionInfo.fragmentSpreads.get(fragmentSpreadName) ?? 0) + 1;
            definitionInfo.fragmentSpreads.set(fragmentSpreadName, spreadCount);
        }
    }
    enterFragment(fragment) {
        this.currentFragment = fragment;
    }
    leaveFragment() {
        this.currentFragment = undefined;
    }
    enterOperation(operation) {
        this.currentOperation = operation;
    }
    leaveOperation() {
        this.currentOperation = undefined;
    }
    computeFragmentRecursiveSelectionsCount(fragment) {
        const cachedCount = this.fragmentRecursiveSelectionCount.get(fragment);
        if (cachedCount === null) {
            return 0;
        }
        if (cachedCount !== undefined) {
            return cachedCount;
        }
        this.fragmentRecursiveSelectionCount.set(fragment, null);
        const definitionInfo = this.fragmentInfo.get(fragment);
        let count = 0;
        if (definitionInfo) {
            count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
        }
        this.fragmentRecursiveSelectionCount.set(fragment, count);
        return count;
    }
    reportError(operation) {
        const operationName = operation ? `Operation "${operation}"` : 'Anonymous operation';
        this.context.reportError(new graphql_1.GraphQLError(`${operationName} recursively requests too many selections.`, {
            nodes: [],
            extensions: {
                validationErrorCode: index_js_1.ApolloServerValidationErrorCode.MAX_RECURSIVE_SELECTIONS_EXCEEDED
            }
        }));
    }
    checkLimitExceeded() {
        for (const [operation, definitionInfo] of this.operationInfo){
            let count = definitionInfo.selectionCount;
            for (const [fragment, spreadCount] of definitionInfo.fragmentSpreads){
                count += spreadCount * this.computeFragmentRecursiveSelectionsCount(fragment);
            }
            if (count > this.selectionCountLimit) {
                this.reportError(operation);
            }
        }
    }
}
function createMaxRecursiveSelectionsRule(limit) {
    return (context)=>{
        const selectionContext = new RecursiveSelectionValidationContext(limit, context);
        return {
            Field () {
                selectionContext.processSelection();
            },
            InlineFragment () {
                selectionContext.processSelection();
            },
            FragmentSpread (node) {
                selectionContext.processSelection(node.name.value);
            },
            FragmentDefinition: {
                enter (node) {
                    selectionContext.enterFragment(node.name.value);
                },
                leave () {
                    selectionContext.leaveFragment();
                }
            },
            OperationDefinition: {
                enter (node) {
                    selectionContext.enterOperation(node.name?.value ?? null);
                },
                leave () {
                    selectionContext.leaveOperation();
                }
            },
            Document: {
                leave () {
                    selectionContext.checkLimitExceeded();
                }
            }
        };
    };
}
exports.createMaxRecursiveSelectionsRule = createMaxRecursiveSelectionsRule; //# sourceMappingURL=RecursiveSelectionsLimit.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/validationRules/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createMaxRecursiveSelectionsRule = exports.DEFAULT_MAX_RECURSIVE_SELECTIONS = exports.NoIntrospection = void 0;
var NoIntrospection_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/validationRules/NoIntrospection.js [app-route] (ecmascript)");
Object.defineProperty(exports, "NoIntrospection", {
    enumerable: true,
    get: function() {
        return NoIntrospection_js_1.NoIntrospection;
    }
});
var RecursiveSelectionsLimit_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/validationRules/RecursiveSelectionsLimit.js [app-route] (ecmascript)");
Object.defineProperty(exports, "DEFAULT_MAX_RECURSIVE_SELECTIONS", {
    enumerable: true,
    get: function() {
        return RecursiveSelectionsLimit_js_1.DEFAULT_MAX_RECURSIVE_SELECTIONS;
    }
});
Object.defineProperty(exports, "createMaxRecursiveSelectionsRule", {
    enumerable: true,
    get: function() {
        return RecursiveSelectionsLimit_js_1.createMaxRecursiveSelectionsRule;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/cacheControl/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApolloServerPluginCacheControl = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const cachePolicy_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/cachePolicy.js [app-route] (ecmascript)");
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
const lru_cache_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/lru-cache/index.js [app-route] (ecmascript)"));
function ApolloServerPluginCacheControl(options = Object.create(null)) {
    let typeAnnotationCache;
    let fieldAnnotationCache;
    return (0, internalPlugin_js_1.internalPlugin)({
        __internal_plugin_id__: 'CacheControl',
        __is_disabled_plugin__: false,
        async serverWillStart ({ schema }) {
            typeAnnotationCache = new lru_cache_1.default({
                max: Object.values(schema.getTypeMap()).filter(graphql_1.isCompositeType).length
            });
            fieldAnnotationCache = new lru_cache_1.default({
                max: Object.values(schema.getTypeMap()).filter(graphql_1.isObjectType).flatMap((t)=>Object.values(t.getFields())).length + Object.values(schema.getTypeMap()).filter(graphql_1.isInterfaceType).flatMap((t)=>Object.values(t.getFields())).length
            });
            return undefined;
        },
        async requestDidStart (requestContext) {
            function memoizedCacheAnnotationFromType(t) {
                const existing = typeAnnotationCache.get(t);
                if (existing) {
                    return existing;
                }
                const annotation = cacheAnnotationFromType(t);
                typeAnnotationCache.set(t, annotation);
                return annotation;
            }
            function memoizedCacheAnnotationFromField(field) {
                const existing = fieldAnnotationCache.get(field);
                if (existing) {
                    return existing;
                }
                const annotation = cacheAnnotationFromField(field);
                fieldAnnotationCache.set(field, annotation);
                return annotation;
            }
            const defaultMaxAge = options.defaultMaxAge ?? 0;
            const calculateHttpHeaders = options.calculateHttpHeaders ?? true;
            const { __testing__cacheHints } = options;
            return {
                async executionDidStart () {
                    if (isRestricted(requestContext.overallCachePolicy)) {
                        const fakeFieldPolicy = (0, cachePolicy_js_1.newCachePolicy)();
                        return {
                            willResolveField ({ info }) {
                                info.cacheControl = {
                                    setCacheHint: (dynamicHint)=>{
                                        fakeFieldPolicy.replace(dynamicHint);
                                    },
                                    cacheHint: fakeFieldPolicy,
                                    cacheHintFromType: memoizedCacheAnnotationFromType
                                };
                            }
                        };
                    }
                    return {
                        willResolveField ({ info }) {
                            const fieldPolicy = (0, cachePolicy_js_1.newCachePolicy)();
                            let inheritMaxAge = false;
                            const targetType = (0, graphql_1.getNamedType)(info.returnType);
                            if ((0, graphql_1.isCompositeType)(targetType)) {
                                const typeAnnotation = memoizedCacheAnnotationFromType(targetType);
                                fieldPolicy.replace(typeAnnotation);
                                inheritMaxAge = !!typeAnnotation.inheritMaxAge;
                            }
                            const fieldAnnotation = memoizedCacheAnnotationFromField(info.parentType.getFields()[info.fieldName]);
                            if (fieldAnnotation.inheritMaxAge && fieldPolicy.maxAge === undefined) {
                                inheritMaxAge = true;
                                if (fieldAnnotation.scope) {
                                    fieldPolicy.replace({
                                        scope: fieldAnnotation.scope
                                    });
                                }
                            } else {
                                fieldPolicy.replace(fieldAnnotation);
                            }
                            info.cacheControl = {
                                setCacheHint: (dynamicHint)=>{
                                    fieldPolicy.replace(dynamicHint);
                                },
                                cacheHint: fieldPolicy,
                                cacheHintFromType: memoizedCacheAnnotationFromType
                            };
                            return ()=>{
                                if (fieldPolicy.maxAge === undefined && ((0, graphql_1.isCompositeType)(targetType) && !inheritMaxAge || !info.path.prev)) {
                                    fieldPolicy.restrict({
                                        maxAge: defaultMaxAge
                                    });
                                }
                                if (__testing__cacheHints && isRestricted(fieldPolicy)) {
                                    const path = (0, graphql_1.responsePathAsArray)(info.path).join('.');
                                    if (__testing__cacheHints.has(path)) {
                                        throw Error("shouldn't happen: addHint should only be called once per path");
                                    }
                                    __testing__cacheHints.set(path, {
                                        maxAge: fieldPolicy.maxAge,
                                        scope: fieldPolicy.scope
                                    });
                                }
                                requestContext.overallCachePolicy.restrict(fieldPolicy);
                            };
                        }
                    };
                },
                async willSendResponse (requestContext) {
                    if (!calculateHttpHeaders) {
                        return;
                    }
                    const { response, overallCachePolicy } = requestContext;
                    const existingCacheControlHeader = parseExistingCacheControlHeader(response.http.headers.get('cache-control'));
                    if (existingCacheControlHeader.kind === 'unparsable') {
                        return;
                    }
                    const cachePolicy = (0, cachePolicy_js_1.newCachePolicy)();
                    cachePolicy.replace(overallCachePolicy);
                    if (existingCacheControlHeader.kind === 'parsable-and-cacheable') {
                        cachePolicy.restrict(existingCacheControlHeader.hint);
                    }
                    const policyIfCacheable = cachePolicy.policyIfCacheable();
                    if (policyIfCacheable && existingCacheControlHeader.kind !== 'uncacheable' && response.body.kind === 'single' && !response.body.singleResult.errors) {
                        response.http.headers.set('cache-control', `max-age=${policyIfCacheable.maxAge}, ${policyIfCacheable.scope.toLowerCase()}`);
                    } else if (calculateHttpHeaders !== 'if-cacheable') {
                        response.http.headers.set('cache-control', CACHE_CONTROL_HEADER_UNCACHEABLE);
                    }
                }
            };
        }
    });
}
exports.ApolloServerPluginCacheControl = ApolloServerPluginCacheControl;
const CACHE_CONTROL_HEADER_CACHEABLE_REGEXP = /^max-age=(\d+), (public|private)$/;
const CACHE_CONTROL_HEADER_UNCACHEABLE = 'no-store';
function parseExistingCacheControlHeader(header) {
    if (!header) {
        return {
            kind: 'no-header'
        };
    }
    if (header === CACHE_CONTROL_HEADER_UNCACHEABLE) {
        return {
            kind: 'uncacheable'
        };
    }
    const match = CACHE_CONTROL_HEADER_CACHEABLE_REGEXP.exec(header);
    if (!match) {
        return {
            kind: 'unparsable'
        };
    }
    return {
        kind: 'parsable-and-cacheable',
        hint: {
            maxAge: +match[1],
            scope: match[2] === 'public' ? 'PUBLIC' : 'PRIVATE'
        }
    };
}
function cacheAnnotationFromDirectives(directives) {
    if (!directives) return undefined;
    const cacheControlDirective = directives.find((directive)=>directive.name.value === 'cacheControl');
    if (!cacheControlDirective) return undefined;
    if (!cacheControlDirective.arguments) return undefined;
    const maxAgeArgument = cacheControlDirective.arguments.find((argument)=>argument.name.value === 'maxAge');
    const scopeArgument = cacheControlDirective.arguments.find((argument)=>argument.name.value === 'scope');
    const inheritMaxAgeArgument = cacheControlDirective.arguments.find((argument)=>argument.name.value === 'inheritMaxAge');
    const scopeString = scopeArgument?.value?.kind === 'EnumValue' ? scopeArgument.value.value : undefined;
    const scope = scopeString === 'PUBLIC' || scopeString === 'PRIVATE' ? scopeString : undefined;
    if (inheritMaxAgeArgument?.value?.kind === 'BooleanValue' && inheritMaxAgeArgument.value.value) {
        return {
            inheritMaxAge: true,
            scope
        };
    }
    return {
        maxAge: maxAgeArgument?.value?.kind === 'IntValue' ? parseInt(maxAgeArgument.value.value) : undefined,
        scope
    };
}
function cacheAnnotationFromType(t) {
    if (t.astNode) {
        const hint = cacheAnnotationFromDirectives(t.astNode.directives);
        if (hint) {
            return hint;
        }
    }
    if (t.extensionASTNodes) {
        for (const node of t.extensionASTNodes){
            const hint = cacheAnnotationFromDirectives(node.directives);
            if (hint) {
                return hint;
            }
        }
    }
    return {};
}
function cacheAnnotationFromField(field) {
    if (field.astNode) {
        const hint = cacheAnnotationFromDirectives(field.astNode.directives);
        if (hint) {
            return hint;
        }
    }
    return {};
}
function isRestricted(hint) {
    return hint.maxAge !== undefined || hint.scope !== undefined;
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/traceTreeBuilder.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dateToProtoTimestamp = exports.TraceTreeBuilder = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const usage_reporting_protobuf_1 = __turbopack_context__.r("[project]/node_modules/@apollo/usage-reporting-protobuf/generated/cjs/protobuf.js [app-route] (ecmascript)");
const UnreachableCaseError_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/UnreachableCaseError.js [app-route] (ecmascript)");
function internalError(message) {
    return new Error(`[internal apollo-server error] ${message}`);
}
class TraceTreeBuilder {
    constructor(options){
        this.rootNode = new usage_reporting_protobuf_1.Trace.Node();
        this.trace = new usage_reporting_protobuf_1.Trace({
            root: this.rootNode,
            fieldExecutionWeight: 1
        });
        this.stopped = false;
        this.nodes = new Map([
            [
                responsePathAsString(),
                this.rootNode
            ]
        ]);
        const { sendErrors, maskedBy } = options;
        if (!sendErrors || 'masked' in sendErrors) {
            this.transformError = ()=>new graphql_1.GraphQLError('<masked>', {
                    extensions: {
                        maskedBy
                    }
                });
        } else if ('transform' in sendErrors) {
            this.transformError = sendErrors.transform;
        } else if ('unmodified' in sendErrors) {
            this.transformError = null;
        } else {
            throw new UnreachableCaseError_js_1.UnreachableCaseError(sendErrors);
        }
    }
    startTiming() {
        if (this.startHrTime) {
            throw internalError('startTiming called twice!');
        }
        if (this.stopped) {
            throw internalError('startTiming called after stopTiming!');
        }
        this.trace.startTime = dateToProtoTimestamp(new Date());
        this.startHrTime = process.hrtime();
    }
    stopTiming() {
        if (!this.startHrTime) {
            throw internalError('stopTiming called before startTiming!');
        }
        if (this.stopped) {
            throw internalError('stopTiming called twice!');
        }
        this.trace.durationNs = durationHrTimeToNanos(process.hrtime(this.startHrTime));
        this.trace.endTime = dateToProtoTimestamp(new Date());
        this.stopped = true;
    }
    willResolveField(info) {
        if (!this.startHrTime) {
            throw internalError('willResolveField called before startTiming!');
        }
        if (this.stopped) {
            return ()=>{};
        }
        const path = info.path;
        const node = this.newNode(path);
        node.type = info.returnType.toString();
        node.parentType = info.parentType.toString();
        node.startTime = durationHrTimeToNanos(process.hrtime(this.startHrTime));
        if (typeof path.key === 'string' && path.key !== info.fieldName) {
            node.originalFieldName = info.fieldName;
        }
        return ()=>{
            node.endTime = durationHrTimeToNanos(process.hrtime(this.startHrTime));
        };
    }
    didEncounterErrors(errors) {
        errors.forEach((err)=>{
            if (err.extensions?.serviceName) {
                return;
            }
            const errorForReporting = this.transformAndNormalizeError(err);
            if (errorForReporting === null) {
                return;
            }
            this.addProtobufError(errorForReporting.path, errorToProtobufError(errorForReporting));
        });
    }
    addProtobufError(path, error) {
        if (!this.startHrTime) {
            throw internalError('addProtobufError called before startTiming!');
        }
        if (this.stopped) {
            throw internalError('addProtobufError called after stopTiming!');
        }
        let node = this.rootNode;
        if (Array.isArray(path)) {
            const specificNode = this.nodes.get(path.join('.'));
            if (specificNode) {
                node = specificNode;
            } else {
                const responsePath = responsePathFromArray(path, this.rootNode);
                if (!responsePath) {
                    throw internalError('addProtobufError called with invalid path!');
                }
                node = this.newNode(responsePath);
            }
        }
        node.error.push(error);
    }
    newNode(path) {
        const node = new usage_reporting_protobuf_1.Trace.Node();
        const id = path.key;
        if (typeof id === 'number') {
            node.index = id;
        } else {
            node.responseName = id;
        }
        this.nodes.set(responsePathAsString(path), node);
        const parentNode = this.ensureParentNode(path);
        parentNode.child.push(node);
        return node;
    }
    ensureParentNode(path) {
        const parentPath = responsePathAsString(path.prev);
        const parentNode = this.nodes.get(parentPath);
        if (parentNode) {
            return parentNode;
        }
        return this.newNode(path.prev);
    }
    transformAndNormalizeError(err) {
        if (this.transformError) {
            const clonedError = Object.assign(Object.create(Object.getPrototypeOf(err)), err);
            const rewrittenError = this.transformError(clonedError);
            if (rewrittenError === null) {
                return null;
            }
            if (!(rewrittenError instanceof graphql_1.GraphQLError)) {
                return err;
            }
            return new graphql_1.GraphQLError(rewrittenError.message, {
                nodes: err.nodes,
                source: err.source,
                positions: err.positions,
                path: err.path,
                originalError: err.originalError,
                extensions: rewrittenError.extensions || err.extensions
            });
        }
        return err;
    }
}
exports.TraceTreeBuilder = TraceTreeBuilder;
function durationHrTimeToNanos(hrtime) {
    return hrtime[0] * 1e9 + hrtime[1];
}
function responsePathAsString(p) {
    if (p === undefined) {
        return '';
    }
    let res = String(p.key);
    while((p = p.prev) !== undefined){
        res = `${p.key}.${res}`;
    }
    return res;
}
function responsePathFromArray(path, node) {
    let responsePath;
    let nodePtr = node;
    for (const key of path){
        nodePtr = nodePtr?.child?.find((child)=>child.responseName === key);
        responsePath = {
            key,
            prev: responsePath,
            typename: nodePtr?.type ?? undefined
        };
    }
    return responsePath;
}
function errorToProtobufError(error) {
    return new usage_reporting_protobuf_1.Trace.Error({
        message: error.message,
        location: (error.locations || []).map(({ line, column })=>new usage_reporting_protobuf_1.Trace.Location({
                line,
                column
            })),
        json: JSON.stringify(error)
    });
}
function dateToProtoTimestamp(date) {
    const totalMillis = +date;
    const millis = totalMillis % 1000;
    return new usage_reporting_protobuf_1.google.protobuf.Timestamp({
        seconds: (totalMillis - millis) / 1000,
        nanos: millis * 1e6
    });
}
exports.dateToProtoTimestamp = dateToProtoTimestamp; //# sourceMappingURL=traceTreeBuilder.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/iterateOverTrace.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.iterateOverTrace = void 0;
function iterateOverTrace(trace, f, includePath) {
    const rootPath = includePath ? new RootCollectingPathsResponseNamePath() : notCollectingPathsResponseNamePath;
    if (trace.root) {
        if (iterateOverTraceNode(trace.root, rootPath, f)) return;
    }
    if (trace.queryPlan) {
        if (iterateOverQueryPlan(trace.queryPlan, rootPath, f)) return;
    }
}
exports.iterateOverTrace = iterateOverTrace;
function iterateOverQueryPlan(node, rootPath, f) {
    if (!node) return false;
    if (node.fetch?.trace?.root && node.fetch.serviceName) {
        return iterateOverTraceNode(node.fetch.trace.root, rootPath.child(`service:${node.fetch.serviceName}`), f);
    }
    if (node.flatten?.node) {
        return iterateOverQueryPlan(node.flatten.node, rootPath, f);
    }
    if (node.parallel?.nodes) {
        return node.parallel.nodes.some((node)=>iterateOverQueryPlan(node, rootPath, f));
    }
    if (node.sequence?.nodes) {
        return node.sequence.nodes.some((node)=>iterateOverQueryPlan(node, rootPath, f));
    }
    return false;
}
function iterateOverTraceNode(node, path, f) {
    if (f(node, path)) {
        return true;
    }
    return node.child?.some((child)=>{
        const childPath = child.responseName ? path.child(child.responseName) : path;
        return iterateOverTraceNode(child, childPath, f);
    }) ?? false;
}
const notCollectingPathsResponseNamePath = {
    toArray () {
        throw Error('not collecting paths!');
    },
    child () {
        return this;
    }
};
class RootCollectingPathsResponseNamePath {
    toArray() {
        return [];
    }
    child(responseName) {
        return new ChildCollectingPathsResponseNamePath(responseName, this);
    }
}
class ChildCollectingPathsResponseNamePath {
    constructor(responseName, prev){
        this.responseName = responseName;
        this.prev = prev;
    }
    toArray() {
        const out = [];
        let curr = this;
        while(curr instanceof ChildCollectingPathsResponseNamePath){
            out.push(curr.responseName);
            curr = curr.prev;
        }
        return out.reverse();
    }
    child(responseName) {
        return new ChildCollectingPathsResponseNamePath(responseName, this);
    }
} //# sourceMappingURL=iterateOverTrace.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/durationHistogram.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.DurationHistogram = void 0;
class DurationHistogram {
    toArray() {
        let bufferedZeroes = 0;
        const outputArray = [];
        for (const value of this.buckets){
            if (value === 0) {
                bufferedZeroes++;
            } else {
                if (bufferedZeroes === 1) {
                    outputArray.push(0);
                } else if (bufferedZeroes !== 0) {
                    outputArray.push(-bufferedZeroes);
                }
                outputArray.push(Math.floor(value));
                bufferedZeroes = 0;
            }
        }
        return outputArray;
    }
    static durationToBucket(durationNs) {
        const log = Math.log(durationNs / 1000.0);
        const unboundedBucket = Math.ceil(log / DurationHistogram.EXPONENT_LOG);
        return unboundedBucket <= 0 || Number.isNaN(unboundedBucket) ? 0 : unboundedBucket >= DurationHistogram.BUCKET_COUNT ? DurationHistogram.BUCKET_COUNT - 1 : unboundedBucket;
    }
    incrementDuration(durationNs, value = 1) {
        this.incrementBucket(DurationHistogram.durationToBucket(durationNs), value);
        return this;
    }
    incrementBucket(bucket, value = 1) {
        if (bucket >= DurationHistogram.BUCKET_COUNT) {
            throw Error('Bucket is out of bounds of the buckets array');
        }
        if (bucket >= this.buckets.length) {
            const oldLength = this.buckets.length;
            this.buckets.length = bucket + 1;
            this.buckets.fill(0, oldLength);
        }
        this.buckets[bucket] += value;
    }
    combine(otherHistogram) {
        for(let i = 0; i < otherHistogram.buckets.length; i++){
            this.incrementBucket(i, otherHistogram.buckets[i]);
        }
    }
    constructor(options){
        const initSize = options?.initSize || 74;
        const buckets = options?.buckets;
        const arrayInitSize = Math.max(buckets?.length || 0, initSize);
        this.buckets = Array(arrayInitSize).fill(0);
        if (buckets) {
            buckets.forEach((val, index)=>this.buckets[index] = val);
        }
    }
}
exports.DurationHistogram = DurationHistogram;
DurationHistogram.BUCKET_COUNT = 384;
DurationHistogram.EXPONENT_LOG = Math.log(1.1); //# sourceMappingURL=durationHistogram.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/defaultSendOperationsAsTrace.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.defaultSendOperationsAsTrace = void 0;
const lru_cache_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/lru-cache/index.js [app-route] (ecmascript)"));
const iterateOverTrace_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/iterateOverTrace.js [app-route] (ecmascript)");
const durationHistogram_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/durationHistogram.js [app-route] (ecmascript)");
function defaultSendOperationsAsTrace() {
    const cache = new lru_cache_1.default({
        maxSize: Math.pow(2, 20),
        sizeCalculation: (_val, key)=>{
            return key && Buffer.byteLength(key) || 0;
        }
    });
    return (trace, statsReportKey)=>{
        const endTimeSeconds = trace.endTime?.seconds;
        if (endTimeSeconds == null) {
            throw Error('programming error: endTime not set on trace');
        }
        const hasErrors = traceHasErrors(trace);
        const cacheKey = JSON.stringify([
            statsReportKey,
            durationHistogram_js_1.DurationHistogram.durationToBucket(trace.durationNs),
            Math.floor(endTimeSeconds / 60),
            hasErrors ? Math.floor(endTimeSeconds / 5) : ''
        ]);
        if (cache.get(cacheKey)) {
            return false;
        }
        cache.set(cacheKey, true);
        return true;
    };
}
exports.defaultSendOperationsAsTrace = defaultSendOperationsAsTrace;
function traceHasErrors(trace) {
    let hasErrors = false;
    function traceNodeStats(node) {
        if ((node.error?.length ?? 0) > 0) {
            hasErrors = true;
        }
        return hasErrors;
    }
    (0, iterateOverTrace_js_1.iterateOverTrace)(trace, traceNodeStats, false);
    return hasErrors;
} //# sourceMappingURL=defaultSendOperationsAsTrace.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/operationDerivedDataCache.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.operationDerivedDataCacheKey = exports.createOperationDerivedDataCache = void 0;
const lru_cache_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/lru-cache/index.js [app-route] (ecmascript)"));
function createOperationDerivedDataCache({ logger }) {
    let lastWarn;
    let lastDisposals = 0;
    return new lru_cache_1.default({
        sizeCalculation (obj) {
            return Buffer.byteLength(JSON.stringify(obj), 'utf8');
        },
        maxSize: Math.pow(2, 20) * 10,
        dispose () {
            lastDisposals++;
            if (!lastWarn || new Date().getTime() - lastWarn.getTime() > 60000) {
                lastWarn = new Date();
                logger.warn([
                    'This server is processing a high number of unique operations.  ',
                    `A total of ${lastDisposals} records have been `,
                    'ejected from the ApolloServerPluginUsageReporting signature cache in the past ',
                    'interval.  If you see this warning frequently, please open an ',
                    'issue on the Apollo Server repository.'
                ].join(''));
                lastDisposals = 0;
            }
        }
    });
}
exports.createOperationDerivedDataCache = createOperationDerivedDataCache;
function operationDerivedDataCacheKey(queryHash, operationName) {
    return `${queryHash}${operationName && ':' + operationName}`;
}
exports.operationDerivedDataCacheKey = operationDerivedDataCacheKey; //# sourceMappingURL=operationDerivedDataCache.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/stats.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.OurContextualizedStats = exports.OurReport = exports.SizeEstimator = void 0;
const usage_reporting_protobuf_1 = __turbopack_context__.r("[project]/node_modules/@apollo/usage-reporting-protobuf/generated/cjs/protobuf.js [app-route] (ecmascript)");
const durationHistogram_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/durationHistogram.js [app-route] (ecmascript)");
const iterateOverTrace_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/iterateOverTrace.js [app-route] (ecmascript)");
class SizeEstimator {
    constructor(){
        this.bytes = 0;
    }
}
exports.SizeEstimator = SizeEstimator;
class OurReport {
    constructor(header){
        this.header = header;
        this.tracesPreAggregated = false;
        this.tracesPerQuery = Object.create(null);
        this.endTime = null;
        this.operationCount = 0;
        this.sizeEstimator = new SizeEstimator();
    }
    ensureCountsAreIntegers() {
        for (const tracesAndStats of Object.values(this.tracesPerQuery)){
            tracesAndStats.ensureCountsAreIntegers();
        }
    }
    addTrace({ statsReportKey, trace, asTrace, referencedFieldsByType, maxTraceBytes = 10 * 1024 * 1024, nonFtv1ErrorPaths }) {
        const tracesAndStats = this.getTracesAndStats({
            statsReportKey,
            referencedFieldsByType
        });
        if (asTrace) {
            const encodedTrace = usage_reporting_protobuf_1.Trace.encode(trace).finish();
            if (!isNaN(maxTraceBytes) && encodedTrace.length > maxTraceBytes) {
                tracesAndStats.statsWithContext.addTrace(trace, this.sizeEstimator, nonFtv1ErrorPaths);
            } else {
                tracesAndStats.trace.push(encodedTrace);
                this.sizeEstimator.bytes += 2 + encodedTrace.length;
            }
        } else {
            tracesAndStats.statsWithContext.addTrace(trace, this.sizeEstimator, nonFtv1ErrorPaths);
        }
    }
    getTracesAndStats({ statsReportKey, referencedFieldsByType }) {
        const existing = this.tracesPerQuery[statsReportKey];
        if (existing) {
            return existing;
        }
        this.sizeEstimator.bytes += estimatedBytesForString(statsReportKey);
        for (const [typeName, referencedFieldsForType] of Object.entries(referencedFieldsByType)){
            this.sizeEstimator.bytes += 2 + 2;
            if (referencedFieldsForType.isInterface) {
                this.sizeEstimator.bytes += 2;
            }
            this.sizeEstimator.bytes += estimatedBytesForString(typeName);
            for (const fieldName of referencedFieldsForType.fieldNames){
                this.sizeEstimator.bytes += estimatedBytesForString(fieldName);
            }
        }
        return this.tracesPerQuery[statsReportKey] = new OurTracesAndStats(referencedFieldsByType);
    }
}
exports.OurReport = OurReport;
class OurTracesAndStats {
    constructor(referencedFieldsByType){
        this.referencedFieldsByType = referencedFieldsByType;
        this.trace = [];
        this.statsWithContext = new StatsByContext();
        this.internalTracesContributingToStats = [];
    }
    ensureCountsAreIntegers() {
        this.statsWithContext.ensureCountsAreIntegers();
    }
}
class StatsByContext {
    constructor(){
        this.map = Object.create(null);
    }
    toArray() {
        return Object.values(this.map);
    }
    ensureCountsAreIntegers() {
        for (const contextualizedStats of Object.values(this.map)){
            contextualizedStats.ensureCountsAreIntegers();
        }
    }
    addTrace(trace, sizeEstimator, nonFtv1ErrorPaths) {
        this.getContextualizedStats(trace, sizeEstimator).addTrace(trace, sizeEstimator, nonFtv1ErrorPaths);
    }
    getContextualizedStats(trace, sizeEstimator) {
        const statsContext = {
            clientName: trace.clientName,
            clientVersion: trace.clientVersion
        };
        const statsContextKey = JSON.stringify(statsContext);
        const existing = this.map[statsContextKey];
        if (existing) {
            return existing;
        }
        sizeEstimator.bytes += 20 + estimatedBytesForString(trace.clientName) + estimatedBytesForString(trace.clientVersion);
        const contextualizedStats = new OurContextualizedStats(statsContext);
        this.map[statsContextKey] = contextualizedStats;
        return contextualizedStats;
    }
}
class OurContextualizedStats {
    constructor(context){
        this.context = context;
        this.queryLatencyStats = new OurQueryLatencyStats();
        this.perTypeStat = Object.create(null);
    }
    ensureCountsAreIntegers() {
        for (const typeStat of Object.values(this.perTypeStat)){
            typeStat.ensureCountsAreIntegers();
        }
    }
    addTrace(trace, sizeEstimator, nonFtv1ErrorPaths = []) {
        const { fieldExecutionWeight } = trace;
        if (!fieldExecutionWeight) {
            this.queryLatencyStats.requestsWithoutFieldInstrumentation++;
        }
        this.queryLatencyStats.requestCount++;
        if (trace.fullQueryCacheHit) {
            this.queryLatencyStats.cacheLatencyCount.incrementDuration(trace.durationNs);
            this.queryLatencyStats.cacheHits++;
        } else {
            this.queryLatencyStats.latencyCount.incrementDuration(trace.durationNs);
        }
        if (!trace.fullQueryCacheHit && trace.cachePolicy?.maxAgeNs != null) {
            switch(trace.cachePolicy.scope){
                case usage_reporting_protobuf_1.Trace.CachePolicy.Scope.PRIVATE:
                    this.queryLatencyStats.privateCacheTtlCount.incrementDuration(trace.cachePolicy.maxAgeNs);
                    break;
                case usage_reporting_protobuf_1.Trace.CachePolicy.Scope.PUBLIC:
                    this.queryLatencyStats.publicCacheTtlCount.incrementDuration(trace.cachePolicy.maxAgeNs);
                    break;
            }
        }
        if (trace.persistedQueryHit) {
            this.queryLatencyStats.persistedQueryHits++;
        }
        if (trace.persistedQueryRegister) {
            this.queryLatencyStats.persistedQueryMisses++;
        }
        if (trace.forbiddenOperation) {
            this.queryLatencyStats.forbiddenOperationCount++;
        }
        if (trace.registeredOperation) {
            this.queryLatencyStats.registeredOperationCount++;
        }
        let hasError = false;
        const errorPathStats = new Set();
        const traceNodeStats = (node, path)=>{
            if (node.error?.length) {
                hasError = true;
                let currPathErrorStats = this.queryLatencyStats.rootErrorStats;
                path.toArray().forEach((subPath)=>{
                    currPathErrorStats = currPathErrorStats.getChild(subPath, sizeEstimator);
                });
                errorPathStats.add(currPathErrorStats);
                currPathErrorStats.errorsCount += node.error.length;
            }
            if (fieldExecutionWeight) {
                const fieldName = node.originalFieldName || node.responseName;
                if (node.parentType && fieldName && node.type && node.endTime != null && node.startTime != null && node.endTime >= node.startTime) {
                    const typeStat = this.getTypeStat(node.parentType, sizeEstimator);
                    const fieldStat = typeStat.getFieldStat(fieldName, node.type, sizeEstimator);
                    fieldStat.errorsCount += node.error?.length ?? 0;
                    fieldStat.observedExecutionCount++;
                    fieldStat.estimatedExecutionCount += fieldExecutionWeight;
                    fieldStat.requestsWithErrorsCount += (node.error?.length ?? 0) > 0 ? 1 : 0;
                    fieldStat.latencyCount.incrementDuration(node.endTime - node.startTime, fieldExecutionWeight);
                }
            }
            return false;
        };
        (0, iterateOverTrace_js_1.iterateOverTrace)(trace, traceNodeStats, true);
        for (const { subgraph, path } of nonFtv1ErrorPaths){
            hasError = true;
            if (path) {
                let currPathErrorStats = this.queryLatencyStats.rootErrorStats.getChild(`service:${subgraph}`, sizeEstimator);
                path.forEach((subPath)=>{
                    if (typeof subPath === 'string') {
                        currPathErrorStats = currPathErrorStats.getChild(subPath, sizeEstimator);
                    }
                });
                errorPathStats.add(currPathErrorStats);
                currPathErrorStats.errorsCount += 1;
            }
        }
        for (const errorPath of errorPathStats){
            errorPath.requestsWithErrorsCount += 1;
        }
        if (hasError) {
            this.queryLatencyStats.requestsWithErrorsCount++;
        }
    }
    getTypeStat(parentType, sizeEstimator) {
        const existing = this.perTypeStat[parentType];
        if (existing) {
            return existing;
        }
        sizeEstimator.bytes += estimatedBytesForString(parentType);
        const typeStat = new OurTypeStat();
        this.perTypeStat[parentType] = typeStat;
        return typeStat;
    }
}
exports.OurContextualizedStats = OurContextualizedStats;
class OurQueryLatencyStats {
    constructor(){
        this.latencyCount = new durationHistogram_js_1.DurationHistogram();
        this.requestCount = 0;
        this.requestsWithoutFieldInstrumentation = 0;
        this.cacheHits = 0;
        this.persistedQueryHits = 0;
        this.persistedQueryMisses = 0;
        this.cacheLatencyCount = new durationHistogram_js_1.DurationHistogram();
        this.rootErrorStats = new OurPathErrorStats();
        this.requestsWithErrorsCount = 0;
        this.publicCacheTtlCount = new durationHistogram_js_1.DurationHistogram();
        this.privateCacheTtlCount = new durationHistogram_js_1.DurationHistogram();
        this.registeredOperationCount = 0;
        this.forbiddenOperationCount = 0;
    }
}
class OurPathErrorStats {
    constructor(){
        this.children = Object.create(null);
        this.errorsCount = 0;
        this.requestsWithErrorsCount = 0;
    }
    getChild(subPath, sizeEstimator) {
        const existing = this.children[subPath];
        if (existing) {
            return existing;
        }
        const child = new OurPathErrorStats();
        this.children[subPath] = child;
        sizeEstimator.bytes += estimatedBytesForString(subPath) + 4;
        return child;
    }
}
class OurTypeStat {
    constructor(){
        this.perFieldStat = Object.create(null);
    }
    getFieldStat(fieldName, returnType, sizeEstimator) {
        const existing = this.perFieldStat[fieldName];
        if (existing) {
            return existing;
        }
        sizeEstimator.bytes += estimatedBytesForString(fieldName) + estimatedBytesForString(returnType) + 10;
        const fieldStat = new OurFieldStat(returnType);
        this.perFieldStat[fieldName] = fieldStat;
        return fieldStat;
    }
    ensureCountsAreIntegers() {
        for (const fieldStat of Object.values(this.perFieldStat)){
            fieldStat.ensureCountsAreIntegers();
        }
    }
}
class OurFieldStat {
    constructor(returnType){
        this.returnType = returnType;
        this.errorsCount = 0;
        this.observedExecutionCount = 0;
        this.estimatedExecutionCount = 0;
        this.requestsWithErrorsCount = 0;
        this.latencyCount = new durationHistogram_js_1.DurationHistogram();
    }
    ensureCountsAreIntegers() {
        this.estimatedExecutionCount = Math.floor(this.estimatedExecutionCount);
    }
}
function estimatedBytesForString(s) {
    return 2 + Buffer.byteLength(s);
} //# sourceMappingURL=stats.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/traceDetails.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.makeTraceDetails = void 0;
const usage_reporting_protobuf_1 = __turbopack_context__.r("[project]/node_modules/@apollo/usage-reporting-protobuf/generated/cjs/protobuf.js [app-route] (ecmascript)");
function makeTraceDetails(variables, sendVariableValues, operationString) {
    const details = new usage_reporting_protobuf_1.Trace.Details();
    const variablesToRecord = (()=>{
        if (sendVariableValues && 'transform' in sendVariableValues) {
            const originalKeys = Object.keys(variables);
            try {
                const modifiedVariables = sendVariableValues.transform({
                    variables: variables,
                    operationString: operationString
                });
                return cleanModifiedVariables(originalKeys, modifiedVariables);
            } catch (e) {
                return handleVariableValueTransformError(originalKeys);
            }
        } else {
            return variables;
        }
    })();
    Object.keys(variablesToRecord).forEach((name)=>{
        if (!sendVariableValues || 'none' in sendVariableValues && sendVariableValues.none || 'all' in sendVariableValues && !sendVariableValues.all || 'exceptNames' in sendVariableValues && sendVariableValues.exceptNames.includes(name) || 'onlyNames' in sendVariableValues && !sendVariableValues.onlyNames.includes(name)) {
            details.variablesJson[name] = '';
        } else {
            try {
                details.variablesJson[name] = typeof variablesToRecord[name] === 'undefined' ? '' : JSON.stringify(variablesToRecord[name]);
            } catch (e) {
                details.variablesJson[name] = JSON.stringify('[Unable to convert value to JSON]');
            }
        }
    });
    return details;
}
exports.makeTraceDetails = makeTraceDetails;
function handleVariableValueTransformError(variableNames) {
    const modifiedVariables = Object.create(null);
    variableNames.forEach((name)=>{
        modifiedVariables[name] = '[PREDICATE_FUNCTION_ERROR]';
    });
    return modifiedVariables;
}
function cleanModifiedVariables(originalKeys, modifiedVariables) {
    const cleanedVariables = Object.create(null);
    originalKeys.forEach((name)=>{
        cleanedVariables[name] = modifiedVariables[name];
    });
    return cleanedVariables;
} //# sourceMappingURL=traceDetails.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/generated/packageVersion.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.packageVersion = void 0;
exports.packageVersion = "4.12.2"; //# sourceMappingURL=packageVersion.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaIsSubgraph.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.schemaIsSubgraph = void 0;
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
function schemaIsSubgraph(schema) {
    const serviceType = schema.getType('_Service');
    if (!(0, graphql_1.isObjectType)(serviceType)) {
        return false;
    }
    const sdlField = serviceType.getFields().sdl;
    if (!sdlField) {
        return false;
    }
    let sdlFieldType = sdlField.type;
    if ((0, graphql_1.isNonNullType)(sdlFieldType)) {
        sdlFieldType = sdlFieldType.ofType;
    }
    if (!(0, graphql_1.isScalarType)(sdlFieldType)) {
        return false;
    }
    return sdlFieldType.name == 'String';
}
exports.schemaIsSubgraph = schemaIsSubgraph; //# sourceMappingURL=schemaIsSubgraph.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/plugin.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.makeHTTPRequestHeaders = exports.ApolloServerPluginUsageReporting = void 0;
const usage_reporting_protobuf_1 = __turbopack_context__.r("[project]/node_modules/@apollo/usage-reporting-protobuf/generated/cjs/protobuf.js [app-route] (ecmascript)");
const utils_usagereporting_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.usagereporting/dist/index.js [app-route] (ecmascript)");
const async_retry_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/async-retry/lib/index.js [app-route] (ecmascript)"));
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const node_abort_controller_1 = __turbopack_context__.r("[project]/node_modules/node-abort-controller/index.js [app-route] (ecmascript)");
const node_fetch_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/node-fetch/lib/index.mjs [app-route] (ecmascript)"));
const os_1 = __importDefault(__turbopack_context__.r("[externals]/os [external] (os, cjs)"));
const zlib_1 = __turbopack_context__.r("[externals]/zlib [external] (zlib, cjs)");
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
const traceTreeBuilder_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/traceTreeBuilder.js [app-route] (ecmascript)");
const defaultSendOperationsAsTrace_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/defaultSendOperationsAsTrace.js [app-route] (ecmascript)");
const operationDerivedDataCache_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/operationDerivedDataCache.js [app-route] (ecmascript)");
const stats_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/stats.js [app-route] (ecmascript)");
const traceDetails_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/traceDetails.js [app-route] (ecmascript)");
const packageVersion_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/generated/packageVersion.js [app-route] (ecmascript)");
const computeCoreSchemaHash_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/computeCoreSchemaHash.js [app-route] (ecmascript)");
const schemaIsSubgraph_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaIsSubgraph.js [app-route] (ecmascript)");
const reportHeaderDefaults = {
    hostname: os_1.default.hostname(),
    agentVersion: `@apollo/server@${packageVersion_js_1.packageVersion}`,
    runtimeVersion: `node ${process.version}`,
    uname: `${os_1.default.platform()}, ${os_1.default.type()}, ${os_1.default.release()}, ${os_1.default.arch()})`
};
function ApolloServerPluginUsageReporting(options = Object.create(null)) {
    const fieldLevelInstrumentationOption = options.fieldLevelInstrumentation;
    const fieldLevelInstrumentation = typeof fieldLevelInstrumentationOption === 'number' ? async ()=>Math.random() < fieldLevelInstrumentationOption ? 1 / fieldLevelInstrumentationOption : 0 : fieldLevelInstrumentationOption ? fieldLevelInstrumentationOption : async ()=>true;
    let requestDidStartHandler = null;
    return (0, internalPlugin_js_1.internalPlugin)({
        __internal_plugin_id__: 'UsageReporting',
        __is_disabled_plugin__: false,
        async requestDidStart (requestContext) {
            if (requestDidStartHandler) {
                return requestDidStartHandler(requestContext);
            }
            return {};
        },
        async serverWillStart ({ logger: serverLogger, apollo, startedInBackground, schema }) {
            const logger = options.logger ?? serverLogger;
            const { key, graphRef } = apollo;
            if (!(key && graphRef)) {
                throw new Error("You've enabled usage reporting via ApolloServerPluginUsageReporting, " + 'but you also need to provide your Apollo API key and graph ref, via ' + 'the APOLLO_KEY/APOLLO_GRAPH_REF environment ' + 'variables or via `new ApolloServer({apollo: {key, graphRef})`.');
            }
            if ((0, schemaIsSubgraph_js_1.schemaIsSubgraph)(schema)) {
                if (options.__onlyIfSchemaIsNotSubgraph) {
                    logger.warn('You have specified an Apollo API key and graph ref but this server appears ' + 'to be a subgraph. Typically usage reports are sent to Apollo by your Router ' + 'or Gateway, not directly from your subgraph; usage reporting is disabled. To ' + 'enable usage reporting anyway, explicitly install `ApolloServerPluginUsageReporting`. ' + 'To disable this warning, install `ApolloServerPluginUsageReportingDisabled`.');
                    return {};
                } else {
                    logger.warn('You have installed `ApolloServerPluginUsageReporting` but this server appears to ' + 'be a subgraph. Typically usage reports are sent to Apollo by your Router ' + 'or Gateway, not directly from your subgraph. If this was unintentional, remove ' + "`ApolloServerPluginUsageReporting` from your server's `plugins` array.");
                }
            }
            logger.info('Apollo usage reporting starting! See your graph at ' + `https://studio.apollographql.com/graph/${encodeURI(graphRef)}/`);
            const sendReportsImmediately = options.sendReportsImmediately ?? startedInBackground;
            let operationDerivedDataCache = null;
            const reportByExecutableSchemaId = new Map();
            const getReportWhichMustBeUsedImmediately = (executableSchemaId)=>{
                const existing = reportByExecutableSchemaId.get(executableSchemaId);
                if (existing) {
                    return existing;
                }
                const report = new stats_js_1.OurReport(new usage_reporting_protobuf_1.ReportHeader({
                    ...reportHeaderDefaults,
                    executableSchemaId,
                    graphRef
                }));
                reportByExecutableSchemaId.set(executableSchemaId, report);
                return report;
            };
            const getAndDeleteReport = (executableSchemaId)=>{
                const report = reportByExecutableSchemaId.get(executableSchemaId);
                if (report) {
                    reportByExecutableSchemaId.delete(executableSchemaId);
                    return report;
                }
                return null;
            };
            const overriddenExecutableSchemaId = options.overrideReportedSchema ? (0, computeCoreSchemaHash_js_1.computeCoreSchemaHash)(options.overrideReportedSchema) : undefined;
            let lastSeenExecutableSchemaToId;
            let reportTimer;
            if (!sendReportsImmediately) {
                reportTimer = setInterval(()=>sendAllReportsAndReportErrors(), options.reportIntervalMs || 10 * 1000);
            }
            let sendTraces = options.sendTraces ?? true;
            const sendOperationAsTrace = options.experimental_sendOperationAsTrace ?? (0, defaultSendOperationsAsTrace_js_1.defaultSendOperationsAsTrace)();
            let stopped = false;
            function executableSchemaIdForSchema(schema) {
                if (lastSeenExecutableSchemaToId?.executableSchema === schema) {
                    return lastSeenExecutableSchemaToId.executableSchemaId;
                }
                const id = (0, computeCoreSchemaHash_js_1.computeCoreSchemaHash)((0, graphql_1.printSchema)(schema));
                lastSeenExecutableSchemaToId = {
                    executableSchema: schema,
                    executableSchemaId: id
                };
                return id;
            }
            async function sendAllReportsAndReportErrors() {
                await Promise.all([
                    ...reportByExecutableSchemaId.keys()
                ].map((executableSchemaId)=>sendReportAndReportErrors(executableSchemaId)));
            }
            async function sendReportAndReportErrors(executableSchemaId) {
                return sendReport(executableSchemaId).catch((err)=>{
                    if (options.reportErrorFunction) {
                        options.reportErrorFunction(err);
                    } else {
                        logger.error(err.message);
                    }
                });
            }
            const sendReport = async (executableSchemaId)=>{
                let report = getAndDeleteReport(executableSchemaId);
                if (!report || Object.keys(report.tracesPerQuery).length === 0 && report.operationCount === 0) {
                    return;
                }
                report.endTime = (0, traceTreeBuilder_js_1.dateToProtoTimestamp)(new Date());
                report.ensureCountsAreIntegers();
                const protobufError = usage_reporting_protobuf_1.Report.verify(report);
                if (protobufError) {
                    throw new Error(`Error verifying report: ${protobufError}`);
                }
                let message = usage_reporting_protobuf_1.Report.encode(report).finish();
                report = null;
                if (options.debugPrintReports) {
                    const decodedReport = usage_reporting_protobuf_1.Report.decode(message);
                    logger.info(`Apollo usage report: ${JSON.stringify(decodedReport.toJSON())}`);
                }
                const compressed = await new Promise((resolve, reject)=>{
                    (0, zlib_1.gzip)(message, (error, result)=>{
                        error ? reject(error) : resolve(result);
                    });
                });
                message = null;
                const fetcher = options.fetcher ?? node_fetch_1.default;
                const response = await (0, async_retry_1.default)(async ()=>{
                    const controller = new node_abort_controller_1.AbortController();
                    const abortTimeout = setTimeout(()=>{
                        controller.abort();
                    }, options.requestTimeoutMs ?? 30000);
                    let curResponse;
                    try {
                        curResponse = await fetcher((options.endpointUrl || 'https://usage-reporting.api.apollographql.com') + '/api/ingress/traces', {
                            method: 'POST',
                            headers: {
                                'user-agent': 'ApolloServerPluginUsageReporting',
                                'x-api-key': key,
                                'content-encoding': 'gzip',
                                accept: 'application/json'
                            },
                            body: compressed,
                            signal: controller.signal
                        });
                    } finally{
                        clearTimeout(abortTimeout);
                    }
                    if (curResponse.status >= 500 && curResponse.status < 600) {
                        throw new Error(`HTTP status ${curResponse.status}, ${await curResponse.text() || '(no body)'}`);
                    } else {
                        return curResponse;
                    }
                }, {
                    retries: (options.maxAttempts || 5) - 1,
                    minTimeout: options.minimumRetryDelayMs || 100,
                    factor: 2
                }).catch((err)=>{
                    throw new Error(`Error sending report to Apollo servers: ${err.message}`);
                });
                if (response.status < 200 || response.status >= 300) {
                    throw new Error(`Error sending report to Apollo servers: HTTP status ${response.status}, ${await response.text() || '(no body)'}`);
                }
                if (sendTraces && response.status === 200 && response.headers.get('content-type')?.match(/^\s*application\/json\s*(?:;|$)/i)) {
                    const body = await response.text();
                    let parsedBody;
                    try {
                        parsedBody = JSON.parse(body);
                    } catch (e) {
                        throw new Error(`Error parsing response from Apollo servers: ${e}`);
                    }
                    if (parsedBody.tracesIgnored === true) {
                        logger.debug("This graph's organization does not have access to traces; sending all " + 'subsequent operations as stats.');
                        sendTraces = false;
                    }
                }
                if (options.debugPrintReports) {
                    logger.info(`Apollo usage report: status ${response.status}`);
                }
            };
            requestDidStartHandler = ({ metrics, schema, request: { http, variables } })=>{
                const treeBuilder = new traceTreeBuilder_js_1.TraceTreeBuilder({
                    maskedBy: 'ApolloServerPluginUsageReporting',
                    sendErrors: options.sendErrors
                });
                treeBuilder.startTiming();
                metrics.startHrTime = treeBuilder.startHrTime;
                let graphqlValidationFailure = false;
                let graphqlUnknownOperationName = false;
                let includeOperationInUsageReporting = null;
                if (http) {
                    treeBuilder.trace.http = new usage_reporting_protobuf_1.Trace.HTTP({
                        method: usage_reporting_protobuf_1.Trace.HTTP.Method[http.method] || usage_reporting_protobuf_1.Trace.HTTP.Method.UNKNOWN
                    });
                    if (options.sendHeaders) {
                        makeHTTPRequestHeaders(treeBuilder.trace.http, http.headers, options.sendHeaders);
                    }
                }
                async function maybeCallIncludeRequestHook(requestContext) {
                    if (includeOperationInUsageReporting !== null) return;
                    if (typeof options.includeRequest !== 'function') {
                        includeOperationInUsageReporting = true;
                        return;
                    }
                    includeOperationInUsageReporting = await options.includeRequest(requestContext);
                    if (typeof includeOperationInUsageReporting !== 'boolean') {
                        logger.warn("The 'includeRequest' async predicate function must return a boolean value.");
                        includeOperationInUsageReporting = true;
                    }
                }
                let didResolveSource = false;
                return {
                    async didResolveSource (requestContext) {
                        didResolveSource = true;
                        if (metrics.persistedQueryHit) {
                            treeBuilder.trace.persistedQueryHit = true;
                        }
                        if (metrics.persistedQueryRegister) {
                            treeBuilder.trace.persistedQueryRegister = true;
                        }
                        if (variables) {
                            treeBuilder.trace.details = (0, traceDetails_js_1.makeTraceDetails)(variables, options.sendVariableValues, requestContext.source);
                        }
                        const clientInfo = (options.generateClientInfo || defaultGenerateClientInfo)(requestContext);
                        if (clientInfo) {
                            const { clientName, clientVersion } = clientInfo;
                            treeBuilder.trace.clientVersion = clientVersion || '';
                            treeBuilder.trace.clientName = clientName || '';
                        }
                    },
                    async validationDidStart () {
                        return async (validationErrors)=>{
                            graphqlValidationFailure = validationErrors ? validationErrors.length !== 0 : false;
                        };
                    },
                    async didResolveOperation (requestContext) {
                        graphqlUnknownOperationName = requestContext.operation === undefined;
                        await maybeCallIncludeRequestHook(requestContext);
                        if (includeOperationInUsageReporting && !graphqlUnknownOperationName) {
                            if (metrics.captureTraces === undefined) {
                                const rawWeight = await fieldLevelInstrumentation(requestContext);
                                treeBuilder.trace.fieldExecutionWeight = typeof rawWeight === 'number' ? rawWeight : rawWeight ? 1 : 0;
                                metrics.captureTraces = !!treeBuilder.trace.fieldExecutionWeight;
                            }
                        }
                    },
                    async executionDidStart () {
                        if (!metrics.captureTraces) return;
                        return {
                            willResolveField ({ info }) {
                                return treeBuilder.willResolveField(info);
                            }
                        };
                    },
                    async didEncounterSubsequentErrors (_requestContext, errors) {
                        treeBuilder.didEncounterErrors(errors);
                    },
                    async willSendSubsequentPayload (requestContext, payload) {
                        if (!payload.hasNext) {
                            await operationFinished(requestContext);
                        }
                    },
                    async willSendResponse (requestContext) {
                        if (!didResolveSource) return;
                        if (requestContext.errors) {
                            treeBuilder.didEncounterErrors(requestContext.errors);
                        }
                        if (requestContext.response.body.kind === 'single') {
                            await operationFinished(requestContext);
                        }
                    }
                };
                "TURBOPACK unreachable";
                async function operationFinished(requestContext) {
                    const resolvedOperation = !!requestContext.operation;
                    await maybeCallIncludeRequestHook(requestContext);
                    treeBuilder.stopTiming();
                    const executableSchemaId = overriddenExecutableSchemaId ?? executableSchemaIdForSchema(schema);
                    if (includeOperationInUsageReporting === false) {
                        if (resolvedOperation) {
                            getReportWhichMustBeUsedImmediately(executableSchemaId).operationCount++;
                        }
                        return;
                    }
                    treeBuilder.trace.fullQueryCacheHit = !!metrics.responseCacheHit;
                    treeBuilder.trace.forbiddenOperation = !!metrics.forbiddenOperation;
                    treeBuilder.trace.registeredOperation = !!metrics.registeredOperation;
                    const policyIfCacheable = requestContext.overallCachePolicy.policyIfCacheable();
                    if (policyIfCacheable) {
                        treeBuilder.trace.cachePolicy = new usage_reporting_protobuf_1.Trace.CachePolicy({
                            scope: policyIfCacheable.scope === 'PRIVATE' ? usage_reporting_protobuf_1.Trace.CachePolicy.Scope.PRIVATE : policyIfCacheable.scope === 'PUBLIC' ? usage_reporting_protobuf_1.Trace.CachePolicy.Scope.PUBLIC : usage_reporting_protobuf_1.Trace.CachePolicy.Scope.UNKNOWN,
                            maxAgeNs: policyIfCacheable.maxAge * 1e9
                        });
                    }
                    if (metrics.queryPlanTrace) {
                        treeBuilder.trace.queryPlan = metrics.queryPlanTrace;
                    }
                    addTrace().catch(logger.error);
                    async function addTrace() {
                        if (stopped) {
                            return;
                        }
                        await new Promise((res)=>setImmediate(res));
                        const executableSchemaId = overriddenExecutableSchemaId ?? executableSchemaIdForSchema(schema);
                        const { trace } = treeBuilder;
                        let statsReportKey = undefined;
                        let referencedFieldsByType;
                        if (!requestContext.document) {
                            statsReportKey = `## GraphQLParseFailure\n`;
                        } else if (graphqlValidationFailure) {
                            statsReportKey = `## GraphQLValidationFailure\n`;
                        } else if (graphqlUnknownOperationName) {
                            statsReportKey = `## GraphQLUnknownOperationName\n`;
                        }
                        const isExecutable = statsReportKey === undefined;
                        if (statsReportKey) {
                            if (options.sendUnexecutableOperationDocuments) {
                                trace.unexecutedOperationBody = requestContext.source;
                                trace.unexecutedOperationName = requestContext.request.operationName || '';
                            }
                            referencedFieldsByType = Object.create(null);
                        } else {
                            const operationDerivedData = getOperationDerivedData();
                            statsReportKey = `# ${requestContext.operationName || '-'}\n${operationDerivedData.signature}`;
                            referencedFieldsByType = operationDerivedData.referencedFieldsByType;
                        }
                        const protobufError = usage_reporting_protobuf_1.Trace.verify(trace);
                        if (protobufError) {
                            throw new Error(`Error encoding trace: ${protobufError}`);
                        }
                        if (resolvedOperation) {
                            getReportWhichMustBeUsedImmediately(executableSchemaId).operationCount++;
                        }
                        getReportWhichMustBeUsedImmediately(executableSchemaId).addTrace({
                            statsReportKey,
                            trace,
                            asTrace: sendTraces && (!isExecutable || !!metrics.captureTraces) && !metrics.nonFtv1ErrorPaths?.length && sendOperationAsTrace(trace, statsReportKey),
                            referencedFieldsByType,
                            nonFtv1ErrorPaths: metrics.nonFtv1ErrorPaths ?? []
                        });
                        if (sendReportsImmediately || getReportWhichMustBeUsedImmediately(executableSchemaId).sizeEstimator.bytes >= (options.maxUncompressedReportSize || 4 * 1024 * 1024)) {
                            await sendReportAndReportErrors(executableSchemaId);
                        }
                    }
                    function getOperationDerivedData() {
                        if (!requestContext.document) {
                            throw new Error('No document?');
                        }
                        const cacheKey = (0, operationDerivedDataCache_js_1.operationDerivedDataCacheKey)(requestContext.queryHash, requestContext.operationName || '');
                        if (!operationDerivedDataCache || operationDerivedDataCache.forSchema !== schema) {
                            operationDerivedDataCache = {
                                forSchema: schema,
                                cache: (0, operationDerivedDataCache_js_1.createOperationDerivedDataCache)({
                                    logger
                                })
                            };
                        }
                        const cachedOperationDerivedData = operationDerivedDataCache.cache.get(cacheKey);
                        if (cachedOperationDerivedData) {
                            return cachedOperationDerivedData;
                        }
                        const generatedSignature = (options.calculateSignature || utils_usagereporting_1.usageReportingSignature)(requestContext.document, requestContext.operationName || '');
                        const generatedOperationDerivedData = {
                            signature: generatedSignature,
                            referencedFieldsByType: (0, utils_usagereporting_1.calculateReferencedFieldsByType)({
                                document: requestContext.document,
                                schema,
                                resolvedOperationName: requestContext.operationName ?? null
                            })
                        };
                        operationDerivedDataCache.cache.set(cacheKey, generatedOperationDerivedData);
                        return generatedOperationDerivedData;
                    }
                }
            };
            return {
                async serverWillStop () {
                    if (reportTimer) {
                        clearInterval(reportTimer);
                        reportTimer = undefined;
                    }
                    stopped = true;
                    await sendAllReportsAndReportErrors();
                }
            };
        }
    });
}
exports.ApolloServerPluginUsageReporting = ApolloServerPluginUsageReporting;
function makeHTTPRequestHeaders(http, headers, sendHeaders) {
    if (!sendHeaders || 'none' in sendHeaders && sendHeaders.none || 'all' in sendHeaders && !sendHeaders.all) {
        return;
    }
    for (const [key, value] of headers){
        if ('exceptNames' in sendHeaders && sendHeaders.exceptNames.some((exceptHeader)=>{
            return exceptHeader.toLowerCase() === key;
        }) || 'onlyNames' in sendHeaders && !sendHeaders.onlyNames.some((header)=>{
            return header.toLowerCase() === key;
        })) {
            continue;
        }
        switch(key){
            case 'authorization':
            case 'cookie':
            case 'set-cookie':
                break;
            default:
                http.requestHeaders[key] = new usage_reporting_protobuf_1.Trace.HTTP.Values({
                    value: [
                        value
                    ]
                });
        }
    }
}
exports.makeHTTPRequestHeaders = makeHTTPRequestHeaders;
function defaultGenerateClientInfo({ request }) {
    const clientNameHeaderKey = 'apollographql-client-name';
    const clientVersionHeaderKey = 'apollographql-client-version';
    if (request.http?.headers?.get(clientNameHeaderKey) || request.http?.headers?.get(clientVersionHeaderKey)) {
        return {
            clientName: request.http?.headers?.get(clientNameHeaderKey),
            clientVersion: request.http?.headers?.get(clientVersionHeaderKey)
        };
    } else if (request.extensions?.clientInfo) {
        return request.extensions.clientInfo;
    } else {
        return {};
    }
} //# sourceMappingURL=plugin.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApolloServerPluginUsageReporting = void 0;
var plugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/plugin.js [app-route] (ecmascript)");
Object.defineProperty(exports, "ApolloServerPluginUsageReporting", {
    enumerable: true,
    get: function() {
        return plugin_js_1.ApolloServerPluginUsageReporting;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaReporting/schemaReporter.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.SchemaReporter = exports.schemaReportGql = void 0;
const node_fetch_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/node-fetch/lib/index.mjs [app-route] (ecmascript)"));
const packageVersion_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/generated/packageVersion.js [app-route] (ecmascript)");
exports.schemaReportGql = `#graphql
  mutation SchemaReport($report: SchemaReport!, $coreSchema: String) {
    reportSchema(report: $report, coreSchema: $coreSchema) {
      __typename
      ... on ReportSchemaError {
        message
        code
      }
      ... on ReportSchemaResponse {
        inSeconds
        withCoreSchema
      }
    }
  }
`;
class SchemaReporter {
    constructor(options){
        this.headers = {
            'Content-Type': 'application/json',
            'x-api-key': options.apiKey,
            'apollographql-client-name': 'ApolloServerPluginSchemaReporting',
            'apollographql-client-version': packageVersion_js_1.packageVersion
        };
        this.endpointUrl = options.endpointUrl || 'https://schema-reporting.api.apollographql.com/api/graphql';
        this.schemaReport = options.schemaReport;
        this.coreSchema = options.coreSchema;
        this.isStopped = false;
        this.logger = options.logger;
        this.initialReportingDelayInMs = options.initialReportingDelayInMs;
        this.fallbackReportingDelayInMs = options.fallbackReportingDelayInMs;
        this.fetcher = options.fetcher ?? node_fetch_1.default;
    }
    stopped() {
        return this.isStopped;
    }
    start() {
        this.pollTimer = setTimeout(()=>this.sendOneReportAndScheduleNext(false), this.initialReportingDelayInMs);
    }
    stop() {
        this.isStopped = true;
        if (this.pollTimer) {
            clearTimeout(this.pollTimer);
            this.pollTimer = undefined;
        }
    }
    async sendOneReportAndScheduleNext(sendNextWithCoreSchema) {
        this.pollTimer = undefined;
        if (this.stopped()) return;
        try {
            const result = await this.reportSchema(sendNextWithCoreSchema);
            if (!result) {
                return;
            }
            if (!this.stopped()) {
                this.pollTimer = setTimeout(()=>this.sendOneReportAndScheduleNext(result.withCoreSchema), result.inSeconds * 1000);
            }
            return;
        } catch (error) {
            this.logger.error(`Error reporting server info to Apollo during schema reporting: ${error}`);
            if (!this.stopped()) {
                this.pollTimer = setTimeout(()=>this.sendOneReportAndScheduleNext(false), this.fallbackReportingDelayInMs);
            }
        }
    }
    async reportSchema(withCoreSchema) {
        const { data, errors } = await this.apolloQuery({
            report: this.schemaReport,
            coreSchema: withCoreSchema ? this.coreSchema : null
        });
        if (errors) {
            throw new Error(errors.map((x)=>x.message).join('\n'));
        }
        function msgForUnexpectedResponse(data) {
            return [
                'Unexpected response shape from Apollo when',
                'reporting schema. If this continues, please reach',
                '<NAME_EMAIL>.',
                'Received response:',
                JSON.stringify(data)
            ].join(' ');
        }
        if (!data || !data.reportSchema) {
            throw new Error(msgForUnexpectedResponse(data));
        }
        if (data.reportSchema.__typename === 'ReportSchemaResponse') {
            return data.reportSchema;
        } else if (data.reportSchema.__typename === 'ReportSchemaError') {
            this.logger.error([
                'Received input validation error from Apollo:',
                data.reportSchema.message,
                'Stopping reporting. Please fix the input errors.'
            ].join(' '));
            this.stop();
            return null;
        }
        throw new Error(msgForUnexpectedResponse(data));
    }
    async apolloQuery(variables) {
        const request = {
            query: exports.schemaReportGql,
            variables
        };
        const httpResponse = await this.fetcher(this.endpointUrl, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(request)
        });
        if (!httpResponse.ok) {
            throw new Error([
                `An unexpected HTTP status code (${httpResponse.status}) was`,
                'encountered during schema reporting.'
            ].join(' '));
        }
        try {
            return await httpResponse.json();
        } catch (error) {
            throw new Error([
                "Couldn't report schema to Apollo.",
                'Parsing response as JSON failed.',
                'If this continues please reach <NAME_EMAIL>',
                error
            ].join(' '));
        }
    }
}
exports.SchemaReporter = SchemaReporter; //# sourceMappingURL=schemaReporter.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaReporting/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApolloServerPluginSchemaReporting = void 0;
const os_1 = __importDefault(__turbopack_context__.r("[externals]/os [external] (os, cjs)"));
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
const uuid_1 = __turbopack_context__.r("[project]/node_modules/uuid/dist/esm-node/index.js [app-route] (ecmascript)");
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const schemaReporter_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaReporting/schemaReporter.js [app-route] (ecmascript)");
const schemaIsSubgraph_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaIsSubgraph.js [app-route] (ecmascript)");
const packageVersion_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/generated/packageVersion.js [app-route] (ecmascript)");
const computeCoreSchemaHash_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/computeCoreSchemaHash.js [app-route] (ecmascript)");
function ApolloServerPluginSchemaReporting({ initialDelayMaxMs, overrideReportedSchema, endpointUrl, fetcher } = Object.create(null)) {
    const bootId = (0, uuid_1.v4)();
    return (0, internalPlugin_js_1.internalPlugin)({
        __internal_plugin_id__: 'SchemaReporting',
        __is_disabled_plugin__: false,
        async serverWillStart ({ apollo, schema, logger }) {
            const { key, graphRef } = apollo;
            if (!key) {
                throw Error('To use ApolloServerPluginSchemaReporting, you must provide an Apollo API ' + 'key, via the APOLLO_KEY environment variable or via `new ApolloServer({apollo: {key})`');
            }
            if (!graphRef) {
                throw Error('To use ApolloServerPluginSchemaReporting, you must provide your graph ref (eg, ' + "'my-graph-id@my-graph-variant'). Try setting the APOLLO_GRAPH_REF environment " + 'variable or passing `new ApolloServer({apollo: {graphRef}})`.');
            }
            if (overrideReportedSchema) {
                try {
                    const validationErrors = (0, graphql_1.validateSchema)((0, graphql_1.buildSchema)(overrideReportedSchema, {
                        noLocation: true
                    }));
                    if (validationErrors.length) {
                        throw new Error(validationErrors.map((error)=>error.message).join('\n'));
                    }
                } catch (err) {
                    throw new Error('The schema provided to overrideReportedSchema failed to parse or ' + `validate: ${err.message}`);
                }
            }
            if ((0, schemaIsSubgraph_js_1.schemaIsSubgraph)(schema)) {
                throw Error([
                    'Schema reporting is not yet compatible with Apollo Federation subgraphs.',
                    "If you're interested in using schema reporting with subgraphs,",
                    'please contact Apollo support. To set up managed federation, see',
                    'https://go.apollo.dev/s/managed-federation'
                ].join(' '));
            }
            if (endpointUrl !== undefined) {
                logger.info(`Apollo schema reporting: schema reporting URL override: ${endpointUrl}`);
            }
            const baseSchemaReport = {
                bootId,
                graphRef,
                platform: process.env.APOLLO_SERVER_PLATFORM || 'local',
                runtimeVersion: `node ${process.version}`,
                userVersion: process.env.APOLLO_SERVER_USER_VERSION,
                serverId: process.env.APOLLO_SERVER_ID || process.env.HOSTNAME || os_1.default.hostname(),
                libraryVersion: `@apollo/server@${packageVersion_js_1.packageVersion}`
            };
            let currentSchemaReporter;
            return {
                schemaDidLoadOrUpdate ({ apiSchema, coreSupergraphSdl }) {
                    if (overrideReportedSchema !== undefined) {
                        if (currentSchemaReporter) {
                            return;
                        } else {
                            logger.info('Apollo schema reporting: schema to report has been overridden');
                        }
                    }
                    const coreSchema = overrideReportedSchema ?? coreSupergraphSdl ?? (0, graphql_1.printSchema)(apiSchema);
                    const coreSchemaHash = (0, computeCoreSchemaHash_js_1.computeCoreSchemaHash)(coreSchema);
                    const schemaReport = {
                        ...baseSchemaReport,
                        coreSchemaHash
                    };
                    currentSchemaReporter?.stop();
                    currentSchemaReporter = new schemaReporter_js_1.SchemaReporter({
                        schemaReport,
                        coreSchema,
                        apiKey: key,
                        endpointUrl,
                        logger,
                        initialReportingDelayInMs: Math.floor(Math.random() * (initialDelayMaxMs ?? 10000)),
                        fallbackReportingDelayInMs: 20000,
                        fetcher
                    });
                    currentSchemaReporter.start();
                    logger.info('Apollo schema reporting: reporting a new schema to Studio! See your graph at ' + `https://studio.apollographql.com/graph/${encodeURI(graphRef)}/ with server info ${JSON.stringify(schemaReport)}`);
                },
                async serverWillStop () {
                    currentSchemaReporter?.stop();
                }
            };
        }
    });
}
exports.ApolloServerPluginSchemaReporting = ApolloServerPluginSchemaReporting; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/inlineTrace/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApolloServerPluginInlineTrace = void 0;
const usage_reporting_protobuf_1 = __turbopack_context__.r("[project]/node_modules/@apollo/usage-reporting-protobuf/generated/cjs/protobuf.js [app-route] (ecmascript)");
const traceTreeBuilder_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/traceTreeBuilder.js [app-route] (ecmascript)");
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
const schemaIsSubgraph_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaIsSubgraph.js [app-route] (ecmascript)");
function ApolloServerPluginInlineTrace(options = Object.create(null)) {
    let enabled = options.__onlyIfSchemaIsSubgraph ? null : true;
    return (0, internalPlugin_js_1.internalPlugin)({
        __internal_plugin_id__: 'InlineTrace',
        __is_disabled_plugin__: false,
        async serverWillStart ({ schema, logger }) {
            if (enabled === null) {
                enabled = (0, schemaIsSubgraph_js_1.schemaIsSubgraph)(schema);
                if (enabled) {
                    logger.info('Enabling inline tracing for this subgraph. To disable, use ' + 'ApolloServerPluginInlineTraceDisabled.');
                }
            }
        },
        async requestDidStart ({ request: { http }, metrics }) {
            if (!enabled) {
                return;
            }
            const treeBuilder = new traceTreeBuilder_js_1.TraceTreeBuilder({
                maskedBy: 'ApolloServerPluginInlineTrace',
                sendErrors: options.includeErrors
            });
            if (http?.headers.get('apollo-federation-include-trace') !== 'ftv1') {
                return;
            }
            if (metrics.captureTraces === false) {
                return;
            }
            metrics.captureTraces = true;
            treeBuilder.startTiming();
            return {
                async executionDidStart () {
                    return {
                        willResolveField ({ info }) {
                            return treeBuilder.willResolveField(info);
                        }
                    };
                },
                async didEncounterErrors ({ errors }) {
                    treeBuilder.didEncounterErrors(errors);
                },
                async willSendResponse ({ response }) {
                    treeBuilder.stopTiming();
                    if (response.body.kind === 'incremental') {
                        return;
                    }
                    if (metrics.queryPlanTrace) {
                        treeBuilder.trace.queryPlan = metrics.queryPlanTrace;
                    }
                    const encodedUint8Array = usage_reporting_protobuf_1.Trace.encode(treeBuilder.trace).finish();
                    const encodedBuffer = Buffer.from(encodedUint8Array, encodedUint8Array.byteOffset, encodedUint8Array.byteLength);
                    const extensions = response.body.singleResult.extensions || (response.body.singleResult.extensions = Object.create(null));
                    if (typeof extensions.ftv1 !== 'undefined') {
                        throw new Error('The `ftv1` extension was already present.');
                    }
                    extensions.ftv1 = encodedBuffer.toString('base64');
                }
            };
        }
    });
}
exports.ApolloServerPluginInlineTrace = ApolloServerPluginInlineTrace; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getEmbeddedSandboxHTML = exports.getEmbeddedExplorerHTML = void 0;
function getConfigStringForHtml(config) {
    return JSON.stringify(config).replace('<', '\\u003c').replace('>', '\\u003e').replace('&', '\\u0026').replace("'", '\\u0027');
}
const getEmbeddedExplorerHTML = (explorerCdnVersion, config, apolloServerVersion, nonce)=>{
    const productionLandingPageEmbedConfigOrDefault = {
        displayOptions: {},
        persistExplorerState: false,
        runTelemetry: true,
        ...typeof config.embed === 'boolean' ? {} : config.embed
    };
    const embeddedExplorerParams = {
        graphRef: config.graphRef,
        target: '#embeddableExplorer',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                headers: config.headers,
                variables: config.variables
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            displayOptions: {
                ...productionLandingPageEmbedConfigOrDefault.displayOptions
            }
        },
        persistExplorerState: productionLandingPageEmbedConfigOrDefault.persistExplorerState,
        includeCookies: config.includeCookies,
        runtime: apolloServerVersion,
        runTelemetry: productionLandingPageEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Explorer cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableExplorer {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableExplorer"></div>
<script nonce="${nonce}" src="https://embeddable-explorer.cdn.apollographql.com/${encodeURIComponent(explorerCdnVersion)}/embeddable-explorer.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var endpointUrl = window.location.href;
  var embeddedExplorerConfig = ${getConfigStringForHtml(embeddedExplorerParams)};
  new window.EmbeddedExplorer({
    ...embeddedExplorerConfig,
    endpointUrl,
  });
</script>
`;
};
exports.getEmbeddedExplorerHTML = getEmbeddedExplorerHTML;
const getEmbeddedSandboxHTML = (sandboxCdnVersion, config, apolloServerVersion, nonce)=>{
    const localDevelopmentEmbedConfigOrDefault = {
        runTelemetry: true,
        endpointIsEditable: false,
        initialState: {},
        ...typeof config.embed === 'boolean' ? {} : config.embed ?? {}
    };
    const embeddedSandboxConfig = {
        target: '#embeddableSandbox',
        initialState: {
            ...'document' in config || 'headers' in config || 'variables' in config ? {
                document: config.document,
                variables: config.variables,
                headers: config.headers
            } : {},
            ...'collectionId' in config ? {
                collectionId: config.collectionId,
                operationId: config.operationId
            } : {},
            includeCookies: config.includeCookies,
            ...localDevelopmentEmbedConfigOrDefault.initialState
        },
        hideCookieToggle: false,
        endpointIsEditable: localDevelopmentEmbedConfigOrDefault.endpointIsEditable,
        runtime: apolloServerVersion,
        runTelemetry: localDevelopmentEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Sandbox cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableSandbox {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableSandbox"></div>
<script nonce="${nonce}" src="https://embeddable-sandbox.cdn.apollographql.com/${encodeURIComponent(sandboxCdnVersion)}/embeddable-sandbox.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var initialEndpoint = window.location.href;
  var embeddedSandboxConfig = ${getConfigStringForHtml(embeddedSandboxConfig)};
  new window.EmbeddedSandbox(
    {
      ...embeddedSandboxConfig,
      initialEndpoint,
    }
  );
</script>
`;
};
exports.getEmbeddedSandboxHTML = getEmbeddedSandboxHTML; //# sourceMappingURL=getEmbeddedHTML.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/landingPage/default/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION = exports.DEFAULT_EMBEDDED_SANDBOX_VERSION = exports.DEFAULT_EMBEDDED_EXPLORER_VERSION = exports.ApolloServerPluginLandingPageProductionDefault = exports.ApolloServerPluginLandingPageLocalDefault = void 0;
const getEmbeddedHTML_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/landingPage/default/getEmbeddedHTML.js [app-route] (ecmascript)");
const packageVersion_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/generated/packageVersion.js [app-route] (ecmascript)");
const utils_createhash_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.createhash/dist/index.js [app-route] (ecmascript)");
const uuid_1 = __turbopack_context__.r("[project]/node_modules/uuid/dist/esm-node/index.js [app-route] (ecmascript)");
function ApolloServerPluginLandingPageLocalDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = {
        embed: true,
        ...options
    };
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: false,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
exports.ApolloServerPluginLandingPageLocalDefault = ApolloServerPluginLandingPageLocalDefault;
function ApolloServerPluginLandingPageProductionDefault(options = {}) {
    const { version, __internal_apolloStudioEnv__, ...rest } = options;
    return ApolloServerPluginLandingPageDefault(version, {
        isProd: true,
        apolloStudioEnv: __internal_apolloStudioEnv__,
        ...rest
    });
}
exports.ApolloServerPluginLandingPageProductionDefault = ApolloServerPluginLandingPageProductionDefault;
function encodeConfig(config) {
    return JSON.stringify(encodeURIComponent(JSON.stringify(config)));
}
const getNonEmbeddedLandingPageHTML = (cdnVersion, config, apolloServerVersion, nonce)=>{
    const encodedConfig = encodeConfig(config);
    return `
 <div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>The full landing page cannot be loaded; it appears that you might be offline.</p>
</div>
<script nonce="${nonce}">window.landingPage = ${encodedConfig};</script>
<script nonce="${nonce}" src="https://apollo-server-landing-page.cdn.apollographql.com/${encodeURIComponent(cdnVersion)}/static/js/main.js?runtime=${apolloServerVersion}"></script>`;
};
exports.DEFAULT_EMBEDDED_EXPLORER_VERSION = 'v3';
exports.DEFAULT_EMBEDDED_SANDBOX_VERSION = 'v2';
exports.DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION = '_latest';
function ApolloServerPluginLandingPageDefault(maybeVersion, config) {
    const explorerVersion = maybeVersion ?? exports.DEFAULT_EMBEDDED_EXPLORER_VERSION;
    const sandboxVersion = maybeVersion ?? exports.DEFAULT_EMBEDDED_SANDBOX_VERSION;
    const apolloServerLandingPageVersion = maybeVersion ?? exports.DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION;
    const apolloServerVersion = `@apollo/server@${packageVersion_js_1.packageVersion}`;
    const scriptSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com'
    ].join(' ');
    const styleSafeList = [
        'https://apollo-server-landing-page.cdn.apollographql.com',
        'https://embeddable-sandbox.cdn.apollographql.com',
        'https://embeddable-explorer.cdn.apollographql.com',
        'https://fonts.googleapis.com'
    ].join(' ');
    const iframeSafeList = [
        'https://explorer.embed.apollographql.com',
        'https://sandbox.embed.apollographql.com',
        'https://embed.apollo.local:3000'
    ].join(' ');
    return {
        __internal_installed_implicitly__: false,
        async serverWillStart (server) {
            if (config.precomputedNonce) {
                server.logger.warn("The `precomputedNonce` landing page configuration option is deprecated. Removing this option is strictly an improvement to Apollo Server's landing page Content Security Policy (CSP) implementation for preventing XSS attacks.");
            }
            return {
                async renderLandingPage () {
                    const encodedASLandingPageVersion = encodeURIComponent(apolloServerLandingPageVersion);
                    async function html() {
                        const nonce = config.precomputedNonce ?? (0, utils_createhash_1.createHash)('sha256').update((0, uuid_1.v4)()).digest('hex');
                        const scriptCsp = `script-src 'self' 'nonce-${nonce}' ${scriptSafeList}`;
                        const styleCsp = `style-src 'nonce-${nonce}' ${styleSafeList}`;
                        const imageCsp = `img-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const manifestCsp = `manifest-src https://apollo-server-landing-page.cdn.apollographql.com`;
                        const frameCsp = `frame-src ${iframeSafeList}`;
                        return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Security-Policy" content="${scriptCsp}; ${styleCsp}; ${imageCsp}; ${manifestCsp}; ${frameCsp}" />
    <link
      rel="icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link
      href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro&display=swap"
      rel="stylesheet"
    />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Apollo server landing page" />
    <link
      rel="apple-touch-icon"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/assets/favicon.png"
    />
    <link
      rel="manifest"
      href="https://apollo-server-landing-page.cdn.apollographql.com/${encodedASLandingPageVersion}/manifest.json"
    />
    <title>Apollo Server</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="react-root">
      <style nonce=${nonce}>
        body {
          margin: 0;
          overflow-x: hidden;
          overflow-y: hidden;
        }
        .fallback {
          opacity: 0;
          animation: fadeIn 1s 1s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
          padding: 1em;
        }
        @keyframes fadeIn {
          0% {opacity:0;}
          100% {opacity:1; }
        }
      </style>
    ${config.embed ? 'graphRef' in config && config.graphRef ? (0, getEmbeddedHTML_js_1.getEmbeddedExplorerHTML)(explorerVersion, config, apolloServerVersion, nonce) : !('graphRef' in config) ? (0, getEmbeddedHTML_js_1.getEmbeddedSandboxHTML)(sandboxVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce) : getNonEmbeddedLandingPageHTML(apolloServerLandingPageVersion, config, apolloServerVersion, nonce)}
    </div>
  </body>
</html>
          `;
                    }
                    return {
                        html
                    };
                }
            };
        }
    };
} //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/plugin/disableSuggestions/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ApolloServerPluginDisableSuggestions = void 0;
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
function ApolloServerPluginDisableSuggestions() {
    return (0, internalPlugin_js_1.internalPlugin)({
        __internal_plugin_id__: 'DisableSuggestions',
        __is_disabled_plugin__: false,
        async requestDidStart () {
            return {
                async validationDidStart () {
                    return async (validationErrors)=>{
                        validationErrors?.forEach((error)=>{
                            error.message = error.message.replace(/ ?Did you mean(.+?)\?$/, '');
                        });
                    };
                }
            };
        }
    });
}
exports.ApolloServerPluginDisableSuggestions = ApolloServerPluginDisableSuggestions; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/ApolloServer.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function(mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) {
        for(var k in mod)if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    }
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.chooseContentTypeForSingleResultResponse = exports.MEDIA_TYPES = exports.isImplicitlyInstallablePlugin = exports.internalExecuteOperation = exports.ApolloServer = void 0;
const utils_isnodelike_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.isnodelike/dist/index.js [app-route] (ecmascript)");
const utils_keyvaluecache_1 = __turbopack_context__.r("[project]/node_modules/@apollo/utils.keyvaluecache/dist/index.js [app-route] (ecmascript)");
const schema_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/schema/cjs/index.js [app-route] (ecmascript)");
const resolvable_js_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/resolvable.js [app-route] (ecmascript)"));
const graphql_1 = __turbopack_context__.r("[project]/node_modules/graphql/index.mjs [app-route] (ecmascript)");
const loglevel_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/loglevel/lib/loglevel.js [app-route] (ecmascript)"));
const negotiator_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/negotiator/index.js [app-route] (ecmascript)"));
const cachePolicy_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/cachePolicy.js [app-route] (ecmascript)");
const determineApolloConfig_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/determineApolloConfig.js [app-route] (ecmascript)");
const errorNormalize_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errorNormalize.js [app-route] (ecmascript)");
const index_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/errors/index.js [app-route] (ecmascript)");
const httpBatching_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/httpBatching.js [app-route] (ecmascript)");
const internalPlugin_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/internalPlugin.js [app-route] (ecmascript)");
const preventCsrf_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/preventCsrf.js [app-route] (ecmascript)");
const requestPipeline_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/requestPipeline.js [app-route] (ecmascript)");
const runHttpQuery_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/runHttpQuery.js [app-route] (ecmascript)");
const HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
const UnreachableCaseError_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/UnreachableCaseError.js [app-route] (ecmascript)");
const computeCoreSchemaHash_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/computeCoreSchemaHash.js [app-route] (ecmascript)");
const isDefined_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/isDefined.js [app-route] (ecmascript)");
const schemaManager_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/schemaManager.js [app-route] (ecmascript)");
const index_js_2 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/validationRules/index.js [app-route] (ecmascript)");
function defaultLogger() {
    const loglevelLogger = loglevel_1.default.getLogger('apollo-server');
    loglevelLogger.setLevel(loglevel_1.default.levels.INFO);
    return loglevelLogger;
}
class ApolloServer {
    constructor(config){
        const nodeEnv = config.nodeEnv ?? ("TURBOPACK compile-time value", "development") ?? '';
        this.logger = config.logger ?? defaultLogger();
        const apolloConfig = (0, determineApolloConfig_js_1.determineApolloConfig)(config.apollo, this.logger);
        const isDev = nodeEnv !== 'production';
        if (config.cache && config.cache !== 'bounded' && utils_keyvaluecache_1.PrefixingKeyValueCache.prefixesAreUnnecessaryForIsolation(config.cache)) {
            throw new Error('You cannot pass a cache returned from ' + '`PrefixingKeyValueCache.cacheDangerouslyDoesNotNeedPrefixesForIsolation`' + 'to `new ApolloServer({ cache })`, because Apollo Server may use it for ' + 'multiple features whose cache keys must be distinct from each other.');
        }
        const state = config.gateway ? {
            phase: 'initialized',
            schemaManager: new schemaManager_js_1.SchemaManager({
                gateway: config.gateway,
                apolloConfig,
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        } : {
            phase: 'initialized',
            schemaManager: new schemaManager_js_1.SchemaManager({
                apiSchema: ApolloServer.constructSchema(config),
                schemaDerivedDataProvider: (schema)=>ApolloServer.generateSchemaDerivedData(schema, config.documentStore),
                logger: this.logger
            })
        };
        const introspectionEnabled = config.introspection ?? isDev;
        const hideSchemaDetailsFromClientErrors = config.hideSchemaDetailsFromClientErrors ?? false;
        this.cache = config.cache === undefined || config.cache === 'bounded' ? new utils_keyvaluecache_1.InMemoryLRUCache() : config.cache;
        const maxRecursiveSelectionsRule = config.maxRecursiveSelections === true ? [
            (0, index_js_2.createMaxRecursiveSelectionsRule)(index_js_2.DEFAULT_MAX_RECURSIVE_SELECTIONS)
        ] : typeof config.maxRecursiveSelections === 'number' ? [
            (0, index_js_2.createMaxRecursiveSelectionsRule)(config.maxRecursiveSelections)
        ] : [];
        const validationRules = [
            ...introspectionEnabled ? [] : [
                index_js_2.NoIntrospection
            ],
            ...maxRecursiveSelectionsRule
        ];
        let laterValidationRules;
        if (maxRecursiveSelectionsRule.length > 0) {
            laterValidationRules = config.validationRules;
        } else {
            validationRules.push(...config.validationRules ?? []);
        }
        this.internals = {
            formatError: config.formatError,
            rootValue: config.rootValue,
            validationRules,
            laterValidationRules,
            hideSchemaDetailsFromClientErrors,
            dangerouslyDisableValidation: config.dangerouslyDisableValidation ?? false,
            fieldResolver: config.fieldResolver,
            includeStacktraceInErrorResponses: config.includeStacktraceInErrorResponses ?? (nodeEnv !== 'production' && nodeEnv !== 'test'),
            persistedQueries: config.persistedQueries === false ? undefined : {
                ...config.persistedQueries,
                cache: new utils_keyvaluecache_1.PrefixingKeyValueCache(config.persistedQueries?.cache ?? this.cache, requestPipeline_js_1.APQ_CACHE_PREFIX)
            },
            nodeEnv,
            allowBatchedHttpRequests: config.allowBatchedHttpRequests ?? false,
            apolloConfig,
            plugins: config.plugins ?? [],
            parseOptions: config.parseOptions ?? {},
            state,
            stopOnTerminationSignals: config.stopOnTerminationSignals,
            gatewayExecutor: null,
            csrfPreventionRequestHeaders: config.csrfPrevention === true || config.csrfPrevention === undefined ? preventCsrf_js_1.recommendedCsrfPreventionRequestHeaders : config.csrfPrevention === false ? null : config.csrfPrevention.requestHeaders ?? preventCsrf_js_1.recommendedCsrfPreventionRequestHeaders,
            status400ForVariableCoercionErrors: config.status400ForVariableCoercionErrors ?? false,
            __testing_incrementalExecutionResults: config.__testing_incrementalExecutionResults,
            stringifyResult: config.stringifyResult ?? runHttpQuery_js_1.prettyJSONStringify
        };
    }
    async start() {
        return await this._start(false);
    }
    startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests() {
        this._start(true).catch((e)=>this.logStartupError(e));
    }
    async _start(startedInBackground) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error(`You should only call 'start()' or ` + `'startInBackgroundHandlingStartupErrorsByLoggingAndFailingAllRequests()' ` + `once on your ApolloServer.`);
        }
        const schemaManager = this.internals.state.schemaManager;
        const barrier = (0, resolvable_js_1.default)();
        this.internals.state = {
            phase: 'starting',
            barrier,
            schemaManager,
            startedInBackground
        };
        try {
            await this.addDefaultPlugins();
            const toDispose = [];
            const executor = await schemaManager.start();
            if (executor) {
                this.internals.gatewayExecutor = executor;
            }
            toDispose.push(async ()=>{
                await schemaManager.stop();
            });
            const schemaDerivedData = schemaManager.getSchemaDerivedData();
            const service = {
                logger: this.logger,
                cache: this.cache,
                schema: schemaDerivedData.schema,
                apollo: this.internals.apolloConfig,
                startedInBackground
            };
            const taggedServerListeners = (await Promise.all(this.internals.plugins.map(async (plugin)=>({
                    serverListener: plugin.serverWillStart && await plugin.serverWillStart(service),
                    installedImplicitly: isImplicitlyInstallablePlugin(plugin) && plugin.__internal_installed_implicitly__
                })))).filter((maybeTaggedServerListener)=>typeof maybeTaggedServerListener.serverListener === 'object');
            taggedServerListeners.forEach(({ serverListener: { schemaDidLoadOrUpdate } })=>{
                if (schemaDidLoadOrUpdate) {
                    schemaManager.onSchemaLoadOrUpdate(schemaDidLoadOrUpdate);
                }
            });
            const serverWillStops = taggedServerListeners.map((l)=>l.serverListener.serverWillStop).filter(isDefined_js_1.isDefined);
            if (serverWillStops.length) {
                toDispose.push(async ()=>{
                    await Promise.all(serverWillStops.map((serverWillStop)=>serverWillStop()));
                });
            }
            const drainServerCallbacks = taggedServerListeners.map((l)=>l.serverListener.drainServer).filter(isDefined_js_1.isDefined);
            const drainServers = drainServerCallbacks.length ? async ()=>{
                await Promise.all(drainServerCallbacks.map((drainServer)=>drainServer()));
            } : null;
            let taggedServerListenersWithRenderLandingPage = taggedServerListeners.filter((l)=>l.serverListener.renderLandingPage);
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                taggedServerListenersWithRenderLandingPage = taggedServerListenersWithRenderLandingPage.filter((l)=>!l.installedImplicitly);
            }
            let landingPage = null;
            if (taggedServerListenersWithRenderLandingPage.length > 1) {
                throw Error('Only one plugin can implement renderLandingPage.');
            } else if (taggedServerListenersWithRenderLandingPage.length) {
                landingPage = await taggedServerListenersWithRenderLandingPage[0].serverListener.renderLandingPage();
            }
            const toDisposeLast = this.maybeRegisterTerminationSignalHandlers([
                'SIGINT',
                'SIGTERM'
            ], startedInBackground);
            this.internals.state = {
                phase: 'started',
                schemaManager,
                drainServers,
                landingPage,
                toDispose,
                toDisposeLast
            };
        } catch (maybeError) {
            const error = (0, errorNormalize_js_1.ensureError)(maybeError);
            try {
                await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.startupDidFail?.({
                        error
                    })));
            } catch (pluginError) {
                this.logger.error(`startupDidFail hook threw: ${pluginError}`);
            }
            this.internals.state = {
                phase: 'failed to start',
                error
            };
            throw error;
        } finally{
            barrier.resolve();
        }
    }
    maybeRegisterTerminationSignalHandlers(signals, startedInBackground) {
        const toDisposeLast = [];
        if (this.internals.stopOnTerminationSignals === false || this.internals.stopOnTerminationSignals === undefined && !(utils_isnodelike_1.isNodeLike && this.internals.nodeEnv !== 'test' && !startedInBackground)) {
            return toDisposeLast;
        }
        let receivedSignal = false;
        const signalHandler = async (signal)=>{
            if (receivedSignal) {
                return;
            }
            receivedSignal = true;
            try {
                await this.stop();
            } catch (e) {
                this.logger.error(`stop() threw during ${signal} shutdown`);
                this.logger.error(e);
                process.exit(1);
            }
            process.kill(process.pid, signal);
        };
        signals.forEach((signal)=>{
            process.on(signal, signalHandler);
            toDisposeLast.push(async ()=>{
                process.removeListener(signal, signalHandler);
            });
        });
        return toDisposeLast;
    }
    async _ensureStarted() {
        while(true){
            switch(this.internals.state.phase){
                case 'initialized':
                    throw new Error('You need to call `server.start()` before using your Apollo Server.');
                case 'starting':
                    await this.internals.state.barrier;
                    break;
                case 'failed to start':
                    this.logStartupError(this.internals.state.error);
                    throw new Error('This data graph is missing a valid configuration. More details may be available in the server logs.');
                case 'started':
                case 'draining':
                    return this.internals.state;
                case 'stopping':
                case 'stopped':
                    this.logger.warn('A GraphQL operation was received during server shutdown. The ' + 'operation will fail. Consider draining the HTTP server on shutdown; ' + 'see https://go.apollo.dev/s/drain for details.');
                    throw new Error(`Cannot execute GraphQL operations ${this.internals.state.phase === 'stopping' ? 'while the server is stopping' : 'after the server has stopped'}.'`);
                default:
                    throw new UnreachableCaseError_js_1.UnreachableCaseError(this.internals.state);
            }
        }
    }
    assertStarted(expressionForError) {
        if (this.internals.state.phase !== 'started' && this.internals.state.phase !== 'draining' && !(this.internals.state.phase === 'starting' && this.internals.state.startedInBackground)) {
            throw new Error('You must `await server.start()` before calling `' + expressionForError + '`');
        }
    }
    logStartupError(err) {
        this.logger.error('An error occurred during Apollo Server startup. All GraphQL requests ' + 'will now fail. The startup error was: ' + (err?.message || err));
    }
    static constructSchema(config) {
        if (config.schema) {
            return config.schema;
        }
        const { typeDefs, resolvers } = config;
        const augmentedTypeDefs = Array.isArray(typeDefs) ? typeDefs : [
            typeDefs
        ];
        return (0, schema_1.makeExecutableSchema)({
            typeDefs: augmentedTypeDefs,
            resolvers
        });
    }
    static generateSchemaDerivedData(schema, providedDocumentStore) {
        (0, graphql_1.assertValidSchema)(schema);
        return {
            schema,
            documentStore: providedDocumentStore === undefined ? new utils_keyvaluecache_1.InMemoryLRUCache() : providedDocumentStore,
            documentStoreKeyPrefix: providedDocumentStore ? `${(0, computeCoreSchemaHash_js_1.computeCoreSchemaHash)((0, graphql_1.printSchema)(schema))}:` : ''
        };
    }
    async stop() {
        switch(this.internals.state.phase){
            case 'initialized':
            case 'starting':
            case 'failed to start':
                throw Error('apolloServer.stop() should only be called after `await apolloServer.start()` has succeeded');
            case 'stopped':
                if (this.internals.state.stopError) {
                    throw this.internals.state.stopError;
                }
                return;
            case 'stopping':
            case 'draining':
                {
                    await this.internals.state.barrier;
                    const state = this.internals.state;
                    if (state.phase !== 'stopped') {
                        throw Error(`Surprising post-stopping state ${state.phase}`);
                    }
                    if (state.stopError) {
                        throw state.stopError;
                    }
                    return;
                }
            case 'started':
                break;
            default:
                throw new UnreachableCaseError_js_1.UnreachableCaseError(this.internals.state);
        }
        const barrier = (0, resolvable_js_1.default)();
        const { schemaManager, drainServers, landingPage, toDispose, toDisposeLast } = this.internals.state;
        this.internals.state = {
            phase: 'draining',
            barrier,
            schemaManager,
            landingPage
        };
        try {
            await drainServers?.();
            this.internals.state = {
                phase: 'stopping',
                barrier
            };
            await Promise.all([
                ...toDispose
            ].map((dispose)=>dispose()));
            await Promise.all([
                ...toDisposeLast
            ].map((dispose)=>dispose()));
        } catch (stopError) {
            this.internals.state = {
                phase: 'stopped',
                stopError: stopError
            };
            barrier.resolve();
            throw stopError;
        }
        this.internals.state = {
            phase: 'stopped',
            stopError: null
        };
    }
    async addDefaultPlugins() {
        const { plugins, apolloConfig, nodeEnv, hideSchemaDetailsFromClientErrors } = this.internals;
        const isDev = nodeEnv !== 'production';
        const alreadyHavePluginWithInternalId = (id)=>plugins.some((p)=>(0, internalPlugin_js_1.pluginIsInternal)(p) && p.__internal_plugin_id__ === id);
        const pluginsByInternalID = new Map();
        for (const p of plugins){
            if ((0, internalPlugin_js_1.pluginIsInternal)(p)) {
                const id = p.__internal_plugin_id__;
                if (!pluginsByInternalID.has(id)) {
                    pluginsByInternalID.set(id, {
                        sawDisabled: false,
                        sawNonDisabled: false
                    });
                }
                const seen = pluginsByInternalID.get(id);
                if (p.__is_disabled_plugin__) {
                    seen.sawDisabled = true;
                } else {
                    seen.sawNonDisabled = true;
                }
                if (seen.sawDisabled && seen.sawNonDisabled) {
                    throw new Error(`You have tried to install both ApolloServerPlugin${id} and ` + `ApolloServerPlugin${id}Disabled in your server. Please choose ` + `whether or not you want to disable the feature and install the ` + `appropriate plugin for your use case.`);
                }
            }
        }
        {
            if (!alreadyHavePluginWithInternalId('CacheControl')) {
                const { ApolloServerPluginCacheControl } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/cacheControl/index.js [app-route] (ecmascript)")));
                plugins.push(ApolloServerPluginCacheControl());
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('UsageReporting');
            if (!alreadyHavePlugin && apolloConfig.key) {
                if (apolloConfig.graphRef) {
                    const { ApolloServerPluginUsageReporting } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/usageReporting/index.js [app-route] (ecmascript)")));
                    plugins.unshift(ApolloServerPluginUsageReporting({
                        __onlyIfSchemaIsNotSubgraph: true
                    }));
                } else {
                    this.logger.warn('You have specified an Apollo key but have not specified a graph ref; usage ' + 'reporting is disabled. To enable usage reporting, set the `APOLLO_GRAPH_REF` ' + 'environment variable to `your-graph-id@your-graph-variant`. To disable this ' + 'warning, install `ApolloServerPluginUsageReportingDisabled`.');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('SchemaReporting');
            const enabledViaEnvVar = process.env.APOLLO_SCHEMA_REPORTING === 'true';
            if (!alreadyHavePlugin && enabledViaEnvVar) {
                if (apolloConfig.key) {
                    const { ApolloServerPluginSchemaReporting } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/schemaReporting/index.js [app-route] (ecmascript)")));
                    plugins.push(ApolloServerPluginSchemaReporting());
                } else {
                    throw new Error("You've enabled schema reporting by setting the APOLLO_SCHEMA_REPORTING " + 'environment variable to true, but you also need to provide your ' + 'Apollo API key, via the APOLLO_KEY environment ' + 'variable or via `new ApolloServer({apollo: {key})');
                }
            }
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('InlineTrace');
            if (!alreadyHavePlugin) {
                const { ApolloServerPluginInlineTrace } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/inlineTrace/index.js [app-route] (ecmascript)")));
                plugins.push(ApolloServerPluginInlineTrace({
                    __onlyIfSchemaIsSubgraph: true
                }));
            }
        }
        const alreadyHavePlugin = alreadyHavePluginWithInternalId('LandingPageDisabled');
        if (!alreadyHavePlugin) {
            const { ApolloServerPluginLandingPageLocalDefault, ApolloServerPluginLandingPageProductionDefault } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/landingPage/default/index.js [app-route] (ecmascript)")));
            const plugin = isDev ? ApolloServerPluginLandingPageLocalDefault() : ApolloServerPluginLandingPageProductionDefault();
            if (!isImplicitlyInstallablePlugin(plugin)) {
                throw Error('default landing page plugin should be implicitly installable?');
            }
            plugin.__internal_installed_implicitly__ = true;
            plugins.push(plugin);
        }
        {
            const alreadyHavePlugin = alreadyHavePluginWithInternalId('DisableSuggestions');
            if (hideSchemaDetailsFromClientErrors && !alreadyHavePlugin) {
                const { ApolloServerPluginDisableSuggestions } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/plugin/disableSuggestions/index.js [app-route] (ecmascript)")));
                plugins.push(ApolloServerPluginDisableSuggestions());
            }
        }
    }
    addPlugin(plugin) {
        if (this.internals.state.phase !== 'initialized') {
            throw new Error("Can't add plugins after the server has started");
        }
        this.internals.plugins.push(plugin);
    }
    async executeHTTPGraphQLRequest({ httpGraphQLRequest, context }) {
        try {
            let runningServerState;
            try {
                runningServerState = await this._ensureStarted();
            } catch (error) {
                return await this.errorResponse(error, httpGraphQLRequest);
            }
            if (runningServerState.landingPage && this.prefersHTML(httpGraphQLRequest)) {
                let renderedHtml;
                if (typeof runningServerState.landingPage.html === 'string') {
                    renderedHtml = runningServerState.landingPage.html;
                } else {
                    try {
                        renderedHtml = await runningServerState.landingPage.html();
                    } catch (maybeError) {
                        const error = (0, errorNormalize_js_1.ensureError)(maybeError);
                        this.logger.error(`Landing page \`html\` function threw: ${error}`);
                        return await this.errorResponse(error, httpGraphQLRequest);
                    }
                }
                return {
                    headers: new HeaderMap_js_1.HeaderMap([
                        [
                            'content-type',
                            'text/html'
                        ]
                    ]),
                    body: {
                        kind: 'complete',
                        string: renderedHtml
                    }
                };
            }
            if (this.internals.csrfPreventionRequestHeaders) {
                (0, preventCsrf_js_1.preventCsrf)(httpGraphQLRequest.headers, this.internals.csrfPreventionRequestHeaders);
            }
            let contextValue;
            try {
                contextValue = await context();
            } catch (maybeError) {
                const error = (0, errorNormalize_js_1.ensureError)(maybeError);
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.contextCreationDidFail?.({
                            error
                        })));
                } catch (pluginError) {
                    this.logger.error(`contextCreationDidFail hook threw: ${pluginError}`);
                }
                return await this.errorResponse((0, errorNormalize_js_1.ensureGraphQLError)(error, 'Context creation failed: '), httpGraphQLRequest);
            }
            return await (0, httpBatching_js_1.runPotentiallyBatchedHttpQuery)(this, httpGraphQLRequest, contextValue, runningServerState.schemaManager.getSchemaDerivedData(), this.internals);
        } catch (maybeError_) {
            const maybeError = maybeError_;
            if (maybeError instanceof graphql_1.GraphQLError && maybeError.extensions.code === index_js_1.ApolloServerErrorCode.BAD_REQUEST) {
                try {
                    await Promise.all(this.internals.plugins.map(async (plugin)=>plugin.invalidRequestWasReceived?.({
                            error: maybeError
                        })));
                } catch (pluginError) {
                    this.logger.error(`invalidRequestWasReceived hook threw: ${pluginError}`);
                }
            }
            return await this.errorResponse(maybeError, httpGraphQLRequest);
        }
    }
    async errorResponse(error, requestHead) {
        const { formattedErrors, httpFromErrors } = (0, errorNormalize_js_1.normalizeAndFormatErrors)([
            error
        ], {
            includeStacktraceInErrorResponses: this.internals.includeStacktraceInErrorResponses,
            formatError: this.internals.formatError
        });
        return {
            status: httpFromErrors.status ?? 500,
            headers: new HeaderMap_js_1.HeaderMap([
                ...httpFromErrors.headers,
                [
                    'content-type',
                    chooseContentTypeForSingleResultResponse(requestHead) ?? exports.MEDIA_TYPES.APPLICATION_JSON
                ]
            ]),
            body: {
                kind: 'complete',
                string: await this.internals.stringifyResult({
                    errors: formattedErrors
                })
            }
        };
    }
    prefersHTML(request) {
        const acceptHeader = request.headers.get('accept');
        return request.method === 'GET' && !!acceptHeader && new negotiator_1.default({
            headers: {
                accept: acceptHeader
            }
        }).mediaType([
            exports.MEDIA_TYPES.APPLICATION_JSON,
            exports.MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            exports.MEDIA_TYPES.MULTIPART_MIXED_EXPERIMENTAL,
            exports.MEDIA_TYPES.MULTIPART_MIXED_NO_DEFER_SPEC,
            exports.MEDIA_TYPES.TEXT_HTML
        ]) === exports.MEDIA_TYPES.TEXT_HTML;
    }
    async executeOperation(request, options = {}) {
        if (this.internals.state.phase === 'initialized') {
            await this.start();
        }
        const schemaDerivedData = (await this._ensureStarted()).schemaManager.getSchemaDerivedData();
        const graphQLRequest = {
            ...request,
            query: request.query && typeof request.query !== 'string' ? (0, graphql_1.print)(request.query) : request.query
        };
        const response = await internalExecuteOperation({
            server: this,
            graphQLRequest,
            internals: this.internals,
            schemaDerivedData,
            sharedResponseHTTPGraphQLHead: null
        }, options);
        return response;
    }
}
exports.ApolloServer = ApolloServer;
async function internalExecuteOperation({ server, graphQLRequest, internals, schemaDerivedData, sharedResponseHTTPGraphQLHead }, options) {
    const requestContext = {
        logger: server.logger,
        cache: server.cache,
        schema: schemaDerivedData.schema,
        request: graphQLRequest,
        response: {
            http: sharedResponseHTTPGraphQLHead ?? (0, runHttpQuery_js_1.newHTTPGraphQLHead)()
        },
        contextValue: cloneObject(options?.contextValue ?? {}),
        metrics: {},
        overallCachePolicy: (0, cachePolicy_js_1.newCachePolicy)(),
        requestIsBatched: sharedResponseHTTPGraphQLHead !== null
    };
    try {
        return await (0, requestPipeline_js_1.processGraphQLRequest)(schemaDerivedData, server, internals, requestContext);
    } catch (maybeError) {
        const error = (0, errorNormalize_js_1.ensureError)(maybeError);
        await Promise.all(internals.plugins.map(async (plugin)=>plugin.unexpectedErrorProcessingRequest?.({
                requestContext,
                error
            })));
        server.logger.error(`Unexpected error processing request: ${error}`);
        throw new Error('Internal server error');
    }
}
exports.internalExecuteOperation = internalExecuteOperation;
function isImplicitlyInstallablePlugin(p) {
    return '__internal_installed_implicitly__' in p;
}
exports.isImplicitlyInstallablePlugin = isImplicitlyInstallablePlugin;
exports.MEDIA_TYPES = {
    APPLICATION_JSON: 'application/json; charset=utf-8',
    APPLICATION_JSON_GRAPHQL_CALLBACK: 'application/json; callbackSpec=1.0; charset=utf-8',
    APPLICATION_GRAPHQL_RESPONSE_JSON: 'application/graphql-response+json; charset=utf-8',
    MULTIPART_MIXED_NO_DEFER_SPEC: 'multipart/mixed',
    MULTIPART_MIXED_EXPERIMENTAL: 'multipart/mixed; deferSpec=20220824',
    TEXT_HTML: 'text/html'
};
function chooseContentTypeForSingleResultResponse(head) {
    const acceptHeader = head.headers.get('accept');
    if (!acceptHeader) {
        return exports.MEDIA_TYPES.APPLICATION_JSON;
    } else {
        const preferred = new negotiator_1.default({
            headers: {
                accept: head.headers.get('accept')
            }
        }).mediaType([
            exports.MEDIA_TYPES.APPLICATION_JSON,
            exports.MEDIA_TYPES.APPLICATION_GRAPHQL_RESPONSE_JSON,
            exports.MEDIA_TYPES.APPLICATION_JSON_GRAPHQL_CALLBACK
        ]);
        if (preferred) {
            return preferred;
        } else {
            return null;
        }
    }
}
exports.chooseContentTypeForSingleResultResponse = chooseContentTypeForSingleResultResponse;
function cloneObject(object) {
    return Object.assign(Object.create(Object.getPrototypeOf(object)), object);
} //# sourceMappingURL=ApolloServer.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/externalTypes/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@apollo/server/dist/cjs/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.HeaderMap = exports.ApolloServer = void 0;
var ApolloServer_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/ApolloServer.js [app-route] (ecmascript)");
Object.defineProperty(exports, "ApolloServer", {
    enumerable: true,
    get: function() {
        return ApolloServer_js_1.ApolloServer;
    }
});
var HeaderMap_js_1 = __turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/utils/HeaderMap.js [app-route] (ecmascript)");
Object.defineProperty(exports, "HeaderMap", {
    enumerable: true,
    get: function() {
        return HeaderMap_js_1.HeaderMap;
    }
});
__exportStar(__turbopack_context__.r("[project]/node_modules/@apollo/server/dist/cjs/externalTypes/index.js [app-route] (ecmascript)"), exports); //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=node_modules_%40apollo_server_dist_87fceaeb._.js.map