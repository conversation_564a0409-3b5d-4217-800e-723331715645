{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/schema.ts"], "sourcesContent": ["import { gql } from 'graphql-tag';\n\nexport const typeDefs = gql`\n  # API Key Types\n  type ApiKey {\n    id: ID!\n    name: String!\n    key: String!\n    keyId: String!\n    environment: Environment!\n    permissions: [String!]!\n    isActive: Boolean!\n    expiresAt: Float\n    lastUsedAt: Float\n    usageCount: Int!\n    rateLimit: RateLimit!\n    revokedAt: Float\n    revokedBy: ID\n    revocationReason: String\n    rotatedAt: Float\n    rotatedBy: ID\n    rotationReason: String\n    createdBy: ID!\n    createdAt: Float!\n    updatedAt: Float!\n  }\n\n  type RateLimit {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  type ApiKeyStats {\n    total: Int!\n    active: Int!\n    inactive: Int!\n    expired: Int!\n    recentlyUsed: Int!\n    totalUsage: Int!\n  }\n\n  enum Environment {\n    live\n    test\n  }\n\n  # Input Types\n  input RateLimitInput {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  input CreateApiKeyInput {\n    name: String!\n    permissions: [String!]!\n    adminId: ID!\n    environment: Environment = live\n    expiresAt: Float\n    rateLimit: RateLimitInput\n  }\n\n  input UpdateApiKeyInput {\n    apiKeyId: ID!\n    name: String\n    permissions: [String!]\n    isActive: Boolean\n    expiresAt: Float\n    rateLimit: RateLimitInput\n    updatedBy: ID!\n  }\n\n  input RevokeApiKeyInput {\n    apiKeyId: ID!\n    revokedBy: ID!\n    reason: String\n  }\n\n  input DeleteApiKeyInput {\n    apiKeyId: ID!\n    deletedBy: ID!\n    reason: String\n  }\n\n  input GetApiKeysInput {\n    search: String\n    isActive: Boolean\n    createdBy: ID\n    limit: Int\n    offset: Int\n  }\n\n  # Queries\n  type Query {\n    getApiKeys(input: GetApiKeysInput): [ApiKey!]!\n    getApiKeyById(id: ID!): ApiKey\n    getFullApiKeyById(id: ID!): ApiKey\n    getApiKeyStats: ApiKeyStats!\n    validateApiKey(key: String!): ApiKey\n  }\n\n  # Mutations\n  type Mutation {\n    createApiKey(input: CreateApiKeyInput!): ApiKey!\n    updateApiKey(input: UpdateApiKeyInput!): ApiKey!\n    revokeApiKey(input: RevokeApiKeyInput!): ApiKey!\n    deleteApiKey(input: DeleteApiKeyInput!): Boolean!\n  }\n`;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,gJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6G5B,CAAC", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/resolvers.ts"], "sourcesContent": ["import { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../../convex/_generated/api';\n\n// Initialize Convex client for GraphQL resolvers\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport const resolvers = {\n  Query: {\n    getApiKeys: async (_: any, { input }: { input?: any }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeys, {\n          search: input?.search,\n          isActive: input?.isActive,\n          createdBy: input?.createdBy,\n          limit: input?.limit,\n          offset: input?.offset,\n        });\n        \n        return result.map((apiKey: any) => ({\n          ...apiKey,\n          id: apiKey._id,\n        }));\n      } catch (error) {\n        console.error('GraphQL getApiKeys error:', error);\n        throw new Error('Failed to fetch API keys');\n      }\n    },\n\n    getApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeyById, { id });\n        if (!result) return null;\n        \n        return {\n          ...result,\n          id: result._id,\n        };\n      } catch (error) {\n        console.error('GraphQL getApiKeyById error:', error);\n        throw new Error('Failed to fetch API key');\n      }\n    },\n\n    getFullApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getFullApiKeyById, { id });\n        if (!result) return null;\n        \n        return {\n          ...result,\n          id: result._id,\n        };\n      } catch (error) {\n        console.error('GraphQL getFullApiKeyById error:', error);\n        throw new Error('Failed to fetch full API key');\n      }\n    },\n\n    getApiKeyStats: async () => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeyStats, {});\n        return result;\n      } catch (error) {\n        console.error('GraphQL getApiKeyStats error:', error);\n        throw new Error('Failed to fetch API key statistics');\n      }\n    },\n\n    validateApiKey: async (_: any, { key }: { key: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.validateApiKey, { key });\n        if (!result) return null;\n        \n        return {\n          ...result,\n          id: result._id,\n        };\n      } catch (error) {\n        console.error('GraphQL validateApiKey error:', error);\n        throw new Error('Failed to validate API key');\n      }\n    },\n  },\n\n  Mutation: {\n    createApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        const result = await convex.mutation(api.apiKeys.createApiKey, {\n          name: input.name,\n          permissions: input.permissions,\n          adminId: input.adminId,\n          environment: input.environment,\n          expiresAt: input.expiresAt,\n          rateLimit: input.rateLimit,\n        });\n        \n        return {\n          ...result,\n          id: result.id,\n        };\n      } catch (error) {\n        console.error('GraphQL createApiKey error:', error);\n        throw new Error('Failed to create API key');\n      }\n    },\n\n    updateApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        const result = await convex.mutation(api.apiKeys.updateApiKey, {\n          apiKeyId: input.apiKeyId,\n          name: input.name,\n          permissions: input.permissions,\n          isActive: input.isActive,\n          expiresAt: input.expiresAt,\n          rateLimit: input.rateLimit,\n          updatedBy: input.updatedBy,\n        });\n        \n        // Fetch the updated API key to return complete data\n        const updatedApiKey = await convex.query(api.apiKeys.getApiKeyById, { id: result });\n        return {\n          ...updatedApiKey,\n          id: updatedApiKey._id,\n        };\n      } catch (error) {\n        console.error('GraphQL updateApiKey error:', error);\n        throw new Error('Failed to update API key');\n      }\n    },\n\n    revokeApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        const result = await convex.mutation(api.apiKeys.revokeApiKey, {\n          apiKeyId: input.apiKeyId,\n          revokedBy: input.revokedBy,\n          reason: input.reason,\n        });\n        \n        // Fetch the revoked API key to return complete data\n        const revokedApiKey = await convex.query(api.apiKeys.getApiKeyById, { id: result });\n        return {\n          ...revokedApiKey,\n          id: revokedApiKey._id,\n        };\n      } catch (error) {\n        console.error('GraphQL revokeApiKey error:', error);\n        throw new Error('Failed to revoke API key');\n      }\n    },\n\n    deleteApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        await convex.mutation(api.apiKeys.deleteApiKey, {\n          apiKeyId: input.apiKeyId,\n          deletedBy: input.deletedBy,\n          reason: input.reason,\n        });\n        \n        return true;\n      } catch (error) {\n        console.error('GraphQL deleteApiKey error:', error);\n        throw new Error('Failed to delete API key');\n      }\n    },\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;AAAA;;;;;;;;AAGA,iDAAiD;AACjD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAE5B,MAAM,YAAY;IACvB,OAAO;QACL,YAAY,OAAO,GAAQ,EAAE,KAAK,EAAmB;YACnD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE;oBACxD,QAAQ,OAAO;oBACf,UAAU,OAAO;oBACjB,WAAW,OAAO;oBAClB,OAAO,OAAO;oBACd,QAAQ,OAAO;gBACjB;gBAEA,OAAO,OAAO,GAAG,CAAC,CAAC,SAAgB,CAAC;wBAClC,GAAG,MAAM;wBACT,IAAI,OAAO,GAAG;oBAChB,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,eAAe,OAAO,GAAQ,EAAE,EAAE,EAAkB;YAClD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;oBAAE;gBAAG;gBAClE,IAAI,CAAC,QAAQ,OAAO;gBAEpB,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,mBAAmB,OAAO,GAAQ,EAAE,EAAE,EAAkB;YACtD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,iBAAiB,EAAE;oBAAE;gBAAG;gBACtE,IAAI,CAAC,QAAQ,OAAO;gBAEpB,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC/D,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB,OAAO,GAAQ,EAAE,GAAG,EAAmB;YACrD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE;oBAAE;gBAAI;gBACpE,IAAI,CAAC,QAAQ,OAAO;gBAEpB,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;gBAChB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,UAAU;QACR,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;oBAC7D,MAAM,MAAM,IAAI;oBAChB,aAAa,MAAM,WAAW;oBAC9B,SAAS,MAAM,OAAO;oBACtB,aAAa,MAAM,WAAW;oBAC9B,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;gBAC5B;gBAEA,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,EAAE;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;oBAC7D,UAAU,MAAM,QAAQ;oBACxB,MAAM,MAAM,IAAI;oBAChB,aAAa,MAAM,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;gBAC5B;gBAEA,oDAAoD;gBACpD,MAAM,gBAAgB,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;oBAAE,IAAI;gBAAO;gBACjF,OAAO;oBACL,GAAG,aAAa;oBAChB,IAAI,cAAc,GAAG;gBACvB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;oBAC7D,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,QAAQ,MAAM,MAAM;gBACtB;gBAEA,oDAAoD;gBACpD,MAAM,gBAAgB,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,CAAC,aAAa,EAAE;oBAAE,IAAI;gBAAO;gBACjF,OAAO;oBACL,GAAG,aAAa;oBAChB,IAAI,cAAc,GAAG;gBACvB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;oBAC9C,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,QAAQ,MAAM,MAAM;gBACtB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/graphql/route.ts"], "sourcesContent": ["import { ApolloServer } from '@apollo/server';\nimport { startServerAndCreateNextHandler } from '@apollo/server/integrations/next';\nimport { makeExecutableSchema } from '@graphql-tools/schema';\nimport { typeDefs } from '@/lib/graphql/schema';\nimport { resolvers } from '@/lib/graphql/resolvers';\nimport { NextRequest } from 'next/server';\n\n// Create the GraphQL schema\nconst schema = makeExecutableSchema({\n  typeDefs,\n  resolvers,\n});\n\n// Create Apollo Server instance\nconst server = new ApolloServer({\n  schema,\n  introspection: process.env.NODE_ENV !== 'production',\n  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',\n});\n\n// Create the Next.js handler\nconst handler = startServerAndCreateNextHandler<NextRequest>(server, {\n  context: async (req) => {\n    // You can add authentication context here if needed\n    return {\n      req,\n    };\n  },\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;;;;AAEA;AACA;AACA;;;;;;AAGA,4BAA4B;AAC5B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE;IAClC,UAAA,iIAAA,CAAA,WAAQ;IACR,WAAA,oIAAA,CAAA,YAAS;AACX;AAEA,gCAAgC;AAChC,MAAM,SAAS,IAAI,mKAAA,CAAA,eAAY,CAAC;IAC9B;IACA,eAAe,oDAAyB;IACxC,mCAAmC,oDAAyB;AAC9D;AAEA,6BAA6B;AAC7B,MAAM,UAAU,gCAA6C,QAAQ;IACnE,SAAS,OAAO;QACd,oDAAoD;QACpD,OAAO;YACL;QACF;IACF;AACF", "debugId": null}}]}