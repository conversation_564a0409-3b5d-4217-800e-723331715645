module.exports = {

"[project]/.next-internal/server/app/api/graphql/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/graphql/schema.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "typeDefs": (()=>typeDefs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2d$tag$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql-tag/lib/index.js [app-route] (ecmascript)");
;
const typeDefs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2d$tag$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["gql"]`
  # API Key Types
  type ApiKey {
    id: ID!
    name: String!
    key: String!
    keyId: String!
    environment: Environment!
    permissions: [String!]!
    isActive: Boolean!
    expiresAt: Float
    lastUsedAt: Float
    usageCount: Int!
    rateLimit: RateLimit!
    revokedAt: Float
    revokedBy: ID
    revocationReason: String
    rotatedAt: Float
    rotatedBy: ID
    rotationReason: String
    createdBy: ID!
    createdAt: Float!
    updatedAt: Float!
  }

  type RateLimit {
    requestsPerMinute: Int!
    requestsPerHour: Int!
    requestsPerDay: Int!
    burstLimit: Int
  }

  type ApiKeyStats {
    total: Int!
    active: Int!
    inactive: Int!
    expired: Int!
    recentlyUsed: Int!
    totalUsage: Int!
  }

  enum Environment {
    live
    test
  }

  # Input Types
  input RateLimitInput {
    requestsPerMinute: Int!
    requestsPerHour: Int!
    requestsPerDay: Int!
    burstLimit: Int
  }

  input CreateApiKeyInput {
    name: String!
    permissions: [String!]!
    adminId: ID!
    environment: Environment = live
    expiresAt: Float
    rateLimit: RateLimitInput
  }

  input UpdateApiKeyInput {
    apiKeyId: ID!
    name: String
    permissions: [String!]
    isActive: Boolean
    expiresAt: Float
    rateLimit: RateLimitInput
    updatedBy: ID!
  }

  input RevokeApiKeyInput {
    apiKeyId: ID!
    revokedBy: ID!
    reason: String
  }

  input DeleteApiKeyInput {
    apiKeyId: ID!
    deletedBy: ID!
    reason: String
  }

  input GetApiKeysInput {
    search: String
    isActive: Boolean
    createdBy: ID
    limit: Int
    offset: Int
  }

  # Queries
  type Query {
    getApiKeys(input: GetApiKeysInput): [ApiKey!]!
    getApiKeyById(id: ID!): ApiKey
    getFullApiKeyById(id: ID!): ApiKey
    getApiKeyStats: ApiKeyStats!
    validateApiKey(key: String!): ApiKey
  }

  # Mutations
  type Mutation {
    createApiKey(input: CreateApiKeyInput!): ApiKey!
    updateApiKey(input: UpdateApiKeyInput!): ApiKey!
    revokeApiKey(input: RevokeApiKeyInput!): ApiKey!
    deleteApiKey(input: DeleteApiKeyInput!): Boolean!
  }
`;
}}),
"[externals]/module [external] (module, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("module", () => require("module"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/convex/_generated/api.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable */ /**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */ __turbopack_context__.s({
    "api": (()=>api),
    "internal": (()=>internal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/server/api.js [app-route] (ecmascript)");
;
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["anyApi"];
const internal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$server$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["anyApi"];
}}),
"[project]/src/lib/graphql/resolvers.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// GraphQL resolvers that use Convex backend
__turbopack_context__.s({
    "resolvers": (()=>resolvers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$index$2d$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/index-node.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/convex/dist/esm/browser/http_client.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/convex/_generated/api.js [app-route] (ecmascript)");
;
;
// Initialize Convex client for GraphQL resolvers
const convex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$convex$2f$dist$2f$esm$2f$browser$2f$http_client$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ConvexHttpClient"](("TURBOPACK compile-time value", "https://outstanding-quail-54.convex.cloud"));
const resolvers = {
    Query: {
        getApiKeys: async (_, { input })=>{
            try {
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getApiKeys, {
                    search: input?.search,
                    isActive: input?.isActive,
                    createdBy: input?.createdBy,
                    limit: input?.limit,
                    offset: input?.offset
                });
                return result.map((apiKey)=>({
                        ...apiKey,
                        id: apiKey._id
                    }));
            } catch (error) {
                console.error('GraphQL getApiKeys error:', error);
                throw new Error('Failed to fetch API keys');
            }
        },
        getApiKeyById: async (_, { id })=>{
            try {
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getApiKeyById, {
                    id: id
                });
                if (!result) {
                    return null;
                }
                return {
                    ...result,
                    id: result._id
                };
            } catch (error) {
                console.error('GraphQL getApiKeyById error:', error);
                throw new Error('Failed to fetch API key');
            }
        },
        getFullApiKeyById: async (_, { id })=>{
            try {
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getFullApiKeyById, {
                    id: id
                });
                if (!result) {
                    return null;
                }
                return {
                    ...result,
                    id: result._id
                };
            } catch (error) {
                console.error('GraphQL getFullApiKeyById error:', error);
                throw new Error('Failed to fetch full API key');
            }
        },
        getApiKeyStats: async ()=>{
            try {
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getApiKeyStats, {});
                return result;
            } catch (error) {
                console.error('GraphQL getApiKeyStats error:', error);
                throw new Error('Failed to fetch API key statistics');
            }
        },
        validateApiKey: async (_, { key })=>{
            try {
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.validateApiKey, {
                    key
                });
                if (!result) {
                    return null;
                }
                // validateApiKey already returns an object with 'id', not '_id'
                return result;
            } catch (error) {
                console.error('GraphQL validateApiKey error:', error);
                throw new Error('Failed to validate API key');
            }
        }
    },
    Mutation: {
        createApiKey: async (_, { input })=>{
            try {
                const result = await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.createApiKey, {
                    name: input.name,
                    permissions: input.permissions,
                    adminId: input.adminId,
                    environment: input.environment,
                    expiresAt: input.expiresAt,
                    rateLimit: input.rateLimit
                });
                // createApiKey already returns an object with 'id', not '_id'
                return result;
            } catch (error) {
                console.error('GraphQL createApiKey error:', error);
                throw new Error('Failed to create API key');
            }
        },
        updateApiKey: async (_, { input })=>{
            try {
                await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.updateApiKey, {
                    apiKeyId: input.apiKeyId,
                    name: input.name,
                    permissions: input.permissions,
                    isActive: input.isActive,
                    expiresAt: input.expiresAt,
                    rateLimit: input.rateLimit,
                    updatedBy: input.updatedBy
                });
                // Fetch the updated API key
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getApiKeyById, {
                    id: input.apiKeyId
                });
                if (!result) {
                    throw new Error('API key not found after update');
                }
                return {
                    ...result,
                    id: result._id
                };
            } catch (error) {
                console.error('GraphQL updateApiKey error:', error);
                throw new Error('Failed to update API key');
            }
        },
        revokeApiKey: async (_, { input })=>{
            try {
                await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.revokeApiKey, {
                    apiKeyId: input.apiKeyId,
                    revokedBy: input.revokedBy,
                    reason: input.reason
                });
                // Fetch the updated API key
                const result = await convex.query(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.getApiKeyById, {
                    id: input.apiKeyId
                });
                if (!result) {
                    throw new Error('API key not found after revocation');
                }
                return {
                    ...result,
                    id: result._id
                };
            } catch (error) {
                console.error('GraphQL revokeApiKey error:', error);
                throw new Error('Failed to revoke API key');
            }
        },
        deleteApiKey: async (_, { input })=>{
            try {
                await convex.mutation(__TURBOPACK__imported__module__$5b$project$5d2f$convex$2f$_generated$2f$api$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["api"].apiKeys.deleteApiKey, {
                    apiKeyId: input.apiKeyId,
                    deletedBy: input.deletedBy,
                    reason: input.reason
                });
                return true;
            } catch (error) {
                console.error('GraphQL deleteApiKey error:', error);
                throw new Error('Failed to delete API key');
            }
        }
    }
};
}}),
"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>handler),
    "POST": (()=>handler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/dist/esm/ApolloServer.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$as$2d$integrations$2f$next$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@as-integrations/next/dist/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/schema/esm/makeExecutableSchema.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/graphql/schema.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$resolvers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/graphql/resolvers.ts [app-route] (ecmascript)");
;
;
;
;
;
// Create the GraphQL schema
const schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$schema$2f$esm$2f$makeExecutableSchema$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["makeExecutableSchema"])({
    typeDefs: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$schema$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["typeDefs"],
    resolvers: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$graphql$2f$resolvers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resolvers"]
});
// Create Apollo Server instance
const server = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$dist$2f$esm$2f$ApolloServer$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApolloServer"]({
    schema,
    introspection: ("TURBOPACK compile-time value", "development") !== 'production',
    includeStacktraceInErrorResponses: ("TURBOPACK compile-time value", "development") !== 'production'
});
// Create the Next.js handler
const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$as$2d$integrations$2f$next$2f$dist$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["startServerAndCreateNextHandler"])(server, {
    context: async (req)=>{
        // You can add authentication context here if needed
        return {
            req
        };
    }
});
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__79cdc10b._.js.map