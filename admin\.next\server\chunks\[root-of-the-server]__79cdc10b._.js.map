{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/schema.ts"], "sourcesContent": ["import { gql } from 'graphql-tag';\n\nexport const typeDefs = gql`\n  # API Key Types\n  type ApiKey {\n    id: ID!\n    name: String!\n    key: String!\n    keyId: String!\n    environment: Environment!\n    permissions: [String!]!\n    isActive: Boolean!\n    expiresAt: Float\n    lastUsedAt: Float\n    usageCount: Int!\n    rateLimit: RateLimit!\n    revokedAt: Float\n    revokedBy: ID\n    revocationReason: String\n    rotatedAt: Float\n    rotatedBy: ID\n    rotationReason: String\n    createdBy: ID!\n    createdAt: Float!\n    updatedAt: Float!\n  }\n\n  type RateLimit {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  type ApiKeyStats {\n    total: Int!\n    active: Int!\n    inactive: Int!\n    expired: Int!\n    recentlyUsed: Int!\n    totalUsage: Int!\n  }\n\n  enum Environment {\n    live\n    test\n  }\n\n  # Input Types\n  input RateLimitInput {\n    requestsPerMinute: Int!\n    requestsPerHour: Int!\n    requestsPerDay: Int!\n    burstLimit: Int\n  }\n\n  input CreateApiKeyInput {\n    name: String!\n    permissions: [String!]!\n    adminId: ID!\n    environment: Environment = live\n    expiresAt: Float\n    rateLimit: RateLimitInput\n  }\n\n  input UpdateApiKeyInput {\n    apiKeyId: ID!\n    name: String\n    permissions: [String!]\n    isActive: Boolean\n    expiresAt: Float\n    rateLimit: RateLimitInput\n    updatedBy: ID!\n  }\n\n  input RevokeApiKeyInput {\n    apiKeyId: ID!\n    revokedBy: ID!\n    reason: String\n  }\n\n  input DeleteApiKeyInput {\n    apiKeyId: ID!\n    deletedBy: ID!\n    reason: String\n  }\n\n  input GetApiKeysInput {\n    search: String\n    isActive: Boolean\n    createdBy: ID\n    limit: Int\n    offset: Int\n  }\n\n  # Queries\n  type Query {\n    getApiKeys(input: GetApiKeysInput): [ApiKey!]!\n    getApiKeyById(id: ID!): ApiKey\n    getFullApiKeyById(id: ID!): ApiKey\n    getApiKeyStats: ApiKeyStats!\n    validateApiKey(key: String!): ApiKey\n  }\n\n  # Mutations\n  type Mutation {\n    createApiKey(input: CreateApiKeyInput!): ApiKey!\n    updateApiKey(input: UpdateApiKeyInput!): ApiKey!\n    revokeApiKey(input: RevokeApiKeyInput!): ApiKey!\n    deleteApiKey(input: DeleteApiKeyInput!): Boolean!\n  }\n`;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,gJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6G5B,CAAC", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,wJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,wJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/resolvers.ts"], "sourcesContent": ["// GraphQL resolvers that use Convex backend\nimport { ConvexHttpClient } from 'convex/browser';\nimport { api } from '../../../convex/_generated/api';\n\n// Initialize Convex client for GraphQL resolvers\nconst convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport const resolvers = {\n  Query: {\n    getApiKeys: async (_: any, { input }: { input?: any }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeys, {\n          search: input?.search,\n          isActive: input?.isActive,\n          createdBy: input?.createdBy,\n          limit: input?.limit,\n          offset: input?.offset,\n        });\n\n        return result.map((apiKey: any) => ({\n          ...apiKey,\n          id: apiKey._id,\n          rateLimit: apiKey.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        }));\n      } catch (error) {\n        console.error('GraphQL getApiKeys error:', error);\n        throw new Error('Failed to fetch API keys');\n      }\n    },\n\n    getApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeyById, { id: id as any });\n        if (!result) {\n          return null;\n        }\n        return {\n          ...result,\n          id: result._id,\n          rateLimit: result.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        };\n      } catch (error) {\n        console.error('GraphQL getApiKeyById error:', error);\n        throw new Error('Failed to fetch API key');\n      }\n    },\n\n    getFullApiKeyById: async (_: any, { id }: { id: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.getFullApiKeyById, { id: id as any });\n        if (!result) {\n          return null;\n        }\n        return {\n          ...result,\n          id: result._id,\n          rateLimit: result.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        };\n      } catch (error) {\n        console.error('GraphQL getFullApiKeyById error:', error);\n        throw new Error('Failed to fetch full API key');\n      }\n    },\n\n    getApiKeyStats: async () => {\n      try {\n        const result = await convex.query(api.apiKeys.getApiKeyStats, {});\n        return result;\n      } catch (error) {\n        console.error('GraphQL getApiKeyStats error:', error);\n        throw new Error('Failed to fetch API key statistics');\n      }\n    },\n\n    validateApiKey: async (_: any, { key }: { key: string }) => {\n      try {\n        const result = await convex.query(api.apiKeys.validateApiKey, { key });\n        if (!result) {\n          return null;\n        }\n        // validateApiKey already returns an object with 'id', not '_id'\n        return result;\n      } catch (error) {\n        console.error('GraphQL validateApiKey error:', error);\n        throw new Error('Failed to validate API key');\n      }\n    },\n  },\n\n  Mutation: {\n    createApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        const result = await convex.mutation(api.apiKeys.createApiKey, {\n          name: input.name,\n          permissions: input.permissions,\n          adminId: input.adminId as any,\n          environment: input.environment,\n          expiresAt: input.expiresAt,\n          rateLimit: input.rateLimit,\n        });\n\n        // Fetch the complete API key data to match the GraphQL schema\n        const fullApiKey = await convex.query(api.apiKeys.getFullApiKeyById, { id: result.id as any });\n        if (!fullApiKey) {\n          throw new Error('Failed to retrieve created API key');\n        }\n\n        // Return the full API key with the plain text key from creation\n        return {\n          ...fullApiKey,\n          id: fullApiKey._id,\n          key: result.key, // Use the plain text key from creation\n          rateLimit: fullApiKey.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        };\n      } catch (error) {\n        console.error('GraphQL createApiKey error:', error);\n        throw new Error('Failed to create API key');\n      }\n    },\n\n    updateApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        await convex.mutation(api.apiKeys.updateApiKey, {\n          apiKeyId: input.apiKeyId as any,\n          name: input.name,\n          permissions: input.permissions,\n          isActive: input.isActive,\n          expiresAt: input.expiresAt,\n          rateLimit: input.rateLimit,\n          updatedBy: input.updatedBy as any,\n        });\n\n        // Fetch the updated API key\n        const result = await convex.query(api.apiKeys.getApiKeyById, { id: input.apiKeyId as any });\n        if (!result) {\n          throw new Error('API key not found after update');\n        }\n\n        return {\n          ...result,\n          id: result._id,\n          rateLimit: result.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        };\n      } catch (error) {\n        console.error('GraphQL updateApiKey error:', error);\n        throw new Error('Failed to update API key');\n      }\n    },\n\n    revokeApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        await convex.mutation(api.apiKeys.revokeApiKey, {\n          apiKeyId: input.apiKeyId as any,\n          revokedBy: input.revokedBy as any,\n          reason: input.reason,\n        });\n\n        // Fetch the updated API key\n        const result = await convex.query(api.apiKeys.getApiKeyById, { id: input.apiKeyId as any });\n        if (!result) {\n          throw new Error('API key not found after revocation');\n        }\n\n        return {\n          ...result,\n          id: result._id,\n          rateLimit: result.rateLimit || {\n            requestsPerMinute: 100,\n            requestsPerHour: 5000,\n            requestsPerDay: 50000,\n            burstLimit: 150,\n          },\n        };\n      } catch (error) {\n        console.error('GraphQL revokeApiKey error:', error);\n        throw new Error('Failed to revoke API key');\n      }\n    },\n\n    deleteApiKey: async (_: any, { input }: { input: any }) => {\n      try {\n        await convex.mutation(api.apiKeys.deleteApiKey, {\n          apiKeyId: input.apiKeyId as any,\n          deletedBy: input.deletedBy as any,\n          reason: input.reason,\n        });\n        return true;\n      } catch (error) {\n        console.error('GraphQL deleteApiKey error:', error);\n        throw new Error('Failed to delete API key');\n      }\n    },\n  },\n};\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAC5C;AAAA;AACA;;;AAEA,iDAAiD;AACjD,MAAM,SAAS,IAAI,iKAAA,CAAA,mBAAgB;AAE5B,MAAM,YAAY;IACvB,OAAO;QACL,YAAY,OAAO,GAAQ,EAAE,KAAK,EAAmB;YACnD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,UAAU,EAAE;oBACxD,QAAQ,OAAO;oBACf,UAAU,OAAO;oBACjB,WAAW,OAAO;oBAClB,OAAO,OAAO;oBACd,QAAQ,OAAO;gBACjB;gBAEA,OAAO,OAAO,GAAG,CAAC,CAAC,SAAgB,CAAC;wBAClC,GAAG,MAAM;wBACT,IAAI,OAAO,GAAG;wBACd,WAAW,OAAO,SAAS,IAAI;4BAC7B,mBAAmB;4BACnB,iBAAiB;4BACjB,gBAAgB;4BAChB,YAAY;wBACd;oBACF,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,eAAe,OAAO,GAAQ,EAAE,EAAE,EAAkB;YAClD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,EAAE;oBAAE,IAAI;gBAAU;gBAC7E,IAAI,CAAC,QAAQ;oBACX,OAAO;gBACT;gBACA,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;oBACd,WAAW,OAAO,SAAS,IAAI;wBAC7B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,mBAAmB,OAAO,GAAQ,EAAE,EAAE,EAAkB;YACtD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAAE,IAAI;gBAAU;gBACjF,IAAI,CAAC,QAAQ;oBACX,OAAO;gBACT;gBACA,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;oBACd,WAAW,OAAO,SAAS,IAAI;wBAC7B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC/D,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,gBAAgB,OAAO,GAAQ,EAAE,GAAG,EAAmB;YACrD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,cAAc,EAAE;oBAAE;gBAAI;gBACpE,IAAI,CAAC,QAAQ;oBACX,OAAO;gBACT;gBACA,gEAAgE;gBAChE,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;IAEA,UAAU;QACR,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC7D,MAAM,MAAM,IAAI;oBAChB,aAAa,MAAM,WAAW;oBAC9B,SAAS,MAAM,OAAO;oBACtB,aAAa,MAAM,WAAW;oBAC9B,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;gBAC5B;gBAEA,8DAA8D;gBAC9D,MAAM,aAAa,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,iBAAiB,EAAE;oBAAE,IAAI,OAAO,EAAE;gBAAQ;gBAC5F,IAAI,CAAC,YAAY;oBACf,MAAM,IAAI,MAAM;gBAClB;gBAEA,gEAAgE;gBAChE,OAAO;oBACL,GAAG,UAAU;oBACb,IAAI,WAAW,GAAG;oBAClB,KAAK,OAAO,GAAG;oBACf,WAAW,WAAW,SAAS,IAAI;wBACjC,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC9C,UAAU,MAAM,QAAQ;oBACxB,MAAM,MAAM,IAAI;oBAChB,aAAa,MAAM,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;oBAC1B,WAAW,MAAM,SAAS;gBAC5B;gBAEA,4BAA4B;gBAC5B,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,EAAE;oBAAE,IAAI,MAAM,QAAQ;gBAAQ;gBACzF,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;oBACd,WAAW,OAAO,SAAS,IAAI;wBAC7B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC9C,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,QAAQ,MAAM,MAAM;gBACtB;gBAEA,4BAA4B;gBAC5B,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,aAAa,EAAE;oBAAE,IAAI,MAAM,QAAQ;gBAAQ;gBACzF,IAAI,CAAC,QAAQ;oBACX,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,GAAG,MAAM;oBACT,IAAI,OAAO,GAAG;oBACd,WAAW,OAAO,SAAS,IAAI;wBAC7B,mBAAmB;wBACnB,iBAAiB;wBACjB,gBAAgB;wBAChB,YAAY;oBACd;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,cAAc,OAAO,GAAQ,EAAE,KAAK,EAAkB;YACpD,IAAI;gBACF,MAAM,OAAO,QAAQ,CAAC,6HAAA,CAAA,MAAG,CAAC,OAAO,CAAC,YAAY,EAAE;oBAC9C,UAAU,MAAM,QAAQ;oBACxB,WAAW,MAAM,SAAS;oBAC1B,QAAQ,MAAM,MAAM;gBACtB;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,MAAM,IAAI,MAAM;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/api/graphql/route.ts"], "sourcesContent": ["import { ApolloServer } from '@apollo/server';\nimport { startServerAndCreateNextHandler } from '@as-integrations/next';\nimport { makeExecutableSchema } from '@graphql-tools/schema';\nimport { typeDefs } from '@/lib/graphql/schema';\nimport { resolvers } from '@/lib/graphql/resolvers';\nimport { NextRequest, NextResponse } from 'next/server';\n\n// Create the GraphQL schema\nconst schema = makeExecutableSchema({\n  typeDefs,\n  resolvers,\n});\n\n// Create Apollo Server instance\nconst server = new ApolloServer({\n  schema,\n  introspection: process.env.NODE_ENV !== 'production',\n  includeStacktraceInErrorResponses: process.env.NODE_ENV !== 'production',\n  // Security: Limit query complexity and depth (Apollo best practice)\n  plugins: [\n    // Add query complexity analysis in production\n    ...(process.env.NODE_ENV === 'production' ? [] : []),\n  ],\n});\n\n// Create the Next.js handler\nconst handler = startServerAndCreateNextHandler<NextRequest>(server, {\n  context: async (req) => {\n    // Extract authentication from request headers or cookies\n    // This follows Apollo's recommendation for putting user info in context\n    const authHeader = req.headers.get('authorization');\n    const sessionCookie = req.cookies.get('session')?.value;\n\n    return {\n      req,\n      // Add user context for authorization in resolvers\n      authHeader,\n      sessionCookie,\n    };\n  },\n});\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGA,4BAA4B;AAC5B,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,uBAAoB,AAAD,EAAE;IAClC,UAAA,iIAAA,CAAA,WAAQ;IACR,WAAA,oIAAA,CAAA,YAAS;AACX;AAEA,gCAAgC;AAChC,MAAM,SAAS,IAAI,mKAAA,CAAA,eAAY,CAAC;IAC9B;IACA,eAAe,oDAAyB;IACxC,mCAAmC,oDAAyB;IAC5D,oEAAoE;IACpE,SAAS;QACP,8CAA8C;WAC1C,6EAA6C,EAAE;KACpD;AACH;AAEA,6BAA6B;AAC7B,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,kCAA+B,AAAD,EAAe,QAAQ;IACnE,SAAS,OAAO;QACd,yDAAyD;QACzD,wEAAwE;QACxE,MAAM,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY;QAElD,OAAO;YACL;YACA,kDAAkD;YAClD;YACA;QACF;IACF;AACF", "debugId": null}}]}