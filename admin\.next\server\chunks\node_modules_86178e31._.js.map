{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/extensions.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\nexport { extractExtensionsFromSchema } from '@graphql-tools/utils';\nexport function mergeExtensions(extensions) {\n    return mergeDeep(extensions);\n}\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj) {\n        return;\n    }\n    obj.extensions = mergeDeep([obj.extensions || {}, extensions || {}]);\n}\nexport function applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS,gBAAgB,UAAU;IACtC,OAAO,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD,EAAE;AACrB;AACA,SAAS,qBAAqB,GAAG,EAAE,UAAU;IACzC,IAAI,CAAC,KAAK;QACN;IACJ;IACA,IAAI,UAAU,GAAG,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD,EAAE;QAAC,IAAI,UAAU,IAAI,CAAC;QAAG,cAAc,CAAC;KAAE;AACvE;AACO,SAAS,gBAAgB,MAAM,EAAE,UAAU;IAC9C,qBAAqB,QAAQ,WAAW,gBAAgB;IACxD,KAAK,MAAM,CAAC,UAAU,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,GAAI;QACnE,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,qBAAqB,MAAM,KAAK,UAAU;YAC1C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,aAAa;gBACrD,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,IAAI,OAAO;wBACP,qBAAqB,OAAO,UAAU,UAAU;wBAChD,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAU,SAAS,EAAG;4BAC9D,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM;wBAC/D;oBACJ;gBACJ;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,SAAS;gBAC5B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,qBAAqB,OAAO,UAAU,UAAU;gBACpD;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC3B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,QAAQ,CAAC;oBAC5B,qBAAqB,OAAO;gBAChC;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/merge-resolvers.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nexport function mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions || (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = mergeDeep(resolvers, true);\n    if (options === null || options === void 0 ? void 0 : options.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,SAAS,eAAe,oBAAoB,EAAE,OAAO;IACxD,IAAI,CAAC,wBAAyB,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,MAAM,KAAK,GAAI;QACrG,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,uBAAuB;QACtC,OAAO;IACX;IACA,IAAI,qBAAqB,MAAM,KAAK,GAAG;QACnC,OAAO,oBAAoB,CAAC,EAAE,IAAI,CAAC;IACvC;IACA,MAAM,YAAY,IAAI;IACtB,KAAK,IAAI,uBAAuB,qBAAsB;QAClD,IAAI,MAAM,OAAO,CAAC,sBAAsB;YACpC,sBAAsB,eAAe;QACzC;QACA,IAAI,OAAO,wBAAwB,YAAY,qBAAqB;YAChE,UAAU,IAAI,CAAC;QACnB;IACJ;IACA,MAAM,SAAS,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD,EAAE,WAAW;IACpC,IAAI,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,EAAE;QACtE,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;YACxC,MAAM,CAAC,UAAU,UAAU,GAAG,UAAU,KAAK,CAAC;YAC9C,IAAI,CAAC,aAAa,cAAc,KAAK;gBACjC,OAAO,MAAM,CAAC,SAAS;YAC3B,OACK,IAAI,MAAM,CAAC,SAAS,EAAE;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/utils.js"], "sourcesContent": ["import { Source, Kind } from 'graphql';\nexport function isStringTypes(types) {\n    return typeof types === 'string';\n}\nexport function isSourceTypes(types) {\n    return types instanceof Source;\n}\nexport function extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nexport function isWrappingTypeNode(type) {\n    return type.kind !== Kind.NAMED_TYPE;\n}\nexport function isListTypeNode(type) {\n    return type.kind === Kind.LIST_TYPE;\n}\nexport function isNonNullTypeNode(type) {\n    return type.kind === Kind.NON_NULL_TYPE;\n}\nexport function printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nexport var CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal || (CompareVal = {}));\nexport function defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,OAAO,UAAU;AAC5B;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,iBAAiB,gJAAA,CAAA,SAAM;AAClC;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,cAAc;IAClB,MAAO,YAAY,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,SAAS,IAAI,YAAY,IAAI,KAAK,cAAe;QAC9E,cAAc,YAAY,IAAI;IAClC;IACA,OAAO;AACX;AACO,SAAS,mBAAmB,IAAI;IACnC,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,UAAU;AACxC;AACO,SAAS,eAAe,IAAI;IAC/B,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,SAAS;AACvC;AACO,SAAS,kBAAkB,IAAI;IAClC,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,aAAa;AAC3C;AACO,SAAS,cAAc,IAAI;IAC9B,IAAI,eAAe,OAAO;QACtB,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,GAAG,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,KAAK,IAAI,CAAC,KAAK;AAC1B;AACO,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,EAAE,GAAG;AACrD,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAC1B,SAAS,wBAAwB,CAAC,EAAE,CAAC;IACxC,IAAI,KAAK,QAAQ,KAAK,MAAM;QACxB,OAAO,WAAW,UAAU;IAChC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,OAAO,WAAW,UAAU;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/directives.js"], "sourcesContent": ["import { print } from 'graphql';\nimport { isSome } from '@graphql-tools/utils';\nfunction directiveAlreadyExists(directivesArr, otherDirective) {\n    return !!directivesArr.find(directive => directive.name.value === otherDirective.name.value);\n}\nfunction isRepeatableDirective(directive, directives) {\n    var _a;\n    return !!((_a = directives === null || directives === void 0 ? void 0 : directives[directive.name.value]) === null || _a === void 0 ? void 0 : _a.repeatable);\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [...a2];\n    for (const argument of a1) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex > -1) {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value.values = deduplicateLists(source, target, (targetVal, source) => {\n                    const value = targetVal.value;\n                    return !value || !source.some((sourceVal) => sourceVal.value === value);\n                });\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n        else {\n            result.push(argument);\n        }\n    }\n    return result;\n}\nfunction deduplicateDirectives(directives, definitions) {\n    return directives\n        .map((directive, i, all) => {\n        const firstAt = all.findIndex(d => d.name.value === directive.name.value);\n        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {\n            const dup = all[firstAt];\n            directive.arguments = mergeArguments(directive.arguments, dup.arguments);\n            return null;\n        }\n        return directive;\n    })\n        .filter(isSome);\n}\nexport function mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = deduplicateDirectives([...asNext], directives);\n    for (const directive of asFirst) {\n        if (directiveAlreadyExists(result, directive) && !isRepeatableDirective(directive, directives)) {\n            const existingDirectiveIndex = result.findIndex(d => d.name.value === directive.name.value);\n            const existingDirective = result[existingDirectiveIndex];\n            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);\n        }\n        else {\n            result.push(directive);\n        }\n    }\n    return result;\n}\nfunction validateInputs(node, existingNode) {\n    const printedNode = print({\n        ...node,\n        description: undefined,\n    });\n    const printedExistingNode = print({\n        ...existingNode,\n        description: undefined,\n    });\n    // eslint-disable-next-line\n    const leaveInputs = new RegExp('(directive @w*d*)|( on .*$)', 'g');\n    const sameArguments = printedNode.replace(leaveInputs, '') === printedExistingNode.replace(leaveInputs, '');\n    if (!sameArguments) {\n        throw new Error(`Unable to merge GraphQL directive \"${node.name.value}\". \\nExisting directive:  \\n\\t${printedExistingNode} \\nReceived directive: \\n\\t${printedNode}`);\n    }\n}\nexport function mergeDirective(node, existingNode) {\n    if (existingNode) {\n        validateInputs(node, existingNode);\n        return {\n            ...node,\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACA,SAAS,uBAAuB,aAAa,EAAE,cAAc;IACzD,OAAO,CAAC,CAAC,cAAc,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK,eAAe,IAAI,CAAC,KAAK;AAC/F;AACA,SAAS,sBAAsB,SAAS,EAAE,UAAU;IAChD,IAAI;IACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;AAChK;AACA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,UAAU,KAAK,KAAK;AAC5D;AACA,SAAS,eAAe,EAAE,EAAE,EAAE;IAC1B,MAAM,SAAS;WAAI;KAAG;IACtB,KAAK,MAAM,YAAY,GAAI;QACvB,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK;QAChF,IAAI,gBAAgB,CAAC,GAAG;YACpB,MAAM,cAAc,MAAM,CAAC,cAAc;YACzC,IAAI,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa;gBACxC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM;gBACvC,MAAM,SAAS,SAAS,KAAK,CAAC,MAAM;gBACpC,4BAA4B;gBAC5B,YAAY,KAAK,CAAC,MAAM,GAAG,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;oBACpE,MAAM,QAAQ,UAAU,KAAK;oBAC7B,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;gBACrE;YACJ,OACK;gBACD,YAAY,KAAK,GAAG,SAAS,KAAK;YACtC;QACJ,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACA,SAAS,sBAAsB,UAAU,EAAE,WAAW;IAClD,OAAO,WACF,GAAG,CAAC,CAAC,WAAW,GAAG;QACpB,MAAM,UAAU,IAAI,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;QACxE,IAAI,YAAY,KAAK,CAAC,sBAAsB,WAAW,cAAc;YACjE,MAAM,MAAM,GAAG,CAAC,QAAQ;YACxB,UAAU,SAAS,GAAG,eAAe,UAAU,SAAS,EAAE,IAAI,SAAS;YACvE,OAAO;QACX;QACA,OAAO;IACX,GACK,MAAM,CAAC,qMAAA,CAAA,SAAM;AACtB;AACO,SAAS,gBAAgB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU;IAChE,MAAM,eAAe,UAAU,OAAO,iBAAiB;IACvD,MAAM,SAAS,eAAe,KAAK;IACnC,MAAM,UAAU,eAAe,KAAK;IACpC,MAAM,SAAS,sBAAsB;WAAI;KAAO,EAAE;IAClD,KAAK,MAAM,aAAa,QAAS;QAC7B,IAAI,uBAAuB,QAAQ,cAAc,CAAC,sBAAsB,WAAW,aAAa;YAC5F,MAAM,yBAAyB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;YAC1F,MAAM,oBAAoB,MAAM,CAAC,uBAAuB;YACxD,MAAM,CAAC,uBAAuB,CAAC,SAAS,GAAG,eAAe,UAAU,SAAS,IAAI,EAAE,EAAE,kBAAkB,SAAS,IAAI,EAAE;QAC1H,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,YAAY;IACtC,MAAM,cAAc,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACtB,GAAG,IAAI;QACP,aAAa;IACjB;IACA,MAAM,sBAAsB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QAC9B,GAAG,YAAY;QACf,aAAa;IACjB;IACA,2BAA2B;IAC3B,MAAM,cAAc,IAAI,OAAO,+BAA+B;IAC9D,MAAM,gBAAgB,YAAY,OAAO,CAAC,aAAa,QAAQ,oBAAoB,OAAO,CAAC,aAAa;IACxG,IAAI,CAAC,eAAe;QAChB,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,oBAAoB,2BAA2B,EAAE,aAAa;IACxK;AACJ;AACO,SAAS,eAAe,IAAI,EAAE,YAAY;IAC7C,IAAI,cAAc;QACd,eAAe,MAAM;QACrB,OAAO;YACH,GAAG,IAAI;YACP,WAAW;mBACJ,aAAa,SAAS;mBACtB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS;aACnF;QACL;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC9C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,MAAO,SAAS,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/arguments.js"], "sourcesContent": ["import { compareNodes, isSome } from '@graphql-tools/utils';\nexport function mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(isSome), config);\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!(config === null || config === void 0 ? void 0 : config.reverseArguments)) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IAC/C,MAAM,SAAS,qBAAqB;WAAI;WAAU;KAAM,CAAC,MAAM,CAAC,qMAAA,CAAA,SAAM,GAAG;IACzE,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,qMAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACtC,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,WAAW,IAAI,SAAS,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;QAC3E,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO,IAAI,MAAM,CAAC;gBAAC;aAAQ;QAC/B,OACK,IAAI,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,gBAAgB,GAAG;YACjF,GAAG,CAAC,SAAS,GAAG;QACpB;QACA,OAAO;IACX,GAAG,EAAE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/fields.js"], "sourcesContent": ["import { extractType, isWrappingTypeNode, isListTypeNode, isNonNullTypeNode, printTypeNode } from './utils.js';\nimport { mergeDirectives } from './directives.js';\nimport { compareNodes } from '@graphql-tools/utils';\nimport { mergeArguments } from './arguments.js';\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nexport function mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !(config === null || config === void 0 ? void 0 : config.ignoreFieldConflicts)) {\n                const newField = ((config === null || config === void 0 ? void 0 : config.onFieldTypeConflict) && config.onFieldTypeConflict(existing, field, type, config === null || config === void 0 ? void 0 : config.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config === null || config === void 0 ? void 0 : config.throwOnConflict);\n                newField.arguments = mergeArguments(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = mergeDirectives(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = printTypeNode(a.type);\n    const bType = printTypeNode(b.type);\n    if (aType !== bType) {\n        const t1 = extractType(a.type);\n        const t2 = extractType(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if (isNonNullTypeNode(b.type) && !isNonNullTypeNode(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!isWrappingTypeNode(oldType) && !isWrappingTypeNode(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if (isNonNullTypeNode(newType)) {\n        const ofType = isNonNullTypeNode(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if (isNonNullTypeNode(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if (isListTypeNode(oldType)) {\n        return ((isListTypeNode(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            (isNonNullTypeNode(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,mBAAmB,SAAS,EAAE,UAAU;IAC7C,MAAM,cAAc,UAAU,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK;IAC3F,OAAO;QAAC,cAAc,CAAC,IAAI,SAAS,CAAC,YAAY,GAAG;QAAM;KAAY;AAC1E;AACO,SAAS,YAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACxD,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO,IAAI,IAAI;IACnB;IACA,IAAI,MAAM,MAAM;QACZ,KAAK,MAAM,SAAS,GAAI;YACpB,MAAM,CAAC,UAAU,cAAc,GAAG,mBAAmB,QAAQ;YAC7D,IAAI,YAAY,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,oBAAoB,GAAG;gBAC5F,MAAM,WAAW,AAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,KAAK,OAAO,mBAAmB,CAAC,UAAU,OAAO,MAAM,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe,KACtN,iBAAiB,MAAM,UAAU,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe;gBAClH,SAAS,SAAS,GAAG,CAAA,GAAA,8NAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE;gBAC3F,SAAS,UAAU,GAAG,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,UAAU,EAAE,SAAS,UAAU,EAAE,QAAQ;gBACrF,SAAS,WAAW,GAAG,MAAM,WAAW,IAAI,SAAS,WAAW;gBAChE,MAAM,CAAC,cAAc,GAAG;YAC5B,OACK;gBACD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ;IACA,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,qMAAA,CAAA,eAAY;IAC5B;IACA,IAAI,UAAU,OAAO,UAAU,EAAE;QAC7B,MAAM,aAAa,OAAO,UAAU;QACpC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;IAC/F;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,KAAK;IAC3D,MAAM,QAAQ,CAAA,GAAA,0NAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,IAAI;IAClC,MAAM,QAAQ,CAAA,GAAA,0NAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,IAAI;IAClC,IAAI,UAAU,OAAO;QACjB,MAAM,KAAK,CAAA,GAAA,0NAAA,CAAA,cAAW,AAAD,EAAE,EAAE,IAAI;QAC7B,MAAM,KAAK,CAAA,GAAA,0NAAA,CAAA,cAAW,AAAD,EAAE,EAAE,IAAI;QAC7B,IAAI,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtK;QACA,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB;YAC7D,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3G;IACJ;IACA,IAAI,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,IAAI,KAAK,CAAC,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,IAAI,GAAG;QACzD,EAAE,IAAI,GAAG,EAAE,IAAI;IACnB;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB,KAAK;IACvE,iBAAiB;IACjB,IAAI,CAAC,CAAA,GAAA,0NAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,CAAC,CAAA,GAAA,0NAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;QAC9D,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;IAClD;IACA,kBAAkB;IAClB,IAAI,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QAC5B,MAAM,SAAS,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,QAAQ,IAAI,GAAG;QAC3D,OAAO,uBAAuB,QAAQ,QAAQ,IAAI;IACtD;IACA,kBAAkB;IAClB,IAAI,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QAC5B,OAAO,uBAAuB,SAAS,SAAS;IACpD;IACA,cAAc;IACd,IAAI,CAAA,GAAA,0NAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QACzB,OAAQ,AAAC,CAAA,GAAA,0NAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,uBAAuB,QAAQ,IAAI,EAAE,QAAQ,IAAI,KAChF,CAAA,GAAA,0NAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,uBAAuB,SAAS,OAAO,CAAC,OAAO;IACtF;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nexport function mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,GAAG,EAAE,KAAK;IAC7B,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK;AAC5D;AACO,SAAS,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;IACpE,MAAM,SAAS;WAAI;WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,cAAc,QAAQ;KAAI;IAC3E,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,qMAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/type.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeFields } from './fields.js';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: mergeNamedTypeArray(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC5D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,2NAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC5D,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,CAAA,GAAA,oPAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE;YAC9E;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QACrF;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum-values.js"], "sourcesContent": ["import { mergeDirectives } from './directives.js';\nimport { compareNodes } from '@graphql-tools/utils';\nexport function mergeEnumValues(first, second, config, directives) {\n    if (config === null || config === void 0 ? void 0 : config.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = mergeDirectives(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IAC7D,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,MAAM,WAAW,EAAE;QACnB,IAAI,OAAO;YACP,SAAS,IAAI,IAAI;QACrB;QACA,QAAQ;QACR,SAAS;IACb;IACA,MAAM,eAAe,IAAI;IACzB,IAAI,OAAO;QACP,KAAK,MAAM,cAAc,MAAO;YAC5B,aAAa,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE;QAC5C;IACJ;IACA,IAAI,QAAQ;QACR,KAAK,MAAM,eAAe,OAAQ;YAC9B,MAAM,YAAY,YAAY,IAAI,CAAC,KAAK;YACxC,IAAI,aAAa,GAAG,CAAC,YAAY;gBAC7B,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,WAAW,WAAW,GAAG,YAAY,WAAW,IAAI,WAAW,WAAW;gBAC1E,WAAW,UAAU,GAAG,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,UAAU,EAAE,WAAW,UAAU,EAAE;YAC3F,OACK;gBACD,aAAa,GAAG,CAAC,WAAW;YAChC;QACJ;IACJ;IACA,MAAM,SAAS;WAAI,aAAa,MAAM;KAAG;IACzC,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,qMAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeEnumValues } from './enum-values.js';\nexport function mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || e1.kind === 'EnumTypeDefinition' || e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: mergeDirectives(e1.directives, e2.directives, config, directives),\n            values: mergeEnumValues(e1.values, e2.values, config),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...e1,\n            kind: Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IAChD,IAAI,IAAI;QACJ,OAAO;YACH,MAAM,GAAG,IAAI;YACb,aAAa,EAAE,CAAC,cAAc,IAAI,EAAE,CAAC,cAAc;YACnD,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAAK,GAAG,IAAI,KAAK,wBAAwB,GAAG,IAAI,KAAK,uBAC9H,uBACA;YACN,KAAK,GAAG,GAAG;YACX,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,QAAQ;YAClE,QAAQ,CAAA,GAAA,mOAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QAClD;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,EAAE;QACL,MAAM,+IAAA,CAAA,OAAI,CAAC,oBAAoB;IACnC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/scalar.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport function mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,YAAY,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC9D,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI;YACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;YACN,KAAK,KAAK,GAAG;YACb,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;QAClF;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/union.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: mergeDirectives(first.directives, second.directives, config, directives),\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || first.kind === 'UnionTypeDefinition' || second.kind === 'UnionTypeDefinition'\n                ? Kind.UNION_TYPE_DEFINITION\n                : Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: mergeNamedTypeArray(first.types, second.types, config),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...first,\n            kind: Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACxD,IAAI,QAAQ;QACR,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,aAAa,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc;YAC1D,0HAA0H;YAC1H,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;YACzE,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAAK,MAAM,IAAI,KAAK,yBAAyB,OAAO,IAAI,KAAK,wBACtI,+IAAA,CAAA,OAAI,CAAC,qBAAqB,GAC1B,+IAAA,CAAA,OAAI,CAAC,oBAAoB;YAC/B,KAAK,MAAM,GAAG;YACd,OAAO,CAAA,GAAA,oPAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,KAAK,EAAE,OAAO,KAAK,EAAE;QAC1D;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,KAAK;QACR,MAAM,+IAAA,CAAA,OAAI,CAAC,qBAAqB;IACpC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/input-type.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeFields } from './fields.js';\nimport { mergeDirectives } from './directives.js';\nexport function mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,+BACd,aAAa,IAAI,KAAK,8BACpB,8BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,2NAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC5D,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAClF;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC3F;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,4BAA4B;IAC3C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/interface.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeFields } from './fields.js';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? mergeNamedTypeArray(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,6BACd,aAAa,IAAI,KAAK,4BACpB,4BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,2NAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC5D,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,IAAI,CAAC,aAAa,GACxB,CAAA,GAAA,oPAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,EAAE,UACpE;YACV;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC1F;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;IACxC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 685, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/schema-def.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport const DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) || existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nexport function mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === Kind.SCHEMA_DEFINITION || existingNode.kind === Kind.SCHEMA_DEFINITION\n                ? Kind.SCHEMA_DEFINITION\n                : Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return ((config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,MAAM,kCAAkC;IAC3C,OAAO;IACP,UAAU;IACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,EAAE,EAAE,qBAAqB,EAAE;IACjE,MAAM,kBAAkB,EAAE;IAC1B,IAAK,MAAM,cAAc,gCAAiC;QACtD,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,eAAe,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChH,IAAI,QAAQ;YACR,gBAAgB,IAAI,CAAC;QACzB;IACJ;IACA,OAAO;AACX;AACO,SAAS,gBAAgB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAClE,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,GACpF,+IAAA,CAAA,OAAI,CAAC,iBAAiB,GACtB,+IAAA,CAAA,OAAI,CAAC,gBAAgB;YAC3B,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,YAAY,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAC9E,gBAAgB,oBAAoB,KAAK,cAAc,EAAE,aAAa,cAAc;QACxF;IACJ;IACA,OAAQ,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC3E;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;IAChC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js"], "sourcesContent": ["import { Kind } from 'graphql';\nimport { mergeType } from './type.js';\nimport { mergeEnum } from './enum.js';\nimport { mergeScalar } from './scalar.js';\nimport { mergeUnion } from './union.js';\nimport { mergeInputType } from './input-type.js';\nimport { mergeInterface } from './interface.js';\nimport { mergeDirective } from './directives.js';\nimport { mergeSchemaDefs } from './schema-def.js';\nimport { collectComment } from '@graphql-tools/utils';\nexport const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nexport function isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nexport function mergeGraphQLNodes(nodes, config, directives = {}) {\n    var _a, _b, _c;\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = (_a = nodeDefinition.name) === null || _a === void 0 ? void 0 : _a.value;\n            if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {\n                collectComment(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (((_b = config === null || config === void 0 ? void 0 : config.exclusions) === null || _b === void 0 ? void 0 : _b.includes(name + '.*')) || ((_c = config === null || config === void 0 ? void 0 : config.exclusions) === null || _c === void 0 ? void 0 : _c.includes(name))) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case Kind.OBJECT_TYPE_DEFINITION:\n                    case Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.ENUM_TYPE_DEFINITION:\n                    case Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeEnum(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.UNION_TYPE_DEFINITION:\n                    case Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeUnion(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.SCALAR_TYPE_DEFINITION:\n                    case Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeScalar(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInputType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INTERFACE_TYPE_DEFINITION:\n                    case Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInterface(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.DIRECTIVE_DEFINITION:\n                        mergedResultMap[name] = mergeDirective(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === Kind.SCHEMA_DEFINITION || nodeDefinition.kind === Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[schemaDefSymbol] = mergeSchemaDefs(nodeDefinition, mergedResultMap[schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,MAAM,kBAAkB;AACxB,SAAS,sBAAsB,cAAc;IAChD,OAAO,UAAU;AACrB;AACO,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC5D,IAAI,IAAI,IAAI;IACZ,MAAM,kBAAkB;IACxB,KAAK,MAAM,kBAAkB,MAAO;QAChC,IAAI,sBAAsB,iBAAiB;YACvC,MAAM,OAAO,CAAC,KAAK,eAAe,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;YACrF,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;gBAC5E,CAAA,GAAA,sMAAA,CAAA,iBAAc,AAAD,EAAE;YACnB;YACA,IAAI,QAAQ,MAAM;gBACd;YACJ;YACA,IAAI,CAAC,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,GAAG;gBAC/Q,OAAO,eAAe,CAAC,KAAK;YAChC,OACK;gBACD,OAAQ,eAAe,IAAI;oBACvB,KAAK,+IAAA,CAAA,OAAI,CAAC,sBAAsB;oBAChC,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,yNAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;oBAC9B,KAAK,+IAAA,CAAA,OAAI,CAAC,mBAAmB;wBACzB,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,yNAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;oBAC/B,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;wBAC1B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,0NAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAClF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,sBAAsB;oBAChC,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,2NAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACnF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,4BAA4B;oBACtC,KAAK,+IAAA,CAAA,OAAI,CAAC,2BAA2B;wBACjC,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,kOAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,yBAAyB;oBACnC,KAAK,+IAAA,CAAA,OAAI,CAAC,wBAAwB;wBAC9B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,8NAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;wBAC1B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,+NAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK;wBAC5E;gBACR;YACJ;QACJ,OACK,IAAI,eAAe,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,IAAI,eAAe,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,gBAAgB,EAAE;YACtG,eAAe,CAAC,gBAAgB,GAAG,CAAA,GAAA,kOAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe,CAAC,gBAAgB,EAAE;QACzG;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js"], "sourcesContent": ["import { parse, Kind, isSchema, isDefinitionNode, } from 'graphql';\nimport { defaultStringComparator, isSourceTypes, isStringTypes } from './utils.js';\nimport { mergeGraphQLNodes, schemaDefSymbol } from './merge-nodes.js';\nimport { getDocumentNodeFromSchema, isDocumentNode, resetComments, printWithComments, } from '@graphql-tools/utils';\nimport { DEFAULT_OPERATION_TYPE_NAME_MAP } from './schema-def.js';\nexport function mergeTypeDefs(typeSource, config) {\n    resetComments();\n    const doc = {\n        kind: Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {\n        result = printWithComments(doc);\n    }\n    else {\n        result = doc;\n    }\n    resetComments();\n    return result;\n}\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);\n            }\n        }\n        else if (isSchema(typeSource)) {\n            const documentNode = getDocumentNodeFromSchema(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (isStringTypes(typeSource) || isSourceTypes(typeSource)) {\n            const documentNode = parse(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (typeof typeSource === 'object' && isDefinitionNode(typeSource)) {\n            if (typeSource.kind === Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if (isDocumentNode(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes };\n}\nexport function mergeGraphQLTypes(typeSource, config) {\n    var _a, _b, _c;\n    resetComments();\n    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);\n    const mergedDirectives = mergeGraphQLNodes(allDirectives, config);\n    const mergedNodes = mergeGraphQLNodes(allNodes, config, mergedDirectives);\n    if (config === null || config === void 0 ? void 0 : config.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[schemaDefSymbol] || {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (((_a = schemaDef === null || schemaDef === void 0 ? void 0 : schemaDef.operationTypes) === null || _a === void 0 ? void 0 : _a.length) != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[schemaDefSymbol] = schemaDef;\n        }\n    }\n    if ((config === null || config === void 0 ? void 0 : config.forceSchemaDefinition) && !((_c = (_b = mergedNodes[schemaDefSymbol]) === null || _b === void 0 ? void 0 : _b.operationTypes) === null || _c === void 0 ? void 0 : _c.length)) {\n        mergedNodes[schemaDefSymbol] = {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config === null || config === void 0 ? void 0 : config.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => { var _a, _b; return sortFn((_a = a.name) === null || _a === void 0 ? void 0 : _a.value, (_b = b.name) === null || _b === void 0 ? void 0 : _b.value); });\n    }\n    return mergedNodeDefinitions;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AACO,SAAS,cAAc,UAAU,EAAE,MAAM;IAC5C,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD;IACZ,MAAM,MAAM;QACR,MAAM,+IAAA,CAAA,OAAI,CAAC,QAAQ;QACnB,aAAa,kBAAkB,YAAY;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,iBAAiB;YACjB,qBAAqB;YACrB,GAAG,MAAM;QACb;IACJ;IACA,IAAI;IACJ,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,SAAS,CAAA,GAAA,sMAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,OACK;QACD,SAAS;IACb;IACA,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD;IACZ,OAAO;AACX;AACA,SAAS,iBAAiB,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,WAAW,EAAE,EAAE,qBAAqB,IAAI,KAAK;IAC5G,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,aAAa;QACnD,mBAAmB,GAAG,CAAC;QACvB,IAAI,OAAO,eAAe,YAAY;YAClC,iBAAiB,cAAc,SAAS,eAAe,UAAU;QACrE,OACK,IAAI,MAAM,OAAO,CAAC,aAAa;YAChC,KAAK,MAAM,QAAQ,WAAY;gBAC3B,iBAAiB,MAAM,SAAS,eAAe,UAAU;YAC7D;QACJ,OACK,IAAI,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC3B,MAAM,eAAe,CAAA,GAAA,mOAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY;YAC3D,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,CAAA,GAAA,0NAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,CAAA,GAAA,0NAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;YAC7D,MAAM,eAAe,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,YAAY;YACvC,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,OAAO,eAAe,YAAY,CAAA,GAAA,oJAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;YACrE,IAAI,WAAW,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB,EAAE;gBAC/C,cAAc,IAAI,CAAC;YACvB,OACK;gBACD,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,CAAA,GAAA,4MAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;YACjC,iBAAiB,WAAW,WAAW,EAAE,SAAS,eAAe,UAAU;QAC/E,OACK;YACD,MAAM,IAAI,MAAM,CAAC,0EAA0E,EAAE,OAAO,YAAY;QACpH;IACJ;IACA,OAAO;QAAE;QAAe;IAAS;AACrC;AACO,SAAS,kBAAkB,UAAU,EAAE,MAAM;IAChD,IAAI,IAAI,IAAI;IACZ,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD;IACZ,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,iBAAiB,YAAY;IACjE,MAAM,mBAAmB,CAAA,GAAA,mOAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IAC1D,MAAM,cAAc,CAAA,GAAA,mOAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;IACxD,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,6DAA6D;QAC7D,MAAM,YAAY,WAAW,CAAC,mOAAA,CAAA,kBAAe,CAAC,IAAI;YAC9C,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;YAC5B,gBAAgB,EAAE;QACtB;QACA,MAAM,iBAAiB,UAAU,cAAc;QAC/C,IAAK,MAAM,qBAAqB,kOAAA,CAAA,kCAA+B,CAAE;YAC7D,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,gBAAiB,cAAc,SAAS,KAAK;YACvF,IAAI,CAAC,eAAe;gBAChB,MAAM,uBAAuB,kOAAA,CAAA,kCAA+B,CAAC,kBAAkB;gBAC/E,MAAM,2BAA2B,WAAW,CAAC,qBAAqB;gBAClE,IAAI,4BAA4B,QAAQ,yBAAyB,IAAI,IAAI,MAAM;oBAC3E,eAAe,IAAI,CAAC;wBAChB,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;wBACpC,MAAM;4BACF,MAAM,+IAAA,CAAA,OAAI,CAAC,UAAU;4BACrB,MAAM,yBAAyB,IAAI;wBACvC;wBACA,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,CAAC,KAAK,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,UAAU,cAAc,CAAC,MAAM,GAAG,GAAG;YACvL,WAAW,CAAC,mOAAA,CAAA,kBAAe,CAAC,GAAG;QACnC;IACJ;IACA,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,qBAAqB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC,mOAAA,CAAA,kBAAe,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG;QACvO,WAAW,CAAC,mOAAA,CAAA,kBAAe,CAAC,GAAG;YAC3B,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;YAC5B,gBAAgB;gBACZ;oBACI,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;oBACpC,WAAW;oBACX,MAAM;wBACF,MAAM,+IAAA,CAAA,OAAI,CAAC,UAAU;wBACrB,MAAM;4BACF,MAAM,+IAAA,CAAA,OAAI,CAAC,IAAI;4BACf,OAAO;wBACX;oBACJ;gBACJ;aACH;QACL;IACJ;IACA,MAAM,wBAAwB,OAAO,MAAM,CAAC;IAC5C,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,EAAE;QAC7D,MAAM,SAAS,OAAO,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG,0NAAA,CAAA,0BAAuB;QACxF,sBAAsB,IAAI,CAAC,CAAC,GAAG;YAAQ,IAAI,IAAI;YAAI,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QAAG;IAChM;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/merge-resolvers.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeResolvers = void 0;\nconst utils_1 = require(\"@graphql-tools/utils\");\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nfunction mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions || (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = (0, utils_1.mergeDeep)(resolvers, true);\n    if (options === null || options === void 0 ? void 0 : options.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\nexports.mergeResolvers = mergeResolvers;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS,eAAe,oBAAoB,EAAE,OAAO;IACjD,IAAI,CAAC,wBAAyB,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,MAAM,KAAK,GAAI;QACrG,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,uBAAuB;QACtC,OAAO;IACX;IACA,IAAI,qBAAqB,MAAM,KAAK,GAAG;QACnC,OAAO,oBAAoB,CAAC,EAAE,IAAI,CAAC;IACvC;IACA,MAAM,YAAY,IAAI;IACtB,KAAK,IAAI,uBAAuB,qBAAsB;QAClD,IAAI,MAAM,OAAO,CAAC,sBAAsB;YACpC,sBAAsB,eAAe;QACzC;QACA,IAAI,OAAO,wBAAwB,YAAY,qBAAqB;YAChE,UAAU,IAAI,CAAC;QACnB;IACJ;IACA,MAAM,SAAS,CAAC,GAAG,QAAQ,SAAS,EAAE,WAAW;IACjD,IAAI,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,EAAE;QACtE,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;YACxC,MAAM,CAAC,UAAU,UAAU,GAAG,UAAU,KAAK,CAAC;YAC9C,IAAI,CAAC,aAAa,cAAc,KAAK;gBACjC,OAAO,MAAM,CAAC,SAAS;YAC3B,OACK,IAAI,MAAM,CAAC,SAAS,EAAE;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC;QACJ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/arguments.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeArguments = void 0;\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(utils_1.isSome), config);\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\nexports.mergeArguments = mergeArguments;\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!(config === null || config === void 0 ? void 0 : config.reverseArguments)) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IACxC,MAAM,SAAS,qBAAqB;WAAI;WAAU;KAAM,CAAC,MAAM,CAAC,QAAQ,MAAM,GAAG;IACjF,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACtC,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,WAAW,IAAI,SAAS,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;QAC3E,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO,IAAI,MAAM,CAAC;gBAAC;aAAQ;QAC/B,OACK,IAAI,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,gBAAgB,GAAG;YACjF,GAAG,CAAC,SAAS,GAAG;QACpB;QACA,OAAO;IACX,GAAG,EAAE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/directives.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeDirective = exports.mergeDirectives = void 0;\nconst graphql_1 = require(\"graphql\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction directiveAlreadyExists(directivesArr, otherDirective) {\n    return !!directivesArr.find(directive => directive.name.value === otherDirective.name.value);\n}\nfunction isRepeatableDirective(directive, directives) {\n    var _a;\n    return !!((_a = directives === null || directives === void 0 ? void 0 : directives[directive.name.value]) === null || _a === void 0 ? void 0 : _a.repeatable);\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [...a2];\n    for (const argument of a1) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex > -1) {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value.values = deduplicateLists(source, target, (targetVal, source) => {\n                    const value = targetVal.value;\n                    return !value || !source.some((sourceVal) => sourceVal.value === value);\n                });\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n        else {\n            result.push(argument);\n        }\n    }\n    return result;\n}\nfunction deduplicateDirectives(directives, definitions) {\n    return directives\n        .map((directive, i, all) => {\n        const firstAt = all.findIndex(d => d.name.value === directive.name.value);\n        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {\n            const dup = all[firstAt];\n            directive.arguments = mergeArguments(directive.arguments, dup.arguments);\n            return null;\n        }\n        return directive;\n    })\n        .filter(utils_1.isSome);\n}\nfunction mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = deduplicateDirectives([...asNext], directives);\n    for (const directive of asFirst) {\n        if (directiveAlreadyExists(result, directive) && !isRepeatableDirective(directive, directives)) {\n            const existingDirectiveIndex = result.findIndex(d => d.name.value === directive.name.value);\n            const existingDirective = result[existingDirectiveIndex];\n            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);\n        }\n        else {\n            result.push(directive);\n        }\n    }\n    return result;\n}\nexports.mergeDirectives = mergeDirectives;\nfunction validateInputs(node, existingNode) {\n    const printedNode = (0, graphql_1.print)({\n        ...node,\n        description: undefined,\n    });\n    const printedExistingNode = (0, graphql_1.print)({\n        ...existingNode,\n        description: undefined,\n    });\n    // eslint-disable-next-line\n    const leaveInputs = new RegExp('(directive @w*d*)|( on .*$)', 'g');\n    const sameArguments = printedNode.replace(leaveInputs, '') === printedExistingNode.replace(leaveInputs, '');\n    if (!sameArguments) {\n        throw new Error(`Unable to merge GraphQL directive \"${node.name.value}\". \\nExisting directive:  \\n\\t${printedExistingNode} \\nReceived directive: \\n\\t${printedNode}`);\n    }\n}\nfunction mergeDirective(node, existingNode) {\n    if (existingNode) {\n        validateInputs(node, existingNode);\n        return {\n            ...node,\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nexports.mergeDirective = mergeDirective;\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,KAAK;AACxD,MAAM;AACN,MAAM;AACN,SAAS,uBAAuB,aAAa,EAAE,cAAc;IACzD,OAAO,CAAC,CAAC,cAAc,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK,eAAe,IAAI,CAAC,KAAK;AAC/F;AACA,SAAS,sBAAsB,SAAS,EAAE,UAAU;IAChD,IAAI;IACJ,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU;AAChK;AACA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,UAAU,KAAK,KAAK;AAC5D;AACA,SAAS,eAAe,EAAE,EAAE,EAAE;IAC1B,MAAM,SAAS;WAAI;KAAG;IACtB,KAAK,MAAM,YAAY,GAAI;QACvB,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK;QAChF,IAAI,gBAAgB,CAAC,GAAG;YACpB,MAAM,cAAc,MAAM,CAAC,cAAc;YACzC,IAAI,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa;gBACxC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM;gBACvC,MAAM,SAAS,SAAS,KAAK,CAAC,MAAM;gBACpC,4BAA4B;gBAC5B,YAAY,KAAK,CAAC,MAAM,GAAG,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;oBACpE,MAAM,QAAQ,UAAU,KAAK;oBAC7B,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;gBACrE;YACJ,OACK;gBACD,YAAY,KAAK,GAAG,SAAS,KAAK;YACtC;QACJ,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACA,SAAS,sBAAsB,UAAU,EAAE,WAAW;IAClD,OAAO,WACF,GAAG,CAAC,CAAC,WAAW,GAAG;QACpB,MAAM,UAAU,IAAI,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;QACxE,IAAI,YAAY,KAAK,CAAC,sBAAsB,WAAW,cAAc;YACjE,MAAM,MAAM,GAAG,CAAC,QAAQ;YACxB,UAAU,SAAS,GAAG,eAAe,UAAU,SAAS,EAAE,IAAI,SAAS;YACvE,OAAO;QACX;QACA,OAAO;IACX,GACK,MAAM,CAAC,QAAQ,MAAM;AAC9B;AACA,SAAS,gBAAgB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU;IACzD,MAAM,eAAe,UAAU,OAAO,iBAAiB;IACvD,MAAM,SAAS,eAAe,KAAK;IACnC,MAAM,UAAU,eAAe,KAAK;IACpC,MAAM,SAAS,sBAAsB;WAAI;KAAO,EAAE;IAClD,KAAK,MAAM,aAAa,QAAS;QAC7B,IAAI,uBAAuB,QAAQ,cAAc,CAAC,sBAAsB,WAAW,aAAa;YAC5F,MAAM,yBAAyB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;YAC1F,MAAM,oBAAoB,MAAM,CAAC,uBAAuB;YACxD,MAAM,CAAC,uBAAuB,CAAC,SAAS,GAAG,eAAe,UAAU,SAAS,IAAI,EAAE,EAAE,kBAAkB,SAAS,IAAI,EAAE;QAC1H,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,eAAe,IAAI,EAAE,YAAY;IACtC,MAAM,cAAc,CAAC,GAAG,UAAU,KAAK,EAAE;QACrC,GAAG,IAAI;QACP,aAAa;IACjB;IACA,MAAM,sBAAsB,CAAC,GAAG,UAAU,KAAK,EAAE;QAC7C,GAAG,YAAY;QACf,aAAa;IACjB;IACA,2BAA2B;IAC3B,MAAM,cAAc,IAAI,OAAO,+BAA+B;IAC9D,MAAM,gBAAgB,YAAY,OAAO,CAAC,aAAa,QAAQ,oBAAoB,OAAO,CAAC,aAAa;IACxG,IAAI,CAAC,eAAe;QAChB,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,oBAAoB,2BAA2B,EAAE,aAAa;IACxK;AACJ;AACA,SAAS,eAAe,IAAI,EAAE,YAAY;IACtC,IAAI,cAAc;QACd,eAAe,MAAM;QACrB,OAAO;YACH,GAAG,IAAI;YACP,WAAW;mBACJ,aAAa,SAAS;mBACtB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS;aACnF;QACL;IACJ;IACA,OAAO;AACX;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC9C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,MAAO,SAAS,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/enum-values.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeEnumValues = void 0;\nconst directives_js_1 = require(\"./directives.js\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction mergeEnumValues(first, second, config, directives) {\n    if (config === null || config === void 0 ? void 0 : config.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = (0, directives_js_1.mergeDirectives)(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\nexports.mergeEnumValues = mergeEnumValues;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,KAAK;AAC/B,MAAM;AACN,MAAM;AACN,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACtD,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,MAAM,WAAW,EAAE;QACnB,IAAI,OAAO;YACP,SAAS,IAAI,IAAI;QACrB;QACA,QAAQ;QACR,SAAS;IACb;IACA,MAAM,eAAe,IAAI;IACzB,IAAI,OAAO;QACP,KAAK,MAAM,cAAc,MAAO;YAC5B,aAAa,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE;QAC5C;IACJ;IACA,IAAI,QAAQ;QACR,KAAK,MAAM,eAAe,OAAQ;YAC9B,MAAM,YAAY,YAAY,IAAI,CAAC,KAAK;YACxC,IAAI,aAAa,GAAG,CAAC,YAAY;gBAC7B,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,WAAW,WAAW,GAAG,YAAY,WAAW,IAAI,WAAW,WAAW;gBAC1E,WAAW,UAAU,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,YAAY,UAAU,EAAE,WAAW,UAAU,EAAE;YAChH,OACK;gBACD,aAAa,GAAG,CAAC,WAAW;YAChC;QACJ;IACJ;IACA,MAAM,SAAS;WAAI,aAAa,MAAM;KAAG;IACzC,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/enum.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeEnum = void 0;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst enum_values_js_1 = require(\"./enum-values.js\");\nfunction mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || e1.kind === 'EnumTypeDefinition' || e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: (0, directives_js_1.mergeDirectives)(e1.directives, e2.directives, config, directives),\n            values: (0, enum_values_js_1.mergeEnumValues)(e1.values, e2.values, config),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...e1,\n            kind: graphql_1.Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\nexports.mergeEnum = mergeEnum;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACzC,IAAI,IAAI;QACJ,OAAO;YACH,MAAM,GAAG,IAAI;YACb,aAAa,EAAE,CAAC,cAAc,IAAI,EAAE,CAAC,cAAc;YACnD,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAAK,GAAG,IAAI,KAAK,wBAAwB,GAAG,IAAI,KAAK,uBAC9H,uBACA;YACN,KAAK,GAAG,GAAG;YACX,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,QAAQ;YACvF,QAAQ,CAAC,GAAG,iBAAiB,eAAe,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QACxE;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,EAAE;QACL,MAAM,UAAU,IAAI,CAAC,oBAAoB;IAC7C,IACE;AACV;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/utils.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.defaultStringComparator = exports.CompareVal = exports.printTypeNode = exports.isNonNullTypeNode = exports.isListTypeNode = exports.isWrappingTypeNode = exports.extractType = exports.isSourceTypes = exports.isStringTypes = void 0;\nconst graphql_1 = require(\"graphql\");\nfunction isStringTypes(types) {\n    return typeof types === 'string';\n}\nexports.isStringTypes = isStringTypes;\nfunction isSourceTypes(types) {\n    return types instanceof graphql_1.Source;\n}\nexports.isSourceTypes = isSourceTypes;\nfunction extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === graphql_1.Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nexports.extractType = extractType;\nfunction isWrappingTypeNode(type) {\n    return type.kind !== graphql_1.Kind.NAMED_TYPE;\n}\nexports.isWrappingTypeNode = isWrappingTypeNode;\nfunction isListTypeNode(type) {\n    return type.kind === graphql_1.Kind.LIST_TYPE;\n}\nexports.isListTypeNode = isListTypeNode;\nfunction isNonNullTypeNode(type) {\n    return type.kind === graphql_1.Kind.NON_NULL_TYPE;\n}\nexports.isNonNullTypeNode = isNonNullTypeNode;\nfunction printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nexports.printTypeNode = printTypeNode;\nvar CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal = exports.CompareVal || (exports.CompareVal = {}));\nfunction defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\nexports.defaultStringComparator = defaultStringComparator;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,uBAAuB,GAAG,QAAQ,UAAU,GAAG,QAAQ,aAAa,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,cAAc,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,WAAW,GAAG,QAAQ,aAAa,GAAG,QAAQ,aAAa,GAAG,KAAK;AAC5O,MAAM;AACN,SAAS,cAAc,KAAK;IACxB,OAAO,OAAO,UAAU;AAC5B;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,cAAc,KAAK;IACxB,OAAO,iBAAiB,UAAU,MAAM;AAC5C;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,YAAY,IAAI;IACrB,IAAI,cAAc;IAClB,MAAO,YAAY,IAAI,KAAK,UAAU,IAAI,CAAC,SAAS,IAAI,YAAY,IAAI,KAAK,cAAe;QACxF,cAAc,YAAY,IAAI;IAClC;IACA,OAAO;AACX;AACA,QAAQ,WAAW,GAAG;AACtB,SAAS,mBAAmB,IAAI;IAC5B,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU;AAClD;AACA,QAAQ,kBAAkB,GAAG;AAC7B,SAAS,eAAe,IAAI;IACxB,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,SAAS;AACjD;AACA,QAAQ,cAAc,GAAG;AACzB,SAAS,kBAAkB,IAAI;IAC3B,OAAO,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,aAAa;AACrD;AACA,QAAQ,iBAAiB,GAAG;AAC5B,SAAS,cAAc,IAAI;IACvB,IAAI,eAAe,OAAO;QACtB,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,GAAG,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,KAAK,IAAI,CAAC,KAAK;AAC1B;AACA,QAAQ,aAAa,GAAG;AACxB,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,EAAE,GAAG;AACrD,CAAC,EAAE,aAAa,QAAQ,UAAU,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,CAAC;AAC9D,SAAS,wBAAwB,CAAC,EAAE,CAAC;IACjC,IAAI,KAAK,QAAQ,KAAK,MAAM;QACxB,OAAO,WAAW,UAAU;IAChC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,OAAO,WAAW,UAAU;AAChC;AACA,QAAQ,uBAAuB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/fields.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeFields = void 0;\nconst utils_js_1 = require(\"./utils.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst arguments_js_1 = require(\"./arguments.js\");\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nfunction mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !(config === null || config === void 0 ? void 0 : config.ignoreFieldConflicts)) {\n                const newField = ((config === null || config === void 0 ? void 0 : config.onFieldTypeConflict) && config.onFieldTypeConflict(existing, field, type, config === null || config === void 0 ? void 0 : config.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config === null || config === void 0 ? void 0 : config.throwOnConflict);\n                newField.arguments = (0, arguments_js_1.mergeArguments)(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = (0, directives_js_1.mergeDirectives)(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nexports.mergeFields = mergeFields;\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = (0, utils_js_1.printTypeNode)(a.type);\n    const bType = (0, utils_js_1.printTypeNode)(b.type);\n    if (aType !== bType) {\n        const t1 = (0, utils_js_1.extractType)(a.type);\n        const t2 = (0, utils_js_1.extractType)(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if ((0, utils_js_1.isNonNullTypeNode)(b.type) && !(0, utils_js_1.isNonNullTypeNode)(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!(0, utils_js_1.isWrappingTypeNode)(oldType) && !(0, utils_js_1.isWrappingTypeNode)(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if ((0, utils_js_1.isNonNullTypeNode)(newType)) {\n        const ofType = (0, utils_js_1.isNonNullTypeNode)(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if ((0, utils_js_1.isNonNullTypeNode)(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if ((0, utils_js_1.isListTypeNode)(oldType)) {\n        return (((0, utils_js_1.isListTypeNode)(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            ((0, utils_js_1.isNonNullTypeNode)(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,mBAAmB,SAAS,EAAE,UAAU;IAC7C,MAAM,cAAc,UAAU,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK;IAC3F,OAAO;QAAC,cAAc,CAAC,IAAI,SAAS,CAAC,YAAY,GAAG;QAAM;KAAY;AAC1E;AACA,SAAS,YAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACjD,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO,IAAI,IAAI;IACnB;IACA,IAAI,MAAM,MAAM;QACZ,KAAK,MAAM,SAAS,GAAI;YACpB,MAAM,CAAC,UAAU,cAAc,GAAG,mBAAmB,QAAQ;YAC7D,IAAI,YAAY,CAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,oBAAoB,GAAG;gBAC5F,MAAM,WAAW,AAAC,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,KAAK,OAAO,mBAAmB,CAAC,UAAU,OAAO,MAAM,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe,KACtN,iBAAiB,MAAM,UAAU,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,eAAe;gBAClH,SAAS,SAAS,GAAG,CAAC,GAAG,eAAe,cAAc,EAAE,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE;gBAC/G,SAAS,UAAU,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,MAAM,UAAU,EAAE,SAAS,UAAU,EAAE,QAAQ;gBAC1G,SAAS,WAAW,GAAG,MAAM,WAAW,IAAI,SAAS,WAAW;gBAChE,MAAM,CAAC,cAAc,GAAG;YAC5B,OACK;gBACD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ;IACA,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,IAAI,UAAU,OAAO,UAAU,EAAE;QAC7B,MAAM,aAAa,OAAO,UAAU;QACpC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;IAC/F;IACA,OAAO;AACX;AACA,QAAQ,WAAW,GAAG;AACtB,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,KAAK;IAC3D,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;IAClD,MAAM,QAAQ,CAAC,GAAG,WAAW,aAAa,EAAE,EAAE,IAAI;IAClD,IAAI,UAAU,OAAO;QACjB,MAAM,KAAK,CAAC,GAAG,WAAW,WAAW,EAAE,EAAE,IAAI;QAC7C,MAAM,KAAK,CAAC,GAAG,WAAW,WAAW,EAAE,EAAE,IAAI;QAC7C,IAAI,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtK;QACA,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB;YAC7D,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3G;IACJ;IACA,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI,KAAK,CAAC,CAAC,GAAG,WAAW,iBAAiB,EAAE,EAAE,IAAI,GAAG;QACzF,EAAE,IAAI,GAAG,EAAE,IAAI;IACnB;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB,KAAK;IACvE,iBAAiB;IACjB,IAAI,CAAC,CAAC,GAAG,WAAW,kBAAkB,EAAE,YAAY,CAAC,CAAC,GAAG,WAAW,kBAAkB,EAAE,UAAU;QAC9F,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;IAClD;IACA,kBAAkB;IAClB,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,UAAU;QAC5C,MAAM,SAAS,CAAC,GAAG,WAAW,iBAAiB,EAAE,WAAW,QAAQ,IAAI,GAAG;QAC3E,OAAO,uBAAuB,QAAQ,QAAQ,IAAI;IACtD;IACA,kBAAkB;IAClB,IAAI,CAAC,GAAG,WAAW,iBAAiB,EAAE,UAAU;QAC5C,OAAO,uBAAuB,SAAS,SAAS;IACpD;IACA,cAAc;IACd,IAAI,CAAC,GAAG,WAAW,cAAc,EAAE,UAAU;QACzC,OAAQ,AAAC,CAAC,GAAG,WAAW,cAAc,EAAE,YAAY,uBAAuB,QAAQ,IAAI,EAAE,QAAQ,IAAI,KAChG,CAAC,GAAG,WAAW,iBAAiB,EAAE,YAAY,uBAAuB,SAAS,OAAO,CAAC,OAAO;IACtG;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/input-type.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeInputType = void 0;\nconst graphql_1 = require(\"graphql\");\nconst fields_js_1 = require(\"./fields.js\");\nconst directives_js_1 = require(\"./directives.js\");\nfunction mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\nexports.mergeInputType = mergeInputType;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC1D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,+BACd,aAAa,IAAI,KAAK,8BACpB,8BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC7E,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YACvG;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC3F;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,4BAA4B;IACrD,IACE;AACV;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1440, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-named-type-array.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeNamedTypeArray = void 0;\nconst utils_1 = require(\"@graphql-tools/utils\");\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nfunction mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(utils_1.compareNodes);\n    }\n    return result;\n}\nexports.mergeNamedTypeArray = mergeNamedTypeArray;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,mBAAmB,GAAG,KAAK;AACnC,MAAM;AACN,SAAS,cAAc,GAAG,EAAE,KAAK;IAC7B,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK;AAC5D;AACA,SAAS,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;IAC7D,MAAM,SAAS;WAAI;WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,cAAc,QAAQ;KAAI;IAC3E,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,QAAQ,YAAY;IACpC;IACA,OAAO;AACX;AACA,QAAQ,mBAAmB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/interface.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeInterface = void 0;\nconst graphql_1 = require(\"graphql\");\nconst fields_js_1 = require(\"./fields.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? (0, merge_named_type_array_js_1.mergeNamedTypeArray)(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: graphql_1.Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\nexports.mergeInterface = mergeInterface;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,cAAc,GAAG,KAAK;AAC9B,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC1D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,6BACd,aAAa,IAAI,KAAK,4BACpB,4BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC7E,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBACnG,YAAY,IAAI,CAAC,aAAa,GACxB,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,EAAE,UACrG;YACV;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC1F;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,yBAAyB;IAClD,IACE;AACV;AACA,QAAQ,cAAc,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/type.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeType = void 0;\nconst graphql_1 = require(\"graphql\");\nconst fields_js_1 = require(\"./fields.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: (0, fields_js_1.mergeFields)(node, node.fields, existingNode.fields, config),\n                directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n                interfaces: (0, merge_named_type_array_js_1.mergeNamedTypeArray)(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: graphql_1.Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\nexports.mergeType = mergeType;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,SAAS,GAAG,KAAK;AACzB,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACrD,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAC,GAAG,YAAY,WAAW,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC7E,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBACnG,YAAY,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE;YAC/G;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QACrF;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,sBAAsB;IAC/C,IACE;AACV;AACA,QAAQ,SAAS,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/scalar.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeScalar = void 0;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nfunction mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: graphql_1.Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\nexports.mergeScalar = mergeScalar;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,WAAW,GAAG,KAAK;AAC3B,MAAM;AACN,MAAM;AACN,SAAS,YAAY,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACvD,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI;YACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAC3E,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;YACN,KAAK,KAAK,GAAG;YACb,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;QACvG;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,sBAAsB;IAC/C,IACE;AACV;AACA,QAAQ,WAAW,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/union.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeUnion = void 0;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nconst merge_named_type_array_js_1 = require(\"./merge-named-type-array.js\");\nfunction mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: (0, directives_js_1.mergeDirectives)(first.directives, second.directives, config, directives),\n            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || first.kind === 'UnionTypeDefinition' || second.kind === 'UnionTypeDefinition'\n                ? graphql_1.Kind.UNION_TYPE_DEFINITION\n                : graphql_1.Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: (0, merge_named_type_array_js_1.mergeNamedTypeArray)(first.types, second.types, config),\n        };\n    }\n    return (config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...first,\n            kind: graphql_1.Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\nexports.mergeUnion = mergeUnion;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,UAAU,GAAG,KAAK;AAC1B,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACjD,IAAI,QAAQ;QACR,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,aAAa,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc;YAC1D,0HAA0H;YAC1H,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;YAC9F,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,KAAK,MAAM,IAAI,KAAK,yBAAyB,OAAO,IAAI,KAAK,wBACtI,UAAU,IAAI,CAAC,qBAAqB,GACpC,UAAU,IAAI,CAAC,oBAAoB;YACzC,KAAK,MAAM,GAAG;YACd,OAAO,CAAC,GAAG,4BAA4B,mBAAmB,EAAE,MAAM,KAAK,EAAE,OAAO,KAAK,EAAE;QAC3F;IACJ;IACA,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC1E;QACE,GAAG,KAAK;QACR,MAAM,UAAU,IAAI,CAAC,qBAAqB;IAC9C,IACE;AACV;AACA,QAAQ,UAAU,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/schema-def.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeSchemaDefs = exports.DEFAULT_OPERATION_TYPE_NAME_MAP = void 0;\nconst graphql_1 = require(\"graphql\");\nconst directives_js_1 = require(\"./directives.js\");\nexports.DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in exports.DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) || existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nfunction mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === graphql_1.Kind.SCHEMA_DEFINITION || existingNode.kind === graphql_1.Kind.SCHEMA_DEFINITION\n                ? graphql_1.Kind.SCHEMA_DEFINITION\n                : graphql_1.Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: (0, directives_js_1.mergeDirectives)(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return ((config === null || config === void 0 ? void 0 : config.convertExtensions)\n        ? {\n            ...node,\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\nexports.mergeSchemaDefs = mergeSchemaDefs;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,QAAQ,+BAA+B,GAAG,KAAK;AACzE,MAAM;AACN,MAAM;AACN,QAAQ,+BAA+B,GAAG;IACtC,OAAO;IACP,UAAU;IACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,EAAE,EAAE,qBAAqB,EAAE;IACjE,MAAM,kBAAkB,EAAE;IAC1B,IAAK,MAAM,cAAc,QAAQ,+BAA+B,CAAE;QAC9D,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,eAAe,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChH,IAAI,QAAQ;YACR,gBAAgB,IAAI,CAAC;QACzB;IACJ;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC3D,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,GACxG,UAAU,IAAI,CAAC,iBAAiB,GAChC,UAAU,IAAI,CAAC,gBAAgB;YACrC,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,YAAY,CAAC,GAAG,gBAAgB,eAAe,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YACnG,gBAAgB,oBAAoB,KAAK,cAAc,EAAE,aAAa,cAAc;QACxF;IACJ;IACA,OAAQ,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB,IAC3E;QACE,GAAG,IAAI;QACP,MAAM,UAAU,IAAI,CAAC,iBAAiB;IAC1C,IACE;AACV;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1638, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-nodes.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeGraphQLNodes = exports.isNamedDefinitionNode = exports.schemaDefSymbol = void 0;\nconst graphql_1 = require(\"graphql\");\nconst type_js_1 = require(\"./type.js\");\nconst enum_js_1 = require(\"./enum.js\");\nconst scalar_js_1 = require(\"./scalar.js\");\nconst union_js_1 = require(\"./union.js\");\nconst input_type_js_1 = require(\"./input-type.js\");\nconst interface_js_1 = require(\"./interface.js\");\nconst directives_js_1 = require(\"./directives.js\");\nconst schema_def_js_1 = require(\"./schema-def.js\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nexports.schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nfunction isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nexports.isNamedDefinitionNode = isNamedDefinitionNode;\nfunction mergeGraphQLNodes(nodes, config, directives = {}) {\n    var _a, _b, _c;\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = (_a = nodeDefinition.name) === null || _a === void 0 ? void 0 : _a.value;\n            if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {\n                (0, utils_1.collectComment)(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (((_b = config === null || config === void 0 ? void 0 : config.exclusions) === null || _b === void 0 ? void 0 : _b.includes(name + '.*')) || ((_c = config === null || config === void 0 ? void 0 : config.exclusions) === null || _c === void 0 ? void 0 : _c.includes(name))) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case graphql_1.Kind.OBJECT_TYPE_DEFINITION:\n                    case graphql_1.Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, type_js_1.mergeType)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.ENUM_TYPE_DEFINITION:\n                    case graphql_1.Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, enum_js_1.mergeEnum)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.UNION_TYPE_DEFINITION:\n                    case graphql_1.Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, union_js_1.mergeUnion)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.SCALAR_TYPE_DEFINITION:\n                    case graphql_1.Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, scalar_js_1.mergeScalar)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case graphql_1.Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, input_type_js_1.mergeInputType)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.INTERFACE_TYPE_DEFINITION:\n                    case graphql_1.Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = (0, interface_js_1.mergeInterface)(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case graphql_1.Kind.DIRECTIVE_DEFINITION:\n                        mergedResultMap[name] = (0, directives_js_1.mergeDirective)(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === graphql_1.Kind.SCHEMA_DEFINITION || nodeDefinition.kind === graphql_1.Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[exports.schemaDefSymbol] = (0, schema_def_js_1.mergeSchemaDefs)(nodeDefinition, mergedResultMap[exports.schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\nexports.mergeGraphQLNodes = mergeGraphQLNodes;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,eAAe,GAAG,KAAK;AAC3F,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,QAAQ,eAAe,GAAG;AAC1B,SAAS,sBAAsB,cAAc;IACzC,OAAO,UAAU;AACrB;AACA,QAAQ,qBAAqB,GAAG;AAChC,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACrD,IAAI,IAAI,IAAI;IACZ,MAAM,kBAAkB;IACxB,KAAK,MAAM,kBAAkB,MAAO;QAChC,IAAI,sBAAsB,iBAAiB;YACvC,MAAM,OAAO,CAAC,KAAK,eAAe,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;YACrF,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;gBAC5E,CAAC,GAAG,QAAQ,cAAc,EAAE;YAChC;YACA,IAAI,QAAQ,MAAM;gBACd;YACJ;YACA,IAAI,CAAC,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,CAAC,KAAK,GAAG;gBAC/Q,OAAO,eAAe,CAAC,KAAK;YAChC,OACK;gBACD,OAAQ,eAAe,IAAI;oBACvB,KAAK,UAAU,IAAI,CAAC,sBAAsB;oBAC1C,KAAK,UAAU,IAAI,CAAC,qBAAqB;wBACrC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,SAAS,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAChG;oBACJ,KAAK,UAAU,IAAI,CAAC,oBAAoB;oBACxC,KAAK,UAAU,IAAI,CAAC,mBAAmB;wBACnC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,UAAU,SAAS,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAChG;oBACJ,KAAK,UAAU,IAAI,CAAC,qBAAqB;oBACzC,KAAK,UAAU,IAAI,CAAC,oBAAoB;wBACpC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAClG;oBACJ,KAAK,UAAU,IAAI,CAAC,sBAAsB;oBAC1C,KAAK,UAAU,IAAI,CAAC,qBAAqB;wBACrC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,WAAW,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACpG;oBACJ,KAAK,UAAU,IAAI,CAAC,4BAA4B;oBAChD,KAAK,UAAU,IAAI,CAAC,2BAA2B;wBAC3C,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAC3G;oBACJ,KAAK,UAAU,IAAI,CAAC,yBAAyB;oBAC7C,KAAK,UAAU,IAAI,CAAC,wBAAwB;wBACxC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,eAAe,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAC1G;oBACJ,KAAK,UAAU,IAAI,CAAC,oBAAoB;wBACpC,eAAe,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,cAAc,EAAE,gBAAgB,eAAe,CAAC,KAAK;wBACjG;gBACR;YACJ;QACJ,OACK,IAAI,eAAe,IAAI,KAAK,UAAU,IAAI,CAAC,iBAAiB,IAAI,eAAe,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,EAAE;YAC1H,eAAe,CAAC,QAAQ,eAAe,CAAC,GAAG,CAAC,GAAG,gBAAgB,eAAe,EAAE,gBAAgB,eAAe,CAAC,QAAQ,eAAe,CAAC,EAAE;QAC9I;IACJ;IACA,OAAO;AACX;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/merge-typedefs.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mergeGraphQLTypes = exports.mergeTypeDefs = void 0;\nconst graphql_1 = require(\"graphql\");\nconst utils_js_1 = require(\"./utils.js\");\nconst merge_nodes_js_1 = require(\"./merge-nodes.js\");\nconst utils_1 = require(\"@graphql-tools/utils\");\nconst schema_def_js_1 = require(\"./schema-def.js\");\nfunction mergeTypeDefs(typeSource, config) {\n    (0, utils_1.resetComments)();\n    const doc = {\n        kind: graphql_1.Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {\n        result = (0, utils_1.printWithComments)(doc);\n    }\n    else {\n        result = doc;\n    }\n    (0, utils_1.resetComments)();\n    return result;\n}\nexports.mergeTypeDefs = mergeTypeDefs;\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);\n            }\n        }\n        else if ((0, graphql_1.isSchema)(typeSource)) {\n            const documentNode = (0, utils_1.getDocumentNodeFromSchema)(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if ((0, utils_js_1.isStringTypes)(typeSource) || (0, utils_js_1.isSourceTypes)(typeSource)) {\n            const documentNode = (0, graphql_1.parse)(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (typeof typeSource === 'object' && (0, graphql_1.isDefinitionNode)(typeSource)) {\n            if (typeSource.kind === graphql_1.Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if ((0, utils_1.isDocumentNode)(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes };\n}\nfunction mergeGraphQLTypes(typeSource, config) {\n    var _a, _b, _c;\n    (0, utils_1.resetComments)();\n    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);\n    const mergedDirectives = (0, merge_nodes_js_1.mergeGraphQLNodes)(allDirectives, config);\n    const mergedNodes = (0, merge_nodes_js_1.mergeGraphQLNodes)(allNodes, config, mergedDirectives);\n    if (config === null || config === void 0 ? void 0 : config.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[merge_nodes_js_1.schemaDefSymbol] || {\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in schema_def_js_1.DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = schema_def_js_1.DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: graphql_1.Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (((_a = schemaDef === null || schemaDef === void 0 ? void 0 : schemaDef.operationTypes) === null || _a === void 0 ? void 0 : _a.length) != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[merge_nodes_js_1.schemaDefSymbol] = schemaDef;\n        }\n    }\n    if ((config === null || config === void 0 ? void 0 : config.forceSchemaDefinition) && !((_c = (_b = mergedNodes[merge_nodes_js_1.schemaDefSymbol]) === null || _b === void 0 ? void 0 : _b.operationTypes) === null || _c === void 0 ? void 0 : _c.length)) {\n        mergedNodes[merge_nodes_js_1.schemaDefSymbol] = {\n            kind: graphql_1.Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: graphql_1.Kind.NAMED_TYPE,\n                        name: {\n                            kind: graphql_1.Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config === null || config === void 0 ? void 0 : config.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : utils_js_1.defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => { var _a, _b; return sortFn((_a = a.name) === null || _a === void 0 ? void 0 : _a.value, (_b = b.name) === null || _b === void 0 ? void 0 : _b.value); });\n    }\n    return mergedNodeDefinitions;\n}\nexports.mergeGraphQLTypes = mergeGraphQLTypes;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,iBAAiB,GAAG,QAAQ,aAAa,GAAG,KAAK;AACzD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,SAAS,cAAc,UAAU,EAAE,MAAM;IACrC,CAAC,GAAG,QAAQ,aAAa;IACzB,MAAM,MAAM;QACR,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,aAAa,kBAAkB,YAAY;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,iBAAiB;YACjB,qBAAqB;YACrB,GAAG,MAAM;QACb;IACJ;IACA,IAAI;IACJ,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,SAAS,CAAC,GAAG,QAAQ,iBAAiB,EAAE;IAC5C,OACK;QACD,SAAS;IACb;IACA,CAAC,GAAG,QAAQ,aAAa;IACzB,OAAO;AACX;AACA,QAAQ,aAAa,GAAG;AACxB,SAAS,iBAAiB,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,WAAW,EAAE,EAAE,qBAAqB,IAAI,KAAK;IAC5G,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,aAAa;QACnD,mBAAmB,GAAG,CAAC;QACvB,IAAI,OAAO,eAAe,YAAY;YAClC,iBAAiB,cAAc,SAAS,eAAe,UAAU;QACrE,OACK,IAAI,MAAM,OAAO,CAAC,aAAa;YAChC,KAAK,MAAM,QAAQ,WAAY;gBAC3B,iBAAiB,MAAM,SAAS,eAAe,UAAU;YAC7D;QACJ,OACK,IAAI,CAAC,GAAG,UAAU,QAAQ,EAAE,aAAa;YAC1C,MAAM,eAAe,CAAC,GAAG,QAAQ,yBAAyB,EAAE,YAAY;YACxE,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,CAAC,GAAG,WAAW,aAAa,EAAE,eAAe,CAAC,GAAG,WAAW,aAAa,EAAE,aAAa;YAC7F,MAAM,eAAe,CAAC,GAAG,UAAU,KAAK,EAAE,YAAY;YACtD,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,OAAO,eAAe,YAAY,CAAC,GAAG,UAAU,gBAAgB,EAAE,aAAa;YACpF,IAAI,WAAW,IAAI,KAAK,UAAU,IAAI,CAAC,oBAAoB,EAAE;gBACzD,cAAc,IAAI,CAAC;YACvB,OACK;gBACD,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,CAAC,GAAG,QAAQ,cAAc,EAAE,aAAa;YAC9C,iBAAiB,WAAW,WAAW,EAAE,SAAS,eAAe,UAAU;QAC/E,OACK;YACD,MAAM,IAAI,MAAM,CAAC,0EAA0E,EAAE,OAAO,YAAY;QACpH;IACJ;IACA,OAAO;QAAE;QAAe;IAAS;AACrC;AACA,SAAS,kBAAkB,UAAU,EAAE,MAAM;IACzC,IAAI,IAAI,IAAI;IACZ,CAAC,GAAG,QAAQ,aAAa;IACzB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,iBAAiB,YAAY;IACjE,MAAM,mBAAmB,CAAC,GAAG,iBAAiB,iBAAiB,EAAE,eAAe;IAChF,MAAM,cAAc,CAAC,GAAG,iBAAiB,iBAAiB,EAAE,UAAU,QAAQ;IAC9E,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,mBAAmB,EAAE;QAC5E,6DAA6D;QAC7D,MAAM,YAAY,WAAW,CAAC,iBAAiB,eAAe,CAAC,IAAI;YAC/D,MAAM,UAAU,IAAI,CAAC,iBAAiB;YACtC,gBAAgB,EAAE;QACtB;QACA,MAAM,iBAAiB,UAAU,cAAc;QAC/C,IAAK,MAAM,qBAAqB,gBAAgB,+BAA+B,CAAE;YAC7E,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,gBAAiB,cAAc,SAAS,KAAK;YACvF,IAAI,CAAC,eAAe;gBAChB,MAAM,uBAAuB,gBAAgB,+BAA+B,CAAC,kBAAkB;gBAC/F,MAAM,2BAA2B,WAAW,CAAC,qBAAqB;gBAClE,IAAI,4BAA4B,QAAQ,yBAAyB,IAAI,IAAI,MAAM;oBAC3E,eAAe,IAAI,CAAC;wBAChB,MAAM,UAAU,IAAI,CAAC,yBAAyB;wBAC9C,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,UAAU;4BAC/B,MAAM,yBAAyB,IAAI;wBACvC;wBACA,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,CAAC,KAAK,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,UAAU,cAAc,CAAC,MAAM,GAAG,GAAG;YACvL,WAAW,CAAC,iBAAiB,eAAe,CAAC,GAAG;QACpD;IACJ;IACA,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,qBAAqB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC,iBAAiB,eAAe,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG;QACxP,WAAW,CAAC,iBAAiB,eAAe,CAAC,GAAG;YAC5C,MAAM,UAAU,IAAI,CAAC,iBAAiB;YACtC,gBAAgB;gBACZ;oBACI,MAAM,UAAU,IAAI,CAAC,yBAAyB;oBAC9C,WAAW;oBACX,MAAM;wBACF,MAAM,UAAU,IAAI,CAAC,UAAU;wBAC/B,MAAM;4BACF,MAAM,UAAU,IAAI,CAAC,IAAI;4BACzB,OAAO;wBACX;oBACJ;gBACJ;aACH;QACL;IACJ;IACA,MAAM,wBAAwB,OAAO,MAAM,CAAC;IAC5C,IAAI,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,EAAE;QAC7D,MAAM,SAAS,OAAO,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG,WAAW,uBAAuB;QACnG,sBAAsB,IAAI,CAAC,CAAC,GAAG;YAAQ,IAAI,IAAI;YAAI,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QAAG;IAChM;IACA,OAAO;AACX;AACA,QAAQ,iBAAiB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/typedefs-mergers/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./arguments.js\"), exports);\ntslib_1.__exportStar(require(\"./directives.js\"), exports);\ntslib_1.__exportStar(require(\"./enum-values.js\"), exports);\ntslib_1.__exportStar(require(\"./enum.js\"), exports);\ntslib_1.__exportStar(require(\"./fields.js\"), exports);\ntslib_1.__exportStar(require(\"./input-type.js\"), exports);\ntslib_1.__exportStar(require(\"./interface.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-named-type-array.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-nodes.js\"), exports);\ntslib_1.__exportStar(require(\"./merge-typedefs.js\"), exports);\ntslib_1.__exportStar(require(\"./scalar.js\"), exports);\ntslib_1.__exportStar(require(\"./type.js\"), exports);\ntslib_1.__exportStar(require(\"./union.js\"), exports);\ntslib_1.__exportStar(require(\"./utils.js\"), exports);\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,QAAQ,YAAY,gKAA4B;AAChD,QAAQ,YAAY,iKAA6B;AACjD,QAAQ,YAAY,kKAA8B;AAClD,QAAQ,YAAY,2JAAuB;AAC3C,QAAQ,YAAY,6JAAyB;AAC7C,QAAQ,YAAY,iKAA6B;AACjD,QAAQ,YAAY,gKAA4B;AAChD,QAAQ,YAAY,6KAAyC;AAC7D,QAAQ,YAAY,kKAA8B;AAClD,QAAQ,YAAY,qKAAiC;AACrD,QAAQ,YAAY,6JAAyB;AAC7C,QAAQ,YAAY,2JAAuB;AAC3C,QAAQ,YAAY,4JAAwB;AAC5C,QAAQ,YAAY,4JAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/extensions.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.applyExtensions = exports.mergeExtensions = exports.extractExtensionsFromSchema = void 0;\nconst utils_1 = require(\"@graphql-tools/utils\");\nvar utils_2 = require(\"@graphql-tools/utils\");\nObject.defineProperty(exports, \"extractExtensionsFromSchema\", { enumerable: true, get: function () { return utils_2.extractExtensionsFromSchema; } });\nfunction mergeExtensions(extensions) {\n    return (0, utils_1.mergeDeep)(extensions);\n}\nexports.mergeExtensions = mergeExtensions;\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj) {\n        return;\n    }\n    obj.extensions = (0, utils_1.mergeDeep)([obj.extensions || {}, extensions || {}]);\n}\nfunction applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\nexports.applyExtensions = applyExtensions;\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,QAAQ,eAAe,GAAG,QAAQ,eAAe,GAAG,QAAQ,2BAA2B,GAAG,KAAK;AAC/F,MAAM;AACN,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,+BAA+B;IAAE,YAAY;IAAM,KAAK;QAAc,OAAO,QAAQ,2BAA2B;IAAE;AAAE;AACnJ,SAAS,gBAAgB,UAAU;IAC/B,OAAO,CAAC,GAAG,QAAQ,SAAS,EAAE;AAClC;AACA,QAAQ,eAAe,GAAG;AAC1B,SAAS,qBAAqB,GAAG,EAAE,UAAU;IACzC,IAAI,CAAC,KAAK;QACN;IACJ;IACA,IAAI,UAAU,GAAG,CAAC,GAAG,QAAQ,SAAS,EAAE;QAAC,IAAI,UAAU,IAAI,CAAC;QAAG,cAAc,CAAC;KAAE;AACpF;AACA,SAAS,gBAAgB,MAAM,EAAE,UAAU;IACvC,qBAAqB,QAAQ,WAAW,gBAAgB;IACxD,KAAK,MAAM,CAAC,UAAU,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,GAAI;QACnE,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,qBAAqB,MAAM,KAAK,UAAU;YAC1C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,aAAa;gBACrD,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,IAAI,OAAO;wBACP,qBAAqB,OAAO,UAAU,UAAU;wBAChD,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAU,SAAS,EAAG;4BAC9D,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM;wBAC/D;oBACJ;gBACJ;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,SAAS;gBAC5B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,qBAAqB,OAAO,UAAU,UAAU;gBACpD;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC3B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,QAAQ,CAAC;oBAC5B,qBAAqB,OAAO;gBAChC;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,QAAQ,eAAe,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40apollo/server/node_modules/%40graphql-tools/merge/cjs/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst tslib_1 = require(\"tslib\");\ntslib_1.__exportStar(require(\"./merge-resolvers.js\"), exports);\ntslib_1.__exportStar(require(\"./typedefs-mergers/index.js\"), exports);\ntslib_1.__exportStar(require(\"./extensions.js\"), exports);\n"], "names": [], "mappings": "AAAA;AACA,OAAO,cAAc,CAAC,SAAS,cAAc;IAAE,OAAO;AAAK;AAC3D,MAAM;AACN,QAAQ,YAAY,qJAAkC;AACtD,QAAQ,YAAY,4JAAyC;AAC7D,QAAQ,YAAY,gJAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1948, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/extensions.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\nexport { extractExtensionsFromSchema } from '@graphql-tools/utils';\nexport function mergeExtensions(extensions) {\n    return mergeDeep(extensions, false, true);\n}\nfunction applyExtensionObject(obj, extensions) {\n    if (!obj || !extensions || extensions === obj.extensions) {\n        return;\n    }\n    if (!obj.extensions) {\n        obj.extensions = extensions;\n        return;\n    }\n    obj.extensions = mergeDeep([obj.extensions, extensions], false, true);\n}\nexport function applyExtensions(schema, extensions) {\n    applyExtensionObject(schema, extensions.schemaExtensions);\n    for (const [typeName, data] of Object.entries(extensions.types || {})) {\n        const type = schema.getType(typeName);\n        if (type) {\n            applyExtensionObject(type, data.extensions);\n            if (data.type === 'object' || data.type === 'interface') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    if (field) {\n                        applyExtensionObject(field, fieldData.extensions);\n                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {\n                            applyExtensionObject(field.args.find(a => a.name === arg), argData);\n                        }\n                    }\n                }\n            }\n            else if (data.type === 'input') {\n                for (const [fieldName, fieldData] of Object.entries(data.fields)) {\n                    const field = type.getFields()[fieldName];\n                    applyExtensionObject(field, fieldData.extensions);\n                }\n            }\n            else if (data.type === 'enum') {\n                for (const [valueName, valueData] of Object.entries(data.values)) {\n                    const value = type.getValue(valueName);\n                    applyExtensionObject(value, valueData);\n                }\n            }\n        }\n    }\n    return schema;\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS,gBAAgB,UAAU;IACtC,OAAO,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,YAAY,OAAO;AACxC;AACA,SAAS,qBAAqB,GAAG,EAAE,UAAU;IACzC,IAAI,CAAC,OAAO,CAAC,cAAc,eAAe,IAAI,UAAU,EAAE;QACtD;IACJ;IACA,IAAI,CAAC,IAAI,UAAU,EAAE;QACjB,IAAI,UAAU,GAAG;QACjB;IACJ;IACA,IAAI,UAAU,GAAG,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE;QAAC,IAAI,UAAU;QAAE;KAAW,EAAE,OAAO;AACpE;AACO,SAAS,gBAAgB,MAAM,EAAE,UAAU;IAC9C,qBAAqB,QAAQ,WAAW,gBAAgB;IACxD,KAAK,MAAM,CAAC,UAAU,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,GAAI;QACnE,MAAM,OAAO,OAAO,OAAO,CAAC;QAC5B,IAAI,MAAM;YACN,qBAAqB,MAAM,KAAK,UAAU;YAC1C,IAAI,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,aAAa;gBACrD,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,IAAI,OAAO;wBACP,qBAAqB,OAAO,UAAU,UAAU;wBAChD,KAAK,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,UAAU,SAAS,EAAG;4BAC9D,qBAAqB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM;wBAC/D;oBACJ;gBACJ;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,SAAS;gBAC5B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,SAAS,EAAE,CAAC,UAAU;oBACzC,qBAAqB,OAAO,UAAU,UAAU;gBACpD;YACJ,OACK,IAAI,KAAK,IAAI,KAAK,QAAQ;gBAC3B,KAAK,MAAM,CAAC,WAAW,UAAU,IAAI,OAAO,OAAO,CAAC,KAAK,MAAM,EAAG;oBAC9D,MAAM,QAAQ,KAAK,QAAQ,CAAC;oBAC5B,qBAAqB,OAAO;gBAChC;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2008, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/merge-resolvers.js"], "sourcesContent": ["import { mergeDeep } from '@graphql-tools/utils';\n/**\n * Deep merges multiple resolver definition objects into a single definition.\n * @param resolversDefinitions Resolver definitions to be merged\n * @param options Additional options\n *\n * ```js\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const clientResolver = require('./clientResolver');\n * const productResolver = require('./productResolver');\n *\n * const resolvers = mergeResolvers([\n *  clientResolver,\n *  productResolver,\n * ]);\n * ```\n *\n * If you don't want to manually create the array of resolver objects, you can\n * also use this function along with loadFiles:\n *\n * ```js\n * const path = require('path');\n * const { mergeResolvers } = require('@graphql-tools/merge');\n * const { loadFilesSync } = require('@graphql-tools/load-files');\n *\n * const resolversArray = loadFilesSync(path.join(__dirname, './resolvers'));\n *\n * const resolvers = mergeResolvers(resolversArray)\n * ```\n */\nexport function mergeResolvers(resolversDefinitions, options) {\n    if (!resolversDefinitions ||\n        (Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0)) {\n        return {};\n    }\n    if (!Array.isArray(resolversDefinitions)) {\n        return resolversDefinitions;\n    }\n    if (resolversDefinitions.length === 1) {\n        return resolversDefinitions[0] || {};\n    }\n    const resolvers = new Array();\n    for (let resolversDefinition of resolversDefinitions) {\n        if (Array.isArray(resolversDefinition)) {\n            resolversDefinition = mergeResolvers(resolversDefinition);\n        }\n        if (typeof resolversDefinition === 'object' && resolversDefinition) {\n            resolvers.push(resolversDefinition);\n        }\n    }\n    const result = mergeDeep(resolvers, true);\n    if (options?.exclusions) {\n        for (const exclusion of options.exclusions) {\n            const [typeName, fieldName] = exclusion.split('.');\n            if (['__proto__', 'constructor', 'prototype'].includes(typeName) ||\n                ['__proto__', 'constructor', 'prototype'].includes(fieldName)) {\n                continue;\n            }\n            if (!fieldName || fieldName === '*') {\n                delete result[typeName];\n            }\n            else if (result[typeName]) {\n                delete result[typeName][fieldName];\n            }\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AA8BO,SAAS,eAAe,oBAAoB,EAAE,OAAO;IACxD,IAAI,CAAC,wBACA,MAAM,OAAO,CAAC,yBAAyB,qBAAqB,MAAM,KAAK,GAAI;QAC5E,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,uBAAuB;QACtC,OAAO;IACX;IACA,IAAI,qBAAqB,MAAM,KAAK,GAAG;QACnC,OAAO,oBAAoB,CAAC,EAAE,IAAI,CAAC;IACvC;IACA,MAAM,YAAY,IAAI;IACtB,KAAK,IAAI,uBAAuB,qBAAsB;QAClD,IAAI,MAAM,OAAO,CAAC,sBAAsB;YACpC,sBAAsB,eAAe;QACzC;QACA,IAAI,OAAO,wBAAwB,YAAY,qBAAqB;YAChE,UAAU,IAAI,CAAC;QACnB;IACJ;IACA,MAAM,SAAS,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,WAAW;IACpC,IAAI,SAAS,YAAY;QACrB,KAAK,MAAM,aAAa,QAAQ,UAAU,CAAE;YACxC,MAAM,CAAC,UAAU,UAAU,GAAG,UAAU,KAAK,CAAC;YAC9C,IAAI;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,aACnD;gBAAC;gBAAa;gBAAe;aAAY,CAAC,QAAQ,CAAC,YAAY;gBAC/D;YACJ;YACA,IAAI,CAAC,aAAa,cAAc,KAAK;gBACjC,OAAO,MAAM,CAAC,SAAS;YAC3B,OACK,IAAI,MAAM,CAAC,SAAS,EAAE;gBACvB,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU;YACtC;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2062, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/directives.js"], "sourcesContent": ["import { isSome } from '@graphql-tools/utils';\nfunction directiveAlreadyExists(directivesArr, otherDirective) {\n    return !!directivesArr.find(directive => directive.name.value === otherDirective.name.value);\n}\nfunction isRepeatableDirective(directive, directives) {\n    return !!directives?.[directive.name.value]?.repeatable;\n}\nfunction nameAlreadyExists(name, namesArr) {\n    return namesArr.some(({ value }) => value === name.value);\n}\nfunction mergeArguments(a1, a2) {\n    const result = [...a2];\n    for (const argument of a1) {\n        const existingIndex = result.findIndex(a => a.name.value === argument.name.value);\n        if (existingIndex > -1) {\n            const existingArg = result[existingIndex];\n            if (existingArg.value.kind === 'ListValue') {\n                const source = existingArg.value.values;\n                const target = argument.value.values;\n                // merge values of two lists\n                existingArg.value.values = deduplicateLists(source, target, (targetVal, source) => {\n                    const value = targetVal.value;\n                    return !value || !source.some((sourceVal) => sourceVal.value === value);\n                });\n            }\n            else {\n                existingArg.value = argument.value;\n            }\n        }\n        else {\n            result.push(argument);\n        }\n    }\n    return result;\n}\nfunction deduplicateDirectives(directives, definitions) {\n    return directives\n        .map((directive, i, all) => {\n        const firstAt = all.findIndex(d => d.name.value === directive.name.value);\n        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {\n            const dup = all[firstAt];\n            directive.arguments = mergeArguments(directive.arguments, dup.arguments);\n            return null;\n        }\n        return directive;\n    })\n        .filter(isSome);\n}\nexport function mergeDirectives(d1 = [], d2 = [], config, directives) {\n    const reverseOrder = config && config.reverseDirectives;\n    const asNext = reverseOrder ? d1 : d2;\n    const asFirst = reverseOrder ? d2 : d1;\n    const result = deduplicateDirectives([...asNext], directives);\n    for (const directive of asFirst) {\n        if (directiveAlreadyExists(result, directive) &&\n            !isRepeatableDirective(directive, directives)) {\n            const existingDirectiveIndex = result.findIndex(d => d.name.value === directive.name.value);\n            const existingDirective = result[existingDirectiveIndex];\n            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);\n        }\n        else {\n            result.push(directive);\n        }\n    }\n    return result;\n}\nexport function mergeDirective(node, existingNode) {\n    if (existingNode) {\n        return {\n            ...node,\n            arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs) => !nameAlreadyExists(arg.name, existingArgs.map(a => a.name))),\n            locations: [\n                ...existingNode.locations,\n                ...node.locations.filter(name => !nameAlreadyExists(name, existingNode.locations)),\n            ],\n        };\n    }\n    return node;\n}\nfunction deduplicateLists(source, target, filterFn) {\n    return source.concat(target.filter(val => filterFn(val, source)));\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,uBAAuB,aAAa,EAAE,cAAc;IACzD,OAAO,CAAC,CAAC,cAAc,IAAI,CAAC,CAAA,YAAa,UAAU,IAAI,CAAC,KAAK,KAAK,eAAe,IAAI,CAAC,KAAK;AAC/F;AACA,SAAS,sBAAsB,SAAS,EAAE,UAAU;IAChD,OAAO,CAAC,CAAC,YAAY,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE;AACjD;AACA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,OAAO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAAK,UAAU,KAAK,KAAK;AAC5D;AACA,SAAS,eAAe,EAAE,EAAE,EAAE;IAC1B,MAAM,SAAS;WAAI;KAAG;IACtB,KAAK,MAAM,YAAY,GAAI;QACvB,MAAM,gBAAgB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,KAAK;QAChF,IAAI,gBAAgB,CAAC,GAAG;YACpB,MAAM,cAAc,MAAM,CAAC,cAAc;YACzC,IAAI,YAAY,KAAK,CAAC,IAAI,KAAK,aAAa;gBACxC,MAAM,SAAS,YAAY,KAAK,CAAC,MAAM;gBACvC,MAAM,SAAS,SAAS,KAAK,CAAC,MAAM;gBACpC,4BAA4B;gBAC5B,YAAY,KAAK,CAAC,MAAM,GAAG,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;oBACpE,MAAM,QAAQ,UAAU,KAAK;oBAC7B,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,CAAC,YAAc,UAAU,KAAK,KAAK;gBACrE;YACJ,OACK;gBACD,YAAY,KAAK,GAAG,SAAS,KAAK;YACtC;QACJ,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACA,SAAS,sBAAsB,UAAU,EAAE,WAAW;IAClD,OAAO,WACF,GAAG,CAAC,CAAC,WAAW,GAAG;QACpB,MAAM,UAAU,IAAI,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;QACxE,IAAI,YAAY,KAAK,CAAC,sBAAsB,WAAW,cAAc;YACjE,MAAM,MAAM,GAAG,CAAC,QAAQ;YACxB,UAAU,SAAS,GAAG,eAAe,UAAU,SAAS,EAAE,IAAI,SAAS;YACvE,OAAO;QACX;QACA,OAAO;IACX,GACK,MAAM,CAAC,+JAAA,CAAA,SAAM;AACtB;AACO,SAAS,gBAAgB,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU;IAChE,MAAM,eAAe,UAAU,OAAO,iBAAiB;IACvD,MAAM,SAAS,eAAe,KAAK;IACnC,MAAM,UAAU,eAAe,KAAK;IACpC,MAAM,SAAS,sBAAsB;WAAI;KAAO,EAAE;IAClD,KAAK,MAAM,aAAa,QAAS;QAC7B,IAAI,uBAAuB,QAAQ,cAC/B,CAAC,sBAAsB,WAAW,aAAa;YAC/C,MAAM,yBAAyB,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,UAAU,IAAI,CAAC,KAAK;YAC1F,MAAM,oBAAoB,MAAM,CAAC,uBAAuB;YACxD,MAAM,CAAC,uBAAuB,CAAC,SAAS,GAAG,eAAe,UAAU,SAAS,IAAI,EAAE,EAAE,kBAAkB,SAAS,IAAI,EAAE;QAC1H,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;AACX;AACO,SAAS,eAAe,IAAI,EAAE,YAAY;IAC7C,IAAI,cAAc;QACd,OAAO;YACH,GAAG,IAAI;YACP,WAAW,iBAAiB,aAAa,SAAS,IAAI,EAAE,EAAE,KAAK,SAAS,IAAI,EAAE,EAAE,CAAC,KAAK,eAAiB,CAAC,kBAAkB,IAAI,IAAI,EAAE,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAChK,WAAW;mBACJ,aAAa,SAAS;mBACtB,KAAK,SAAS,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,kBAAkB,MAAM,aAAa,SAAS;aACnF;QACL;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,MAAM,EAAE,MAAM,EAAE,QAAQ;IAC9C,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,MAAO,SAAS,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum-values.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nimport { mergeDirectives } from './directives.js';\nexport function mergeEnumValues(first, second, config, directives) {\n    if (config?.consistentEnumMerge) {\n        const reversed = [];\n        if (first) {\n            reversed.push(...first);\n        }\n        first = second;\n        second = reversed;\n    }\n    const enumValueMap = new Map();\n    if (first) {\n        for (const firstValue of first) {\n            enumValueMap.set(firstValue.name.value, firstValue);\n        }\n    }\n    if (second) {\n        for (const secondValue of second) {\n            const enumValue = secondValue.name.value;\n            if (enumValueMap.has(enumValue)) {\n                const firstValue = enumValueMap.get(enumValue);\n                firstValue.description = secondValue.description || firstValue.description;\n                firstValue.directives = mergeDirectives(secondValue.directives, firstValue.directives, directives);\n            }\n            else {\n                enumValueMap.set(enumValue, secondValue);\n            }\n        }\n    }\n    const result = [...enumValueMap.values()];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IAC7D,IAAI,QAAQ,qBAAqB;QAC7B,MAAM,WAAW,EAAE;QACnB,IAAI,OAAO;YACP,SAAS,IAAI,IAAI;QACrB;QACA,QAAQ;QACR,SAAS;IACb;IACA,MAAM,eAAe,IAAI;IACzB,IAAI,OAAO;QACP,KAAK,MAAM,cAAc,MAAO;YAC5B,aAAa,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE;QAC5C;IACJ;IACA,IAAI,QAAQ;QACR,KAAK,MAAM,eAAe,OAAQ;YAC9B,MAAM,YAAY,YAAY,IAAI,CAAC,KAAK;YACxC,IAAI,aAAa,GAAG,CAAC,YAAY;gBAC7B,MAAM,aAAa,aAAa,GAAG,CAAC;gBACpC,WAAW,WAAW,GAAG,YAAY,WAAW,IAAI,WAAW,WAAW;gBAC1E,WAAW,UAAU,GAAG,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,UAAU,EAAE,WAAW,UAAU,EAAE;YAC3F,OACK;gBACD,aAAa,GAAG,CAAC,WAAW;YAChC;QACJ;IACJ;IACA,MAAM,SAAS;WAAI,aAAa,MAAM;KAAG;IACzC,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+JAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/enum.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeEnumValues } from './enum-values.js';\nexport function mergeEnum(e1, e2, config, directives) {\n    if (e2) {\n        return {\n            name: e1.name,\n            description: e1['description'] || e2['description'],\n            kind: config?.convertExtensions ||\n                e1.kind === 'EnumTypeDefinition' ||\n                e2.kind === 'EnumTypeDefinition'\n                ? 'EnumTypeDefinition'\n                : 'EnumTypeExtension',\n            loc: e1.loc,\n            directives: mergeDirectives(e1.directives, e2.directives, config, directives),\n            values: mergeEnumValues(e1.values, e2.values, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...e1,\n            kind: Kind.ENUM_TYPE_DEFINITION,\n        }\n        : e1;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IAChD,IAAI,IAAI;QACJ,OAAO;YACH,MAAM,GAAG,IAAI;YACb,aAAa,EAAE,CAAC,cAAc,IAAI,EAAE,CAAC,cAAc;YACnD,MAAM,QAAQ,qBACV,GAAG,IAAI,KAAK,wBACZ,GAAG,IAAI,KAAK,uBACV,uBACA;YACN,KAAK,GAAG,GAAG;YACX,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,QAAQ;YAClE,QAAQ,CAAA,GAAA,6LAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE;QAClD;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,EAAE;QACL,MAAM,+IAAA,CAAA,OAAI,CAAC,oBAAoB;IACnC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/arguments.js"], "sourcesContent": ["import { compareNodes, isSome } from '@graphql-tools/utils';\nexport function mergeArguments(args1, args2, config) {\n    const result = deduplicateArguments([...args2, ...args1].filter(isSome), config);\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\nfunction deduplicateArguments(args, config) {\n    return args.reduce((acc, current) => {\n        const dupIndex = acc.findIndex(arg => arg.name.value === current.name.value);\n        if (dupIndex === -1) {\n            return acc.concat([current]);\n        }\n        else if (!config?.reverseArguments) {\n            acc[dupIndex] = current;\n        }\n        return acc;\n    }, []);\n}\n"], "names": [], "mappings": ";;;AAAA;;AACO,SAAS,eAAe,KAAK,EAAE,KAAK,EAAE,MAAM;IAC/C,MAAM,SAAS,qBAAqB;WAAI;WAAU;KAAM,CAAC,MAAM,CAAC,+JAAA,CAAA,SAAM,GAAG;IACzE,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+JAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,MAAM;IACtC,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,WAAW,IAAI,SAAS,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;QAC3E,IAAI,aAAa,CAAC,GAAG;YACjB,OAAO,IAAI,MAAM,CAAC;gBAAC;aAAQ;QAC/B,OACK,IAAI,CAAC,QAAQ,kBAAkB;YAChC,GAAG,CAAC,SAAS,GAAG;QACpB;QACA,OAAO;IACX,GAAG,EAAE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2266, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/utils.js"], "sourcesContent": ["import { Kind, Source } from 'graphql';\nexport function isStringTypes(types) {\n    return typeof types === 'string';\n}\nexport function isSourceTypes(types) {\n    return types instanceof Source;\n}\nexport function extractType(type) {\n    let visitedType = type;\n    while (visitedType.kind === Kind.LIST_TYPE || visitedType.kind === 'NonNullType') {\n        visitedType = visitedType.type;\n    }\n    return visitedType;\n}\nexport function isWrappingTypeNode(type) {\n    return type.kind !== Kind.NAMED_TYPE;\n}\nexport function isListTypeNode(type) {\n    return type.kind === Kind.LIST_TYPE;\n}\nexport function isNonNullTypeNode(type) {\n    return type.kind === Kind.NON_NULL_TYPE;\n}\nexport function printTypeNode(type) {\n    if (isListTypeNode(type)) {\n        return `[${printTypeNode(type.type)}]`;\n    }\n    if (isNonNullTypeNode(type)) {\n        return `${printTypeNode(type.type)}!`;\n    }\n    return type.name.value;\n}\nexport var CompareVal;\n(function (CompareVal) {\n    CompareVal[CompareVal[\"A_SMALLER_THAN_B\"] = -1] = \"A_SMALLER_THAN_B\";\n    CompareVal[CompareVal[\"A_EQUALS_B\"] = 0] = \"A_EQUALS_B\";\n    CompareVal[CompareVal[\"A_GREATER_THAN_B\"] = 1] = \"A_GREATER_THAN_B\";\n})(CompareVal || (CompareVal = {}));\nexport function defaultStringComparator(a, b) {\n    if (a == null && b == null) {\n        return CompareVal.A_EQUALS_B;\n    }\n    if (a == null) {\n        return CompareVal.A_SMALLER_THAN_B;\n    }\n    if (b == null) {\n        return CompareVal.A_GREATER_THAN_B;\n    }\n    if (a < b)\n        return CompareVal.A_SMALLER_THAN_B;\n    if (a > b)\n        return CompareVal.A_GREATER_THAN_B;\n    return CompareVal.A_EQUALS_B;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,OAAO,UAAU;AAC5B;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,iBAAiB,gJAAA,CAAA,SAAM;AAClC;AACO,SAAS,YAAY,IAAI;IAC5B,IAAI,cAAc;IAClB,MAAO,YAAY,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,SAAS,IAAI,YAAY,IAAI,KAAK,cAAe;QAC9E,cAAc,YAAY,IAAI;IAClC;IACA,OAAO;AACX;AACO,SAAS,mBAAmB,IAAI;IACnC,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,UAAU;AACxC;AACO,SAAS,eAAe,IAAI;IAC/B,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,SAAS;AACvC;AACO,SAAS,kBAAkB,IAAI;IAClC,OAAO,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,aAAa;AAC3C;AACO,SAAS,cAAc,IAAI;IAC9B,IAAI,eAAe,OAAO;QACtB,OAAO,CAAC,CAAC,EAAE,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IAC1C;IACA,IAAI,kBAAkB,OAAO;QACzB,OAAO,GAAG,cAAc,KAAK,IAAI,EAAE,CAAC,CAAC;IACzC;IACA,OAAO,KAAK,IAAI,CAAC,KAAK;AAC1B;AACO,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE,GAAG;IAClD,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,mBAAmB,GAAG,EAAE,GAAG;AACrD,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAC1B,SAAS,wBAAwB,CAAC,EAAE,CAAC;IACxC,IAAI,KAAK,QAAQ,KAAK,MAAM;QACxB,OAAO,WAAW,UAAU;IAChC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,KAAK,MAAM;QACX,OAAO,WAAW,gBAAgB;IACtC;IACA,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,IAAI,IAAI,GACJ,OAAO,WAAW,gBAAgB;IACtC,OAAO,WAAW,UAAU;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/fields.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nimport { mergeArguments } from './arguments.js';\nimport { mergeDirectives } from './directives.js';\nimport { extractType, isListTypeNode, isNonNullTypeNode, isWrappingTypeNode, printTypeNode, } from './utils.js';\nfunction fieldAlreadyExists(fieldsArr, otherField) {\n    const resultIndex = fieldsArr.findIndex(field => field.name.value === otherField.name.value);\n    return [resultIndex > -1 ? fieldsArr[resultIndex] : null, resultIndex];\n}\nexport function mergeFields(type, f1, f2, config, directives) {\n    const result = [];\n    if (f2 != null) {\n        result.push(...f2);\n    }\n    if (f1 != null) {\n        for (const field of f1) {\n            const [existing, existingIndex] = fieldAlreadyExists(result, field);\n            if (existing && !config?.ignoreFieldConflicts) {\n                const newField = (config?.onFieldTypeConflict &&\n                    config.onFieldTypeConflict(existing, field, type, config?.throwOnConflict)) ||\n                    preventConflicts(type, existing, field, config?.throwOnConflict);\n                newField.arguments = mergeArguments(field['arguments'] || [], existing['arguments'] || [], config);\n                newField.directives = mergeDirectives(field.directives, existing.directives, config, directives);\n                newField.description = field.description || existing.description;\n                result[existingIndex] = newField;\n            }\n            else {\n                result.push(field);\n            }\n        }\n    }\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    if (config && config.exclusions) {\n        const exclusions = config.exclusions;\n        return result.filter(field => !exclusions.includes(`${type.name.value}.${field.name.value}`));\n    }\n    return result;\n}\nfunction preventConflicts(type, a, b, ignoreNullability = false) {\n    const aType = printTypeNode(a.type);\n    const bType = printTypeNode(b.type);\n    if (aType !== bType) {\n        const t1 = extractType(a.type);\n        const t2 = extractType(b.type);\n        if (t1.name.value !== t2.name.value) {\n            throw new Error(`Field \"${b.name.value}\" already defined with a different type. Declared as \"${t1.name.value}\", but you tried to override with \"${t2.name.value}\"`);\n        }\n        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {\n            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);\n        }\n    }\n    if (isNonNullTypeNode(b.type) && !isNonNullTypeNode(a.type)) {\n        a.type = b.type;\n    }\n    return a;\n}\nfunction safeChangeForFieldType(oldType, newType, ignoreNullability = false) {\n    // both are named\n    if (!isWrappingTypeNode(oldType) && !isWrappingTypeNode(newType)) {\n        return oldType.toString() === newType.toString();\n    }\n    // new is non-null\n    if (isNonNullTypeNode(newType)) {\n        const ofType = isNonNullTypeNode(oldType) ? oldType.type : oldType;\n        return safeChangeForFieldType(ofType, newType.type);\n    }\n    // old is non-null\n    if (isNonNullTypeNode(oldType)) {\n        return safeChangeForFieldType(newType, oldType, ignoreNullability);\n    }\n    // old is list\n    if (isListTypeNode(oldType)) {\n        return ((isListTypeNode(newType) && safeChangeForFieldType(oldType.type, newType.type)) ||\n            (isNonNullTypeNode(newType) && safeChangeForFieldType(oldType, newType['type'])));\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,mBAAmB,SAAS,EAAE,UAAU;IAC7C,MAAM,cAAc,UAAU,SAAS,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,KAAK;IAC3F,OAAO;QAAC,cAAc,CAAC,IAAI,SAAS,CAAC,YAAY,GAAG;QAAM;KAAY;AAC1E;AACO,SAAS,YAAY,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU;IACxD,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,MAAM;QACZ,OAAO,IAAI,IAAI;IACnB;IACA,IAAI,MAAM,MAAM;QACZ,KAAK,MAAM,SAAS,GAAI;YACpB,MAAM,CAAC,UAAU,cAAc,GAAG,mBAAmB,QAAQ;YAC7D,IAAI,YAAY,CAAC,QAAQ,sBAAsB;gBAC3C,MAAM,WAAW,AAAC,QAAQ,uBACtB,OAAO,mBAAmB,CAAC,UAAU,OAAO,MAAM,QAAQ,oBAC1D,iBAAiB,MAAM,UAAU,OAAO,QAAQ;gBACpD,SAAS,SAAS,GAAG,CAAA,GAAA,wLAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,EAAE;gBAC3F,SAAS,UAAU,GAAG,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,UAAU,EAAE,SAAS,UAAU,EAAE,QAAQ;gBACrF,SAAS,WAAW,GAAG,MAAM,WAAW,IAAI,SAAS,WAAW;gBAChE,MAAM,CAAC,cAAc,GAAG;YAC5B,OACK;gBACD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ;IACA,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+JAAA,CAAA,eAAY;IAC5B;IACA,IAAI,UAAU,OAAO,UAAU,EAAE;QAC7B,MAAM,aAAa,OAAO,UAAU;QACpC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,WAAW,QAAQ,CAAC,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;IAC/F;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,KAAK;IAC3D,MAAM,QAAQ,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,IAAI;IAClC,MAAM,QAAQ,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,IAAI;IAClC,IAAI,UAAU,OAAO;QACjB,MAAM,KAAK,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,EAAE,IAAI;QAC7B,MAAM,KAAK,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,EAAE,IAAI;QAC7B,IAAI,GAAG,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,sDAAsD,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACtK;QACA,IAAI,CAAC,uBAAuB,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,oBAAoB;YAC7D,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3G;IACJ;IACA,IAAI,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,IAAI,KAAK,CAAC,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,EAAE,IAAI,GAAG;QACzD,EAAE,IAAI,GAAG,EAAE,IAAI;IACnB;IACA,OAAO;AACX;AACA,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,oBAAoB,KAAK;IACvE,iBAAiB;IACjB,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,CAAC,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;QAC9D,OAAO,QAAQ,QAAQ,OAAO,QAAQ,QAAQ;IAClD;IACA,kBAAkB;IAClB,IAAI,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QAC5B,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,QAAQ,IAAI,GAAG;QAC3D,OAAO,uBAAuB,QAAQ,QAAQ,IAAI;IACtD;IACA,kBAAkB;IAClB,IAAI,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;QAC5B,OAAO,uBAAuB,SAAS,SAAS;IACpD;IACA,cAAc;IACd,IAAI,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QACzB,OAAQ,AAAC,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,uBAAuB,QAAQ,IAAI,EAAE,QAAQ,IAAI,KAChF,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,uBAAuB,SAAS,OAAO,CAAC,OAAO;IACtF;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2427, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/input-type.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nexport function mergeInputType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InputObjectTypeDefinition' ||\n                    existingNode.kind === 'InputObjectTypeDefinition'\n                    ? 'InputObjectTypeDefinition'\n                    : 'InputObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL input type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INPUT_OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,+BACd,aAAa,IAAI,KAAK,8BACpB,8BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE;gBAC5D,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAClF;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC3F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,4BAA4B;IAC3C,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2462, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js"], "sourcesContent": ["import { compareNodes } from '@graphql-tools/utils';\nfunction alreadyExists(arr, other) {\n    return !!arr.find(i => i.name.value === other.name.value);\n}\nexport function mergeNamedTypeArray(first = [], second = [], config = {}) {\n    const result = [...second, ...first.filter(d => !alreadyExists(second, d))];\n    if (config && config.sort) {\n        result.sort(compareNodes);\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,cAAc,GAAG,EAAE,KAAK;IAC7B,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK;AAC5D;AACO,SAAS,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,CAAC;IACpE,MAAM,SAAS;WAAI;WAAW,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,cAAc,QAAQ;KAAI;IAC3E,IAAI,UAAU,OAAO,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,+JAAA,CAAA,eAAY;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2486, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/interface.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeInterface(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'InterfaceTypeDefinition' ||\n                    existingNode.kind === 'InterfaceTypeDefinition'\n                    ? 'InterfaceTypeDefinition'\n                    : 'InterfaceTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: node['interfaces']\n                    ? mergeNamedTypeArray(node['interfaces'], existingNode['interfaces'], config)\n                    : undefined,\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL interface \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.INTERFACE_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IACjE,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,6BACd,aAAa,IAAI,KAAK,4BACpB,4BACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACpE,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,IAAI,CAAC,aAAa,GACxB,CAAA,GAAA,8MAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,aAAa,EAAE,UACpE;YACV;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QAC1F;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;IACxC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/scalar.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport function mergeScalar(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            name: node.name,\n            description: node['description'] || existingNode['description'],\n            kind: config?.convertExtensions ||\n                node.kind === 'ScalarTypeDefinition' ||\n                existingNode.kind === 'ScalarTypeDefinition'\n                ? 'ScalarTypeDefinition'\n                : 'ScalarTypeExtension',\n            loc: node.loc,\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCALAR_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,YAAY,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC9D,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI;YACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;YACN,KAAK,KAAK,GAAG;YACb,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;QAClF;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2552, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/schema-def.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nexport const DEFAULT_OPERATION_TYPE_NAME_MAP = {\n    query: 'Query',\n    mutation: 'Mutation',\n    subscription: 'Subscription',\n};\nfunction mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {\n    const finalOpNodeList = [];\n    for (const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n        const opNode = opNodeList.find(n => n.operation === opNodeType) ||\n            existingOpNodeList.find(n => n.operation === opNodeType);\n        if (opNode) {\n            finalOpNodeList.push(opNode);\n        }\n    }\n    return finalOpNodeList;\n}\nexport function mergeSchemaDefs(node, existingNode, config, directives) {\n    if (existingNode) {\n        return {\n            kind: node.kind === Kind.SCHEMA_DEFINITION || existingNode.kind === Kind.SCHEMA_DEFINITION\n                ? Kind.SCHEMA_DEFINITION\n                : Kind.SCHEMA_EXTENSION,\n            description: node['description'] || existingNode['description'],\n            directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes),\n        };\n    }\n    return (config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.SCHEMA_DEFINITION,\n        }\n        : node);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,MAAM,kCAAkC;IAC3C,OAAO;IACP,UAAU;IACV,cAAc;AAClB;AACA,SAAS,oBAAoB,aAAa,EAAE,EAAE,qBAAqB,EAAE;IACjE,MAAM,kBAAkB,EAAE;IAC1B,IAAK,MAAM,cAAc,gCAAiC;QACtD,MAAM,SAAS,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,eAChD,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACjD,IAAI,QAAQ;YACR,gBAAgB,IAAI,CAAC;QACzB;IACJ;IACA,OAAO;AACX;AACO,SAAS,gBAAgB,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAClE,IAAI,cAAc;QACd,OAAO;YACH,MAAM,KAAK,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,IAAI,aAAa,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,GACpF,+IAAA,CAAA,OAAI,CAAC,iBAAiB,GACtB,+IAAA,CAAA,OAAI,CAAC,gBAAgB;YAC3B,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;YAC/D,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;YAC9E,gBAAgB,oBAAoB,KAAK,cAAc,EAAE,aAAa,cAAc;QACxF;IACJ;IACA,OAAQ,QAAQ,oBACV;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;IAChC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/type.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeFields } from './fields.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeType(node, existingNode, config, directives) {\n    if (existingNode) {\n        try {\n            return {\n                name: node.name,\n                description: node['description'] || existingNode['description'],\n                kind: config?.convertExtensions ||\n                    node.kind === 'ObjectTypeDefinition' ||\n                    existingNode.kind === 'ObjectTypeDefinition'\n                    ? 'ObjectTypeDefinition'\n                    : 'ObjectTypeExtension',\n                loc: node.loc,\n                fields: mergeFields(node, node.fields, existingNode.fields, config, directives),\n                directives: mergeDirectives(node.directives, existingNode.directives, config, directives),\n                interfaces: mergeNamedTypeArray(node.interfaces, existingNode.interfaces, config),\n            };\n        }\n        catch (e) {\n            throw new Error(`Unable to merge GraphQL type \"${node.name.value}\": ${e.message}`);\n        }\n    }\n    return config?.convertExtensions\n        ? {\n            ...node,\n            kind: Kind.OBJECT_TYPE_DEFINITION,\n        }\n        : node;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU;IAC5D,IAAI,cAAc;QACd,IAAI;YACA,OAAO;gBACH,MAAM,KAAK,IAAI;gBACf,aAAa,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc;gBAC/D,MAAM,QAAQ,qBACV,KAAK,IAAI,KAAK,0BACd,aAAa,IAAI,KAAK,yBACpB,yBACA;gBACN,KAAK,KAAK,GAAG;gBACb,QAAQ,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE,MAAM,KAAK,MAAM,EAAE,aAAa,MAAM,EAAE,QAAQ;gBACpE,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE,QAAQ;gBAC9E,YAAY,CAAA,GAAA,8MAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,UAAU,EAAE,aAAa,UAAU,EAAE;YAC9E;QACJ,EACA,OAAO,GAAG;YACN,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE;QACrF;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,IAAI;QACP,MAAM,+IAAA,CAAA,OAAI,CAAC,sBAAsB;IACrC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2633, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/union.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { mergeDirectives } from './directives.js';\nimport { mergeNamedTypeArray } from './merge-named-type-array.js';\nexport function mergeUnion(first, second, config, directives) {\n    if (second) {\n        return {\n            name: first.name,\n            description: first['description'] || second['description'],\n            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility\n            directives: mergeDirectives(first.directives, second.directives, config, directives),\n            kind: config?.convertExtensions ||\n                first.kind === 'UnionTypeDefinition' ||\n                second.kind === 'UnionTypeDefinition'\n                ? Kind.UNION_TYPE_DEFINITION\n                : Kind.UNION_TYPE_EXTENSION,\n            loc: first.loc,\n            types: mergeNamedTypeArray(first.types, second.types, config),\n        };\n    }\n    return config?.convertExtensions\n        ? {\n            ...first,\n            kind: Kind.UNION_TYPE_DEFINITION,\n        }\n        : first;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,SAAS,WAAW,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACxD,IAAI,QAAQ;QACR,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,aAAa,KAAK,CAAC,cAAc,IAAI,MAAM,CAAC,cAAc;YAC1D,0HAA0H;YAC1H,YAAY,CAAA,GAAA,yLAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,QAAQ;YACzE,MAAM,QAAQ,qBACV,MAAM,IAAI,KAAK,yBACf,OAAO,IAAI,KAAK,wBACd,+IAAA,CAAA,OAAI,CAAC,qBAAqB,GAC1B,+IAAA,CAAA,OAAI,CAAC,oBAAoB;YAC/B,KAAK,MAAM,GAAG;YACd,OAAO,CAAA,GAAA,8MAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,KAAK,EAAE,OAAO,KAAK,EAAE;QAC1D;IACJ;IACA,OAAO,QAAQ,oBACT;QACE,GAAG,KAAK;QACR,MAAM,+IAAA,CAAA,OAAI,CAAC,qBAAqB;IACpC,IACE;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2665, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js"], "sourcesContent": ["import { Kind, } from 'graphql';\nimport { collectComment } from '@graphql-tools/utils';\nimport { mergeDirective } from './directives.js';\nimport { mergeEnum } from './enum.js';\nimport { mergeInputType } from './input-type.js';\nimport { mergeInterface } from './interface.js';\nimport { mergeScalar } from './scalar.js';\nimport { mergeSchemaDefs } from './schema-def.js';\nimport { mergeType } from './type.js';\nimport { mergeUnion } from './union.js';\nexport const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';\nexport function isNamedDefinitionNode(definitionNode) {\n    return 'name' in definitionNode;\n}\nexport function mergeGraphQLNodes(nodes, config, directives = {}) {\n    const mergedResultMap = directives;\n    for (const nodeDefinition of nodes) {\n        if (isNamedDefinitionNode(nodeDefinition)) {\n            const name = nodeDefinition.name?.value;\n            if (config?.commentDescriptions) {\n                collectComment(nodeDefinition);\n            }\n            if (name == null) {\n                continue;\n            }\n            if (config?.exclusions?.includes(name + '.*') || config?.exclusions?.includes(name)) {\n                delete mergedResultMap[name];\n            }\n            else {\n                switch (nodeDefinition.kind) {\n                    case Kind.OBJECT_TYPE_DEFINITION:\n                    case Kind.OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.ENUM_TYPE_DEFINITION:\n                    case Kind.ENUM_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeEnum(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.UNION_TYPE_DEFINITION:\n                    case Kind.UNION_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeUnion(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.SCALAR_TYPE_DEFINITION:\n                    case Kind.SCALAR_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeScalar(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INPUT_OBJECT_TYPE_DEFINITION:\n                    case Kind.INPUT_OBJECT_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInputType(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.INTERFACE_TYPE_DEFINITION:\n                    case Kind.INTERFACE_TYPE_EXTENSION:\n                        mergedResultMap[name] = mergeInterface(nodeDefinition, mergedResultMap[name], config, directives);\n                        break;\n                    case Kind.DIRECTIVE_DEFINITION:\n                        if (mergedResultMap[name]) {\n                            const isInheritedFromPrototype = name in {}; // i.e. toString\n                            if (isInheritedFromPrototype) {\n                                if (!isASTNode(mergedResultMap[name])) {\n                                    mergedResultMap[name] = undefined;\n                                }\n                            }\n                        }\n                        mergedResultMap[name] = mergeDirective(nodeDefinition, mergedResultMap[name]);\n                        break;\n                }\n            }\n        }\n        else if (nodeDefinition.kind === Kind.SCHEMA_DEFINITION ||\n            nodeDefinition.kind === Kind.SCHEMA_EXTENSION) {\n            mergedResultMap[schemaDefSymbol] = mergeSchemaDefs(nodeDefinition, mergedResultMap[schemaDefSymbol], config);\n        }\n    }\n    return mergedResultMap;\n}\nfunction isASTNode(node) {\n    return (node != null && typeof node === 'object' && 'kind' in node && typeof node.kind === 'string');\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,MAAM,kBAAkB;AACxB,SAAS,sBAAsB,cAAc;IAChD,OAAO,UAAU;AACrB;AACO,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC5D,MAAM,kBAAkB;IACxB,KAAK,MAAM,kBAAkB,MAAO;QAChC,IAAI,sBAAsB,iBAAiB;YACvC,MAAM,OAAO,eAAe,IAAI,EAAE;YAClC,IAAI,QAAQ,qBAAqB;gBAC7B,CAAA,GAAA,gKAAA,CAAA,iBAAc,AAAD,EAAE;YACnB;YACA,IAAI,QAAQ,MAAM;gBACd;YACJ;YACA,IAAI,QAAQ,YAAY,SAAS,OAAO,SAAS,QAAQ,YAAY,SAAS,OAAO;gBACjF,OAAO,eAAe,CAAC,KAAK;YAChC,OACK;gBACD,OAAQ,eAAe,IAAI;oBACvB,KAAK,+IAAA,CAAA,OAAI,CAAC,sBAAsB;oBAChC,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;oBAC9B,KAAK,+IAAA,CAAA,OAAI,CAAC,mBAAmB;wBACzB,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACjF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;oBAC/B,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;wBAC1B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBAClF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,sBAAsB;oBAChC,KAAK,+IAAA,CAAA,OAAI,CAAC,qBAAqB;wBAC3B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,qLAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACnF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,4BAA4B;oBACtC,KAAK,+IAAA,CAAA,OAAI,CAAC,2BAA2B;wBACjC,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,yBAAyB;oBACnC,KAAK,+IAAA,CAAA,OAAI,CAAC,wBAAwB;wBAC9B,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,wLAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK,EAAE,QAAQ;wBACtF;oBACJ,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB;wBAC1B,IAAI,eAAe,CAAC,KAAK,EAAE;4BACvB,MAAM,2BAA2B,QAAQ,CAAC,GAAG,gBAAgB;4BAC7D,IAAI,0BAA0B;gCAC1B,IAAI,CAAC,UAAU,eAAe,CAAC,KAAK,GAAG;oCACnC,eAAe,CAAC,KAAK,GAAG;gCAC5B;4BACJ;wBACJ;wBACA,eAAe,CAAC,KAAK,GAAG,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,eAAe,CAAC,KAAK;wBAC5E;gBACR;YACJ;QACJ,OACK,IAAI,eAAe,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,iBAAiB,IACnD,eAAe,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,gBAAgB,EAAE;YAC/C,eAAe,CAAC,gBAAgB,GAAG,CAAA,GAAA,4LAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe,CAAC,gBAAgB,EAAE;QACzG;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI;IACnB,OAAQ,QAAQ,QAAQ,OAAO,SAAS,YAAY,UAAU,QAAQ,OAAO,KAAK,IAAI,KAAK;AAC/F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2761, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/node_modules/%40graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js"], "sourcesContent": ["import { isDefinitionNode, isSchema, Kind, parse, } from 'graphql';\nimport { getDocumentNodeFromSchema, isDocumentNode, printWithComments, resetComments, } from '@graphql-tools/utils';\nimport { mergeGraphQLNodes, schemaDefSymbol } from './merge-nodes.js';\nimport { DEFAULT_OPERATION_TYPE_NAME_MAP } from './schema-def.js';\nimport { defaultStringComparator, isSourceTypes, isStringTypes } from './utils.js';\nexport function mergeTypeDefs(typeSource, config) {\n    resetComments();\n    const doc = {\n        kind: Kind.DOCUMENT,\n        definitions: mergeGraphQLTypes(typeSource, {\n            useSchemaDefinition: true,\n            forceSchemaDefinition: false,\n            throwOnConflict: false,\n            commentDescriptions: false,\n            ...config,\n        }),\n    };\n    let result;\n    if (config?.commentDescriptions) {\n        result = printWithComments(doc);\n    }\n    else {\n        result = doc;\n    }\n    resetComments();\n    return result;\n}\nfunction visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {\n    if (typeSource && !visitedTypeSources.has(typeSource)) {\n        visitedTypeSources.add(typeSource);\n        if (typeof typeSource === 'function') {\n            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (Array.isArray(typeSource)) {\n            for (const type of typeSource) {\n                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);\n            }\n        }\n        else if (isSchema(typeSource)) {\n            const documentNode = getDocumentNodeFromSchema(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (isStringTypes(typeSource) || isSourceTypes(typeSource)) {\n            const documentNode = parse(typeSource, options);\n            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else if (typeof typeSource === 'object' && isDefinitionNode(typeSource)) {\n            if (typeSource.kind === Kind.DIRECTIVE_DEFINITION) {\n                allDirectives.push(typeSource);\n            }\n            else {\n                allNodes.push(typeSource);\n            }\n        }\n        else if (isDocumentNode(typeSource)) {\n            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);\n        }\n        else {\n            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);\n        }\n    }\n    return { allDirectives, allNodes };\n}\nexport function mergeGraphQLTypes(typeSource, config) {\n    resetComments();\n    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);\n    const mergedDirectives = mergeGraphQLNodes(allDirectives, config);\n    const mergedNodes = mergeGraphQLNodes(allNodes, config, mergedDirectives);\n    if (config?.useSchemaDefinition) {\n        // XXX: right now we don't handle multiple schema definitions\n        const schemaDef = mergedNodes[schemaDefSymbol] || {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [],\n        };\n        const operationTypes = schemaDef.operationTypes;\n        for (const opTypeDefNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP) {\n            const opTypeDefNode = operationTypes.find(operationType => operationType.operation === opTypeDefNodeType);\n            if (!opTypeDefNode) {\n                const possibleRootTypeName = DEFAULT_OPERATION_TYPE_NAME_MAP[opTypeDefNodeType];\n                const existingPossibleRootType = mergedNodes[possibleRootTypeName];\n                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {\n                    operationTypes.push({\n                        kind: Kind.OPERATION_TYPE_DEFINITION,\n                        type: {\n                            kind: Kind.NAMED_TYPE,\n                            name: existingPossibleRootType.name,\n                        },\n                        operation: opTypeDefNodeType,\n                    });\n                }\n            }\n        }\n        if (schemaDef?.operationTypes?.length != null && schemaDef.operationTypes.length > 0) {\n            mergedNodes[schemaDefSymbol] = schemaDef;\n        }\n    }\n    if (config?.forceSchemaDefinition && !mergedNodes[schemaDefSymbol]?.operationTypes?.length) {\n        mergedNodes[schemaDefSymbol] = {\n            kind: Kind.SCHEMA_DEFINITION,\n            operationTypes: [\n                {\n                    kind: Kind.OPERATION_TYPE_DEFINITION,\n                    operation: 'query',\n                    type: {\n                        kind: Kind.NAMED_TYPE,\n                        name: {\n                            kind: Kind.NAME,\n                            value: 'Query',\n                        },\n                    },\n                },\n            ],\n        };\n    }\n    const mergedNodeDefinitions = Object.values(mergedNodes);\n    if (config?.sort) {\n        const sortFn = typeof config.sort === 'function' ? config.sort : defaultStringComparator;\n        mergedNodeDefinitions.sort((a, b) => sortFn(a.name?.value, b.name?.value));\n    }\n    return mergedNodeDefinitions;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AACO,SAAS,cAAc,UAAU,EAAE,MAAM;IAC5C,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD;IACZ,MAAM,MAAM;QACR,MAAM,+IAAA,CAAA,OAAI,CAAC,QAAQ;QACnB,aAAa,kBAAkB,YAAY;YACvC,qBAAqB;YACrB,uBAAuB;YACvB,iBAAiB;YACjB,qBAAqB;YACrB,GAAG,MAAM;QACb;IACJ;IACA,IAAI;IACJ,IAAI,QAAQ,qBAAqB;QAC7B,SAAS,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC/B,OACK;QACD,SAAS;IACb;IACA,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD;IACZ,OAAO;AACX;AACA,SAAS,iBAAiB,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,WAAW,EAAE,EAAE,qBAAqB,IAAI,KAAK;IAC5G,IAAI,cAAc,CAAC,mBAAmB,GAAG,CAAC,aAAa;QACnD,mBAAmB,GAAG,CAAC;QACvB,IAAI,OAAO,eAAe,YAAY;YAClC,iBAAiB,cAAc,SAAS,eAAe,UAAU;QACrE,OACK,IAAI,MAAM,OAAO,CAAC,aAAa;YAChC,KAAK,MAAM,QAAQ,WAAY;gBAC3B,iBAAiB,MAAM,SAAS,eAAe,UAAU;YAC7D;QACJ,OACK,IAAI,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC3B,MAAM,eAAe,CAAA,GAAA,6LAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY;YAC3D,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,aAAa;YAC7D,MAAM,eAAe,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,YAAY;YACvC,iBAAiB,aAAa,WAAW,EAAE,SAAS,eAAe,UAAU;QACjF,OACK,IAAI,OAAO,eAAe,YAAY,CAAA,GAAA,oJAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;YACrE,IAAI,WAAW,IAAI,KAAK,+IAAA,CAAA,OAAI,CAAC,oBAAoB,EAAE;gBAC/C,cAAc,IAAI,CAAC;YACvB,OACK;gBACD,SAAS,IAAI,CAAC;YAClB;QACJ,OACK,IAAI,CAAA,GAAA,sKAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;YACjC,iBAAiB,WAAW,WAAW,EAAE,SAAS,eAAe,UAAU;QAC/E,OACK;YACD,MAAM,IAAI,MAAM,CAAC,0EAA0E,EAAE,OAAO,YAAY;QACpH;IACJ;IACA,OAAO;QAAE;QAAe;IAAS;AACrC;AACO,SAAS,kBAAkB,UAAU,EAAE,MAAM;IAChD,CAAA,GAAA,gKAAA,CAAA,gBAAa,AAAD;IACZ,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,iBAAiB,YAAY;IACjE,MAAM,mBAAmB,CAAA,GAAA,6LAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IAC1D,MAAM,cAAc,CAAA,GAAA,6LAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,QAAQ;IACxD,IAAI,QAAQ,qBAAqB;QAC7B,6DAA6D;QAC7D,MAAM,YAAY,WAAW,CAAC,6LAAA,CAAA,kBAAe,CAAC,IAAI;YAC9C,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;YAC5B,gBAAgB,EAAE;QACtB;QACA,MAAM,iBAAiB,UAAU,cAAc;QAC/C,IAAK,MAAM,qBAAqB,4LAAA,CAAA,kCAA+B,CAAE;YAC7D,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,gBAAiB,cAAc,SAAS,KAAK;YACvF,IAAI,CAAC,eAAe;gBAChB,MAAM,uBAAuB,4LAAA,CAAA,kCAA+B,CAAC,kBAAkB;gBAC/E,MAAM,2BAA2B,WAAW,CAAC,qBAAqB;gBAClE,IAAI,4BAA4B,QAAQ,yBAAyB,IAAI,IAAI,MAAM;oBAC3E,eAAe,IAAI,CAAC;wBAChB,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;wBACpC,MAAM;4BACF,MAAM,+IAAA,CAAA,OAAI,CAAC,UAAU;4BACrB,MAAM,yBAAyB,IAAI;wBACvC;wBACA,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,IAAI,WAAW,gBAAgB,UAAU,QAAQ,UAAU,cAAc,CAAC,MAAM,GAAG,GAAG;YAClF,WAAW,CAAC,6LAAA,CAAA,kBAAe,CAAC,GAAG;QACnC;IACJ;IACA,IAAI,QAAQ,yBAAyB,CAAC,WAAW,CAAC,6LAAA,CAAA,kBAAe,CAAC,EAAE,gBAAgB,QAAQ;QACxF,WAAW,CAAC,6LAAA,CAAA,kBAAe,CAAC,GAAG;YAC3B,MAAM,+IAAA,CAAA,OAAI,CAAC,iBAAiB;YAC5B,gBAAgB;gBACZ;oBACI,MAAM,+IAAA,CAAA,OAAI,CAAC,yBAAyB;oBACpC,WAAW;oBACX,MAAM;wBACF,MAAM,+IAAA,CAAA,OAAI,CAAC,UAAU;wBACrB,MAAM;4BACF,MAAM,+IAAA,CAAA,OAAI,CAAC,IAAI;4BACf,OAAO;wBACX;oBACJ;gBACJ;aACH;QACL;IACJ;IACA,MAAM,wBAAwB,OAAO,MAAM,CAAC;IAC5C,IAAI,QAAQ,MAAM;QACd,MAAM,SAAS,OAAO,OAAO,IAAI,KAAK,aAAa,OAAO,IAAI,GAAG,oLAAA,CAAA,0BAAuB;QACxF,sBAAsB,IAAI,CAAC,CAAC,GAAG,IAAM,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IACvE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}