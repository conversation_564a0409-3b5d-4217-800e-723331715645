module.exports = {

"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/extensions.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyExtensions": (()=>applyExtensions),
    "mergeExtensions": (()=>mergeExtensions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/mergeDeep.js [app-route] (ecmascript)");
;
;
function mergeExtensions(extensions) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])(extensions);
}
function applyExtensionObject(obj, extensions) {
    if (!obj) {
        return;
    }
    obj.extensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])([
        obj.extensions || {},
        extensions || {}
    ]);
}
function applyExtensions(schema, extensions) {
    applyExtensionObject(schema, extensions.schemaExtensions);
    for (const [typeName, data] of Object.entries(extensions.types || {})){
        const type = schema.getType(typeName);
        if (type) {
            applyExtensionObject(type, data.extensions);
            if (data.type === 'object' || data.type === 'interface') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)){
                    const field = type.getFields()[fieldName];
                    if (field) {
                        applyExtensionObject(field, fieldData.extensions);
                        for (const [arg, argData] of Object.entries(fieldData.arguments)){
                            applyExtensionObject(field.args.find((a)=>a.name === arg), argData);
                        }
                    }
                }
            } else if (data.type === 'input') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)){
                    const field = type.getFields()[fieldName];
                    applyExtensionObject(field, fieldData.extensions);
                }
            } else if (data.type === 'enum') {
                for (const [valueName, valueData] of Object.entries(data.values)){
                    const value = type.getValue(valueName);
                    applyExtensionObject(value, valueData);
                }
            }
        }
    }
    return schema;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/merge-resolvers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeResolvers": (()=>mergeResolvers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/mergeDeep.js [app-route] (ecmascript)");
;
function mergeResolvers(resolversDefinitions, options) {
    if (!resolversDefinitions || Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0) {
        return {};
    }
    if (!Array.isArray(resolversDefinitions)) {
        return resolversDefinitions;
    }
    if (resolversDefinitions.length === 1) {
        return resolversDefinitions[0] || {};
    }
    const resolvers = new Array();
    for (let resolversDefinition of resolversDefinitions){
        if (Array.isArray(resolversDefinition)) {
            resolversDefinition = mergeResolvers(resolversDefinition);
        }
        if (typeof resolversDefinition === 'object' && resolversDefinition) {
            resolvers.push(resolversDefinition);
        }
    }
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])(resolvers, true);
    if (options === null || options === void 0 ? void 0 : options.exclusions) {
        for (const exclusion of options.exclusions){
            const [typeName, fieldName] = exclusion.split('.');
            if (!fieldName || fieldName === '*') {
                delete result[typeName];
            } else if (result[typeName]) {
                delete result[typeName][fieldName];
            }
        }
    }
    return result;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompareVal": (()=>CompareVal),
    "defaultStringComparator": (()=>defaultStringComparator),
    "extractType": (()=>extractType),
    "isListTypeNode": (()=>isListTypeNode),
    "isNonNullTypeNode": (()=>isNonNullTypeNode),
    "isSourceTypes": (()=>isSourceTypes),
    "isStringTypes": (()=>isStringTypes),
    "isWrappingTypeNode": (()=>isWrappingTypeNode),
    "printTypeNode": (()=>printTypeNode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$source$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/source.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
;
function isStringTypes(types) {
    return typeof types === 'string';
}
function isSourceTypes(types) {
    return types instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$source$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Source"];
}
function extractType(type) {
    let visitedType = type;
    while(visitedType.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE || visitedType.kind === 'NonNullType'){
        visitedType = visitedType.type;
    }
    return visitedType;
}
function isWrappingTypeNode(type) {
    return type.kind !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE;
}
function isListTypeNode(type) {
    return type.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE;
}
function isNonNullTypeNode(type) {
    return type.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NON_NULL_TYPE;
}
function printTypeNode(type) {
    if (isListTypeNode(type)) {
        return `[${printTypeNode(type.type)}]`;
    }
    if (isNonNullTypeNode(type)) {
        return `${printTypeNode(type.type)}!`;
    }
    return type.name.value;
}
var CompareVal;
(function(CompareVal) {
    CompareVal[CompareVal["A_SMALLER_THAN_B"] = -1] = "A_SMALLER_THAN_B";
    CompareVal[CompareVal["A_EQUALS_B"] = 0] = "A_EQUALS_B";
    CompareVal[CompareVal["A_GREATER_THAN_B"] = 1] = "A_GREATER_THAN_B";
})(CompareVal || (CompareVal = {}));
function defaultStringComparator(a, b) {
    if (a == null && b == null) {
        return CompareVal.A_EQUALS_B;
    }
    if (a == null) {
        return CompareVal.A_SMALLER_THAN_B;
    }
    if (b == null) {
        return CompareVal.A_GREATER_THAN_B;
    }
    if (a < b) return CompareVal.A_SMALLER_THAN_B;
    if (a > b) return CompareVal.A_GREATER_THAN_B;
    return CompareVal.A_EQUALS_B;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeDirective": (()=>mergeDirective),
    "mergeDirectives": (()=>mergeDirectives)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/printer.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
;
function directiveAlreadyExists(directivesArr, otherDirective) {
    return !!directivesArr.find((directive)=>directive.name.value === otherDirective.name.value);
}
function isRepeatableDirective(directive, directives) {
    var _a;
    return !!((_a = directives === null || directives === void 0 ? void 0 : directives[directive.name.value]) === null || _a === void 0 ? void 0 : _a.repeatable);
}
function nameAlreadyExists(name, namesArr) {
    return namesArr.some(({ value })=>value === name.value);
}
function mergeArguments(a1, a2) {
    const result = [
        ...a2
    ];
    for (const argument of a1){
        const existingIndex = result.findIndex((a)=>a.name.value === argument.name.value);
        if (existingIndex > -1) {
            const existingArg = result[existingIndex];
            if (existingArg.value.kind === 'ListValue') {
                const source = existingArg.value.values;
                const target = argument.value.values;
                // merge values of two lists
                existingArg.value.values = deduplicateLists(source, target, (targetVal, source)=>{
                    const value = targetVal.value;
                    return !value || !source.some((sourceVal)=>sourceVal.value === value);
                });
            } else {
                existingArg.value = argument.value;
            }
        } else {
            result.push(argument);
        }
    }
    return result;
}
function deduplicateDirectives(directives, definitions) {
    return directives.map((directive, i, all)=>{
        const firstAt = all.findIndex((d)=>d.name.value === directive.name.value);
        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {
            const dup = all[firstAt];
            directive.arguments = mergeArguments(directive.arguments, dup.arguments);
            return null;
        }
        return directive;
    }).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"]);
}
function mergeDirectives(d1 = [], d2 = [], config, directives) {
    const reverseOrder = config && config.reverseDirectives;
    const asNext = reverseOrder ? d1 : d2;
    const asFirst = reverseOrder ? d2 : d1;
    const result = deduplicateDirectives([
        ...asNext
    ], directives);
    for (const directive of asFirst){
        if (directiveAlreadyExists(result, directive) && !isRepeatableDirective(directive, directives)) {
            const existingDirectiveIndex = result.findIndex((d)=>d.name.value === directive.name.value);
            const existingDirective = result[existingDirectiveIndex];
            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);
        } else {
            result.push(directive);
        }
    }
    return result;
}
function validateInputs(node, existingNode) {
    const printedNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])({
        ...node,
        description: undefined
    });
    const printedExistingNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$printer$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["print"])({
        ...existingNode,
        description: undefined
    });
    // eslint-disable-next-line
    const leaveInputs = new RegExp('(directive @w*d*)|( on .*$)', 'g');
    const sameArguments = printedNode.replace(leaveInputs, '') === printedExistingNode.replace(leaveInputs, '');
    if (!sameArguments) {
        throw new Error(`Unable to merge GraphQL directive "${node.name.value}". \nExisting directive:  \n\t${printedExistingNode} \nReceived directive: \n\t${printedNode}`);
    }
}
function mergeDirective(node, existingNode) {
    if (existingNode) {
        validateInputs(node, existingNode);
        return {
            ...node,
            locations: [
                ...existingNode.locations,
                ...node.locations.filter((name)=>!nameAlreadyExists(name, existingNode.locations))
            ]
        };
    }
    return node;
}
function deduplicateLists(source, target, filterFn) {
    return source.concat(target.filter((val)=>filterFn(val, source)));
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/arguments.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeArguments": (()=>mergeArguments)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function mergeArguments(args1, args2, config) {
    const result = deduplicateArguments([
        ...args2,
        ...args1
    ].filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"]), config);
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
function deduplicateArguments(args, config) {
    return args.reduce((acc, current)=>{
        const dupIndex = acc.findIndex((arg)=>arg.name.value === current.name.value);
        if (dupIndex === -1) {
            return acc.concat([
                current
            ]);
        } else if (!(config === null || config === void 0 ? void 0 : config.reverseArguments)) {
            acc[dupIndex] = current;
        }
        return acc;
    }, []);
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeFields": (()=>mergeFields)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$arguments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/arguments.js [app-route] (ecmascript)");
;
;
;
;
function fieldAlreadyExists(fieldsArr, otherField) {
    const resultIndex = fieldsArr.findIndex((field)=>field.name.value === otherField.name.value);
    return [
        resultIndex > -1 ? fieldsArr[resultIndex] : null,
        resultIndex
    ];
}
function mergeFields(type, f1, f2, config, directives) {
    const result = [];
    if (f2 != null) {
        result.push(...f2);
    }
    if (f1 != null) {
        for (const field of f1){
            const [existing, existingIndex] = fieldAlreadyExists(result, field);
            if (existing && !(config === null || config === void 0 ? void 0 : config.ignoreFieldConflicts)) {
                const newField = (config === null || config === void 0 ? void 0 : config.onFieldTypeConflict) && config.onFieldTypeConflict(existing, field, type, config === null || config === void 0 ? void 0 : config.throwOnConflict) || preventConflicts(type, existing, field, config === null || config === void 0 ? void 0 : config.throwOnConflict);
                newField.arguments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$arguments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeArguments"])(field['arguments'] || [], existing['arguments'] || [], config);
                newField.directives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(field.directives, existing.directives, config, directives);
                newField.description = field.description || existing.description;
                result[existingIndex] = newField;
            } else {
                result.push(field);
            }
        }
    }
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    if (config && config.exclusions) {
        const exclusions = config.exclusions;
        return result.filter((field)=>!exclusions.includes(`${type.name.value}.${field.name.value}`));
    }
    return result;
}
function preventConflicts(type, a, b, ignoreNullability = false) {
    const aType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printTypeNode"])(a.type);
    const bType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printTypeNode"])(b.type);
    if (aType !== bType) {
        const t1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractType"])(a.type);
        const t2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractType"])(b.type);
        if (t1.name.value !== t2.name.value) {
            throw new Error(`Field "${b.name.value}" already defined with a different type. Declared as "${t1.name.value}", but you tried to override with "${t2.name.value}"`);
        }
        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {
            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);
        }
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(b.type) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(a.type)) {
        a.type = b.type;
    }
    return a;
}
function safeChangeForFieldType(oldType, newType, ignoreNullability = false) {
    // both are named
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isWrappingTypeNode"])(oldType) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isWrappingTypeNode"])(newType)) {
        return oldType.toString() === newType.toString();
    }
    // new is non-null
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(newType)) {
        const ofType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(oldType) ? oldType.type : oldType;
        return safeChangeForFieldType(ofType, newType.type);
    }
    // old is non-null
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(oldType)) {
        return safeChangeForFieldType(newType, oldType, ignoreNullability);
    }
    // old is list
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListTypeNode"])(oldType)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListTypeNode"])(newType) && safeChangeForFieldType(oldType.type, newType.type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(newType) && safeChangeForFieldType(oldType, newType['type']);
    }
    return false;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeNamedTypeArray": (()=>mergeNamedTypeArray)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function alreadyExists(arr, other) {
    return !!arr.find((i)=>i.name.value === other.name.value);
}
function mergeNamedTypeArray(first = [], second = [], config = {}) {
    const result = [
        ...second,
        ...first.filter((d)=>!alreadyExists(second, d))
    ];
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/type.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeType": (()=>mergeType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
;
function mergeType(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || node.kind === 'ObjectTypeDefinition' || existingNode.kind === 'ObjectTypeDefinition' ? 'ObjectTypeDefinition' : 'ObjectTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
                interfaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(node.interfaces, existingNode.interfaces, config)
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL type "${node.name.value}": ${e.message}`);
        }
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum-values.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeEnumValues": (()=>mergeEnumValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
;
function mergeEnumValues(first, second, config, directives) {
    if (config === null || config === void 0 ? void 0 : config.consistentEnumMerge) {
        const reversed = [];
        if (first) {
            reversed.push(...first);
        }
        first = second;
        second = reversed;
    }
    const enumValueMap = new Map();
    if (first) {
        for (const firstValue of first){
            enumValueMap.set(firstValue.name.value, firstValue);
        }
    }
    if (second) {
        for (const secondValue of second){
            const enumValue = secondValue.name.value;
            if (enumValueMap.has(enumValue)) {
                const firstValue = enumValueMap.get(enumValue);
                firstValue.description = secondValue.description || firstValue.description;
                firstValue.directives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(secondValue.directives, firstValue.directives, directives);
            } else {
                enumValueMap.set(enumValue, secondValue);
            }
        }
    }
    const result = [
        ...enumValueMap.values()
    ];
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeEnum": (()=>mergeEnum)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2d$values$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum-values.js [app-route] (ecmascript)");
;
;
;
function mergeEnum(e1, e2, config, directives) {
    if (e2) {
        return {
            name: e1.name,
            description: e1['description'] || e2['description'],
            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || e1.kind === 'EnumTypeDefinition' || e2.kind === 'EnumTypeDefinition' ? 'EnumTypeDefinition' : 'EnumTypeExtension',
            loc: e1.loc,
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(e1.directives, e2.directives, config, directives),
            values: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2d$values$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeEnumValues"])(e1.values, e2.values, config)
        };
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...e1,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_DEFINITION
    } : e1;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/scalar.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeScalar": (()=>mergeScalar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
function mergeScalar(node, existingNode, config, directives) {
    if (existingNode) {
        return {
            name: node.name,
            description: node['description'] || existingNode['description'],
            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || node.kind === 'ScalarTypeDefinition' || existingNode.kind === 'ScalarTypeDefinition' ? 'ScalarTypeDefinition' : 'ScalarTypeExtension',
            loc: node.loc,
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives)
        };
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/union.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeUnion": (()=>mergeUnion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
function mergeUnion(first, second, config, directives) {
    if (second) {
        return {
            name: first.name,
            description: first['description'] || second['description'],
            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(first.directives, second.directives, config, directives),
            kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || first.kind === 'UnionTypeDefinition' || second.kind === 'UnionTypeDefinition' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_EXTENSION,
            loc: first.loc,
            types: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(first.types, second.types, config)
        };
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...first,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION
    } : first;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/input-type.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeInputType": (()=>mergeInputType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
;
function mergeInputType(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || node.kind === 'InputObjectTypeDefinition' || existingNode.kind === 'InputObjectTypeDefinition' ? 'InputObjectTypeDefinition' : 'InputObjectTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives)
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL input type "${node.name.value}": ${e.message}`);
        }
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeInterface": (()=>mergeInterface)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
;
function mergeInterface(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: (config === null || config === void 0 ? void 0 : config.convertExtensions) || node.kind === 'InterfaceTypeDefinition' || existingNode.kind === 'InterfaceTypeDefinition' ? 'InterfaceTypeDefinition' : 'InterfaceTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
                interfaces: node['interfaces'] ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(node['interfaces'], existingNode['interfaces'], config) : undefined
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL interface "${node.name.value}": ${e.message}`);
        }
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_OPERATION_TYPE_NAME_MAP": (()=>DEFAULT_OPERATION_TYPE_NAME_MAP),
    "mergeSchemaDefs": (()=>mergeSchemaDefs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
const DEFAULT_OPERATION_TYPE_NAME_MAP = {
    query: 'Query',
    mutation: 'Mutation',
    subscription: 'Subscription'
};
function mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {
    const finalOpNodeList = [];
    for(const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP){
        const opNode = opNodeList.find((n)=>n.operation === opNodeType) || existingOpNodeList.find((n)=>n.operation === opNodeType);
        if (opNode) {
            finalOpNodeList.push(opNode);
        }
    }
    return finalOpNodeList;
}
function mergeSchemaDefs(node, existingNode, config, directives) {
    if (existingNode) {
        return {
            kind: node.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION || existingNode.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_EXTENSION,
            description: node['description'] || existingNode['description'],
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes)
        };
    }
    return (config === null || config === void 0 ? void 0 : config.convertExtensions) ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isNamedDefinitionNode": (()=>isNamedDefinitionNode),
    "mergeGraphQLNodes": (()=>mergeGraphQLNodes),
    "schemaDefSymbol": (()=>schemaDefSymbol)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$scalar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/scalar.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$union$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/union.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$input$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/input-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$interface$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/comments.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';
function isNamedDefinitionNode(definitionNode) {
    return 'name' in definitionNode;
}
function mergeGraphQLNodes(nodes, config, directives = {}) {
    var _a, _b, _c;
    const mergedResultMap = directives;
    for (const nodeDefinition of nodes){
        if (isNamedDefinitionNode(nodeDefinition)) {
            const name = (_a = nodeDefinition.name) === null || _a === void 0 ? void 0 : _a.value;
            if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["collectComment"])(nodeDefinition);
            }
            if (name == null) {
                continue;
            }
            if (((_b = config === null || config === void 0 ? void 0 : config.exclusions) === null || _b === void 0 ? void 0 : _b.includes(name + '.*')) || ((_c = config === null || config === void 0 ? void 0 : config.exclusions) === null || _c === void 0 ? void 0 : _c.includes(name))) {
                delete mergedResultMap[name];
            } else {
                switch(nodeDefinition.kind){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeType"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeEnum"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$union$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeUnion"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$scalar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeScalar"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$input$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeInputType"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$interface$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeInterface"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE_DEFINITION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirective"])(nodeDefinition, mergedResultMap[name]);
                        break;
                }
            }
        } else if (nodeDefinition.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION || nodeDefinition.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_EXTENSION) {
            mergedResultMap[schemaDefSymbol] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeSchemaDefs"])(nodeDefinition, mergedResultMap[schemaDefSymbol], config);
        }
    }
    return mergedResultMap;
}
}}),
"[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeGraphQLTypes": (()=>mergeGraphQLTypes),
    "mergeTypeDefs": (()=>mergeTypeDefs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/schema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$predicates$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/predicates.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$print$2d$schema$2d$with$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/print-schema-with-directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$isDocumentNode$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/isDocumentNode.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/utils/esm/comments.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@apollo/server/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)");
;
;
;
;
;
function mergeTypeDefs(typeSource, config) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    const doc = {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT,
        definitions: mergeGraphQLTypes(typeSource, {
            useSchemaDefinition: true,
            forceSchemaDefinition: false,
            throwOnConflict: false,
            commentDescriptions: false,
            ...config
        })
    };
    let result;
    if (config === null || config === void 0 ? void 0 : config.commentDescriptions) {
        result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printWithComments"])(doc);
    } else {
        result = doc;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    return result;
}
function visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {
    if (typeSource && !visitedTypeSources.has(typeSource)) {
        visitedTypeSources.add(typeSource);
        if (typeof typeSource === 'function') {
            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);
        } else if (Array.isArray(typeSource)) {
            for (const type of typeSource){
                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSchema"])(typeSource)) {
            const documentNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$print$2d$schema$2d$with$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDocumentNodeFromSchema"])(typeSource, options);
            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isStringTypes"])(typeSource) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSourceTypes"])(typeSource)) {
            const documentNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(typeSource, options);
            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else if (typeof typeSource === 'object' && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$predicates$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefinitionNode"])(typeSource)) {
            if (typeSource.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE_DEFINITION) {
                allDirectives.push(typeSource);
            } else {
                allNodes.push(typeSource);
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$isDocumentNode$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDocumentNode"])(typeSource)) {
            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else {
            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);
        }
    }
    return {
        allDirectives,
        allNodes
    };
}
function mergeGraphQLTypes(typeSource, config) {
    var _a, _b, _c;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);
    const mergedDirectives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeGraphQLNodes"])(allDirectives, config);
    const mergedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeGraphQLNodes"])(allNodes, config, mergedDirectives);
    if (config === null || config === void 0 ? void 0 : config.useSchemaDefinition) {
        // XXX: right now we don't handle multiple schema definitions
        const schemaDef = mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] || {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION,
            operationTypes: []
        };
        const operationTypes = schemaDef.operationTypes;
        for(const opTypeDefNodeType in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_OPERATION_TYPE_NAME_MAP"]){
            const opTypeDefNode = operationTypes.find((operationType)=>operationType.operation === opTypeDefNodeType);
            if (!opTypeDefNode) {
                const possibleRootTypeName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_OPERATION_TYPE_NAME_MAP"][opTypeDefNodeType];
                const existingPossibleRootType = mergedNodes[possibleRootTypeName];
                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {
                    operationTypes.push({
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OPERATION_TYPE_DEFINITION,
                        type: {
                            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE,
                            name: existingPossibleRootType.name
                        },
                        operation: opTypeDefNodeType
                    });
                }
            }
        }
        if (((_a = schemaDef === null || schemaDef === void 0 ? void 0 : schemaDef.operationTypes) === null || _a === void 0 ? void 0 : _a.length) != null && schemaDef.operationTypes.length > 0) {
            mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] = schemaDef;
        }
    }
    if ((config === null || config === void 0 ? void 0 : config.forceSchemaDefinition) && !((_c = (_b = mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]]) === null || _b === void 0 ? void 0 : _b.operationTypes) === null || _c === void 0 ? void 0 : _c.length)) {
        mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] = {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION,
            operationTypes: [
                {
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OPERATION_TYPE_DEFINITION,
                    operation: 'query',
                    type: {
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE,
                        name: {
                            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                            value: 'Query'
                        }
                    }
                }
            ]
        };
    }
    const mergedNodeDefinitions = Object.values(mergedNodes);
    if (config === null || config === void 0 ? void 0 : config.sort) {
        const sortFn = typeof config.sort === 'function' ? config.sort : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$apollo$2f$server$2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultStringComparator"];
        mergedNodeDefinitions.sort((a, b)=>{
            var _a, _b;
            return sortFn((_a = a.name) === null || _a === void 0 ? void 0 : _a.value, (_b = b.name) === null || _b === void 0 ? void 0 : _b.value);
        });
    }
    return mergedNodeDefinitions;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/extensions.js [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyExtensions": (()=>applyExtensions),
    "mergeExtensions": (()=>mergeExtensions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/mergeDeep.js [app-route] (ecmascript)");
;
;
function mergeExtensions(extensions) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])(extensions, false, true);
}
function applyExtensionObject(obj, extensions) {
    if (!obj || !extensions || extensions === obj.extensions) {
        return;
    }
    if (!obj.extensions) {
        obj.extensions = extensions;
        return;
    }
    obj.extensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])([
        obj.extensions,
        extensions
    ], false, true);
}
function applyExtensions(schema, extensions) {
    applyExtensionObject(schema, extensions.schemaExtensions);
    for (const [typeName, data] of Object.entries(extensions.types || {})){
        const type = schema.getType(typeName);
        if (type) {
            applyExtensionObject(type, data.extensions);
            if (data.type === 'object' || data.type === 'interface') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)){
                    const field = type.getFields()[fieldName];
                    if (field) {
                        applyExtensionObject(field, fieldData.extensions);
                        for (const [arg, argData] of Object.entries(fieldData.arguments)){
                            applyExtensionObject(field.args.find((a)=>a.name === arg), argData);
                        }
                    }
                }
            } else if (data.type === 'input') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)){
                    const field = type.getFields()[fieldName];
                    applyExtensionObject(field, fieldData.extensions);
                }
            } else if (data.type === 'enum') {
                for (const [valueName, valueData] of Object.entries(data.values)){
                    const value = type.getValue(valueName);
                    applyExtensionObject(value, valueData);
                }
            }
        }
    }
    return schema;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/merge-resolvers.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeResolvers": (()=>mergeResolvers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/mergeDeep.js [app-route] (ecmascript)");
;
function mergeResolvers(resolversDefinitions, options) {
    if (!resolversDefinitions || Array.isArray(resolversDefinitions) && resolversDefinitions.length === 0) {
        return {};
    }
    if (!Array.isArray(resolversDefinitions)) {
        return resolversDefinitions;
    }
    if (resolversDefinitions.length === 1) {
        return resolversDefinitions[0] || {};
    }
    const resolvers = new Array();
    for (let resolversDefinition of resolversDefinitions){
        if (Array.isArray(resolversDefinition)) {
            resolversDefinition = mergeResolvers(resolversDefinition);
        }
        if (typeof resolversDefinition === 'object' && resolversDefinition) {
            resolvers.push(resolversDefinition);
        }
    }
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$mergeDeep$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDeep"])(resolvers, true);
    if (options?.exclusions) {
        for (const exclusion of options.exclusions){
            const [typeName, fieldName] = exclusion.split('.');
            if ([
                '__proto__',
                'constructor',
                'prototype'
            ].includes(typeName) || [
                '__proto__',
                'constructor',
                'prototype'
            ].includes(fieldName)) {
                continue;
            }
            if (!fieldName || fieldName === '*') {
                delete result[typeName];
            } else if (result[typeName]) {
                delete result[typeName][fieldName];
            }
        }
    }
    return result;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeDirective": (()=>mergeDirective),
    "mergeDirectives": (()=>mergeDirectives)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function directiveAlreadyExists(directivesArr, otherDirective) {
    return !!directivesArr.find((directive)=>directive.name.value === otherDirective.name.value);
}
function isRepeatableDirective(directive, directives) {
    return !!directives?.[directive.name.value]?.repeatable;
}
function nameAlreadyExists(name, namesArr) {
    return namesArr.some(({ value })=>value === name.value);
}
function mergeArguments(a1, a2) {
    const result = [
        ...a2
    ];
    for (const argument of a1){
        const existingIndex = result.findIndex((a)=>a.name.value === argument.name.value);
        if (existingIndex > -1) {
            const existingArg = result[existingIndex];
            if (existingArg.value.kind === 'ListValue') {
                const source = existingArg.value.values;
                const target = argument.value.values;
                // merge values of two lists
                existingArg.value.values = deduplicateLists(source, target, (targetVal, source)=>{
                    const value = targetVal.value;
                    return !value || !source.some((sourceVal)=>sourceVal.value === value);
                });
            } else {
                existingArg.value = argument.value;
            }
        } else {
            result.push(argument);
        }
    }
    return result;
}
function deduplicateDirectives(directives, definitions) {
    return directives.map((directive, i, all)=>{
        const firstAt = all.findIndex((d)=>d.name.value === directive.name.value);
        if (firstAt !== i && !isRepeatableDirective(directive, definitions)) {
            const dup = all[firstAt];
            directive.arguments = mergeArguments(directive.arguments, dup.arguments);
            return null;
        }
        return directive;
    }).filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"]);
}
function mergeDirectives(d1 = [], d2 = [], config, directives) {
    const reverseOrder = config && config.reverseDirectives;
    const asNext = reverseOrder ? d1 : d2;
    const asFirst = reverseOrder ? d2 : d1;
    const result = deduplicateDirectives([
        ...asNext
    ], directives);
    for (const directive of asFirst){
        if (directiveAlreadyExists(result, directive) && !isRepeatableDirective(directive, directives)) {
            const existingDirectiveIndex = result.findIndex((d)=>d.name.value === directive.name.value);
            const existingDirective = result[existingDirectiveIndex];
            result[existingDirectiveIndex].arguments = mergeArguments(directive.arguments || [], existingDirective.arguments || []);
        } else {
            result.push(directive);
        }
    }
    return result;
}
function mergeDirective(node, existingNode) {
    if (existingNode) {
        return {
            ...node,
            arguments: deduplicateLists(existingNode.arguments || [], node.arguments || [], (arg, existingArgs)=>!nameAlreadyExists(arg.name, existingArgs.map((a)=>a.name))),
            locations: [
                ...existingNode.locations,
                ...node.locations.filter((name)=>!nameAlreadyExists(name, existingNode.locations))
            ]
        };
    }
    return node;
}
function deduplicateLists(source, target, filterFn) {
    return source.concat(target.filter((val)=>filterFn(val, source)));
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum-values.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeEnumValues": (()=>mergeEnumValues)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
function mergeEnumValues(first, second, config, directives) {
    if (config?.consistentEnumMerge) {
        const reversed = [];
        if (first) {
            reversed.push(...first);
        }
        first = second;
        second = reversed;
    }
    const enumValueMap = new Map();
    if (first) {
        for (const firstValue of first){
            enumValueMap.set(firstValue.name.value, firstValue);
        }
    }
    if (second) {
        for (const secondValue of second){
            const enumValue = secondValue.name.value;
            if (enumValueMap.has(enumValue)) {
                const firstValue = enumValueMap.get(enumValue);
                firstValue.description = secondValue.description || firstValue.description;
                firstValue.directives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(secondValue.directives, firstValue.directives, directives);
            } else {
                enumValueMap.set(enumValue, secondValue);
            }
        }
    }
    const result = [
        ...enumValueMap.values()
    ];
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeEnum": (()=>mergeEnum)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2d$values$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum-values.js [app-route] (ecmascript)");
;
;
;
function mergeEnum(e1, e2, config, directives) {
    if (e2) {
        return {
            name: e1.name,
            description: e1['description'] || e2['description'],
            kind: config?.convertExtensions || e1.kind === 'EnumTypeDefinition' || e2.kind === 'EnumTypeDefinition' ? 'EnumTypeDefinition' : 'EnumTypeExtension',
            loc: e1.loc,
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(e1.directives, e2.directives, config, directives),
            values: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2d$values$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeEnumValues"])(e1.values, e2.values, config)
        };
    }
    return config?.convertExtensions ? {
        ...e1,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_DEFINITION
    } : e1;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/arguments.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeArguments": (()=>mergeArguments)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function mergeArguments(args1, args2, config) {
    const result = deduplicateArguments([
        ...args2,
        ...args1
    ].filter(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSome"]), config);
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
function deduplicateArguments(args, config) {
    return args.reduce((acc, current)=>{
        const dupIndex = acc.findIndex((arg)=>arg.name.value === current.name.value);
        if (dupIndex === -1) {
            return acc.concat([
                current
            ]);
        } else if (!config?.reverseArguments) {
            acc[dupIndex] = current;
        }
        return acc;
    }, []);
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompareVal": (()=>CompareVal),
    "defaultStringComparator": (()=>defaultStringComparator),
    "extractType": (()=>extractType),
    "isListTypeNode": (()=>isListTypeNode),
    "isNonNullTypeNode": (()=>isNonNullTypeNode),
    "isSourceTypes": (()=>isSourceTypes),
    "isStringTypes": (()=>isStringTypes),
    "isWrappingTypeNode": (()=>isWrappingTypeNode),
    "printTypeNode": (()=>printTypeNode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$source$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/source.mjs [app-route] (ecmascript)");
;
function isStringTypes(types) {
    return typeof types === 'string';
}
function isSourceTypes(types) {
    return types instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$source$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Source"];
}
function extractType(type) {
    let visitedType = type;
    while(visitedType.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE || visitedType.kind === 'NonNullType'){
        visitedType = visitedType.type;
    }
    return visitedType;
}
function isWrappingTypeNode(type) {
    return type.kind !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE;
}
function isListTypeNode(type) {
    return type.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].LIST_TYPE;
}
function isNonNullTypeNode(type) {
    return type.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NON_NULL_TYPE;
}
function printTypeNode(type) {
    if (isListTypeNode(type)) {
        return `[${printTypeNode(type.type)}]`;
    }
    if (isNonNullTypeNode(type)) {
        return `${printTypeNode(type.type)}!`;
    }
    return type.name.value;
}
var CompareVal;
(function(CompareVal) {
    CompareVal[CompareVal["A_SMALLER_THAN_B"] = -1] = "A_SMALLER_THAN_B";
    CompareVal[CompareVal["A_EQUALS_B"] = 0] = "A_EQUALS_B";
    CompareVal[CompareVal["A_GREATER_THAN_B"] = 1] = "A_GREATER_THAN_B";
})(CompareVal || (CompareVal = {}));
function defaultStringComparator(a, b) {
    if (a == null && b == null) {
        return CompareVal.A_EQUALS_B;
    }
    if (a == null) {
        return CompareVal.A_SMALLER_THAN_B;
    }
    if (b == null) {
        return CompareVal.A_GREATER_THAN_B;
    }
    if (a < b) return CompareVal.A_SMALLER_THAN_B;
    if (a > b) return CompareVal.A_GREATER_THAN_B;
    return CompareVal.A_EQUALS_B;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeFields": (()=>mergeFields)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$arguments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/arguments.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)");
;
;
;
;
function fieldAlreadyExists(fieldsArr, otherField) {
    const resultIndex = fieldsArr.findIndex((field)=>field.name.value === otherField.name.value);
    return [
        resultIndex > -1 ? fieldsArr[resultIndex] : null,
        resultIndex
    ];
}
function mergeFields(type, f1, f2, config, directives) {
    const result = [];
    if (f2 != null) {
        result.push(...f2);
    }
    if (f1 != null) {
        for (const field of f1){
            const [existing, existingIndex] = fieldAlreadyExists(result, field);
            if (existing && !config?.ignoreFieldConflicts) {
                const newField = config?.onFieldTypeConflict && config.onFieldTypeConflict(existing, field, type, config?.throwOnConflict) || preventConflicts(type, existing, field, config?.throwOnConflict);
                newField.arguments = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$arguments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeArguments"])(field['arguments'] || [], existing['arguments'] || [], config);
                newField.directives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(field.directives, existing.directives, config, directives);
                newField.description = field.description || existing.description;
                result[existingIndex] = newField;
            } else {
                result.push(field);
            }
        }
    }
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    if (config && config.exclusions) {
        const exclusions = config.exclusions;
        return result.filter((field)=>!exclusions.includes(`${type.name.value}.${field.name.value}`));
    }
    return result;
}
function preventConflicts(type, a, b, ignoreNullability = false) {
    const aType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printTypeNode"])(a.type);
    const bType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printTypeNode"])(b.type);
    if (aType !== bType) {
        const t1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractType"])(a.type);
        const t2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractType"])(b.type);
        if (t1.name.value !== t2.name.value) {
            throw new Error(`Field "${b.name.value}" already defined with a different type. Declared as "${t1.name.value}", but you tried to override with "${t2.name.value}"`);
        }
        if (!safeChangeForFieldType(a.type, b.type, !ignoreNullability)) {
            throw new Error(`Field '${type.name.value}.${a.name.value}' changed type from '${aType}' to '${bType}'`);
        }
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(b.type) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(a.type)) {
        a.type = b.type;
    }
    return a;
}
function safeChangeForFieldType(oldType, newType, ignoreNullability = false) {
    // both are named
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isWrappingTypeNode"])(oldType) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isWrappingTypeNode"])(newType)) {
        return oldType.toString() === newType.toString();
    }
    // new is non-null
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(newType)) {
        const ofType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(oldType) ? oldType.type : oldType;
        return safeChangeForFieldType(ofType, newType.type);
    }
    // old is non-null
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(oldType)) {
        return safeChangeForFieldType(newType, oldType, ignoreNullability);
    }
    // old is list
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListTypeNode"])(oldType)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isListTypeNode"])(newType) && safeChangeForFieldType(oldType.type, newType.type) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isNonNullTypeNode"])(newType) && safeChangeForFieldType(oldType, newType['type']);
    }
    return false;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/input-type.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeInputType": (()=>mergeInputType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
;
;
;
function mergeInputType(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: config?.convertExtensions || node.kind === 'InputObjectTypeDefinition' || existingNode.kind === 'InputObjectTypeDefinition' ? 'InputObjectTypeDefinition' : 'InputObjectTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives)
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL input type "${node.name.value}": ${e.message}`);
        }
    }
    return config?.convertExtensions ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeNamedTypeArray": (()=>mergeNamedTypeArray)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/helpers.js [app-route] (ecmascript)");
;
function alreadyExists(arr, other) {
    return !!arr.find((i)=>i.name.value === other.name.value);
}
function mergeNamedTypeArray(first = [], second = [], config = {}) {
    const result = [
        ...second,
        ...first.filter((d)=>!alreadyExists(second, d))
    ];
    if (config && config.sort) {
        result.sort(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$helpers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["compareNodes"]);
    }
    return result;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeInterface": (()=>mergeInterface)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
;
function mergeInterface(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: config?.convertExtensions || node.kind === 'InterfaceTypeDefinition' || existingNode.kind === 'InterfaceTypeDefinition' ? 'InterfaceTypeDefinition' : 'InterfaceTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config, directives),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
                interfaces: node['interfaces'] ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(node['interfaces'], existingNode['interfaces'], config) : undefined
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL interface "${node.name.value}": ${e.message}`);
        }
    }
    return config?.convertExtensions ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/scalar.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeScalar": (()=>mergeScalar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
function mergeScalar(node, existingNode, config, directives) {
    if (existingNode) {
        return {
            name: node.name,
            description: node['description'] || existingNode['description'],
            kind: config?.convertExtensions || node.kind === 'ScalarTypeDefinition' || existingNode.kind === 'ScalarTypeDefinition' ? 'ScalarTypeDefinition' : 'ScalarTypeExtension',
            loc: node.loc,
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives)
        };
    }
    return config?.convertExtensions ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_OPERATION_TYPE_NAME_MAP": (()=>DEFAULT_OPERATION_TYPE_NAME_MAP),
    "mergeSchemaDefs": (()=>mergeSchemaDefs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
;
;
const DEFAULT_OPERATION_TYPE_NAME_MAP = {
    query: 'Query',
    mutation: 'Mutation',
    subscription: 'Subscription'
};
function mergeOperationTypes(opNodeList = [], existingOpNodeList = []) {
    const finalOpNodeList = [];
    for(const opNodeType in DEFAULT_OPERATION_TYPE_NAME_MAP){
        const opNode = opNodeList.find((n)=>n.operation === opNodeType) || existingOpNodeList.find((n)=>n.operation === opNodeType);
        if (opNode) {
            finalOpNodeList.push(opNode);
        }
    }
    return finalOpNodeList;
}
function mergeSchemaDefs(node, existingNode, config, directives) {
    if (existingNode) {
        return {
            kind: node.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION || existingNode.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_EXTENSION,
            description: node['description'] || existingNode['description'],
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
            operationTypes: mergeOperationTypes(node.operationTypes, existingNode.operationTypes)
        };
    }
    return config?.convertExtensions ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/type.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeType": (()=>mergeType)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/fields.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
;
function mergeType(node, existingNode, config, directives) {
    if (existingNode) {
        try {
            return {
                name: node.name,
                description: node['description'] || existingNode['description'],
                kind: config?.convertExtensions || node.kind === 'ObjectTypeDefinition' || existingNode.kind === 'ObjectTypeDefinition' ? 'ObjectTypeDefinition' : 'ObjectTypeExtension',
                loc: node.loc,
                fields: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$fields$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeFields"])(node, node.fields, existingNode.fields, config, directives),
                directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(node.directives, existingNode.directives, config, directives),
                interfaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(node.interfaces, existingNode.interfaces, config)
            };
        } catch (e) {
            throw new Error(`Unable to merge GraphQL type "${node.name.value}": ${e.message}`);
        }
    }
    return config?.convertExtensions ? {
        ...node,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION
    } : node;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/union.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeUnion": (()=>mergeUnion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-named-type-array.js [app-route] (ecmascript)");
;
;
;
function mergeUnion(first, second, config, directives) {
    if (second) {
        return {
            name: first.name,
            description: first['description'] || second['description'],
            // ConstXNode has been introduced in v16 but it is not compatible with XNode so we do `as any` for backwards compatibility
            directives: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirectives"])(first.directives, second.directives, config, directives),
            kind: config?.convertExtensions || first.kind === 'UnionTypeDefinition' || second.kind === 'UnionTypeDefinition' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_EXTENSION,
            loc: first.loc,
            types: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$named$2d$type$2d$array$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeNamedTypeArray"])(first.types, second.types, config)
        };
    }
    return config?.convertExtensions ? {
        ...first,
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION
    } : first;
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "isNamedDefinitionNode": (()=>isNamedDefinitionNode),
    "mergeGraphQLNodes": (()=>mergeGraphQLNodes),
    "schemaDefSymbol": (()=>schemaDefSymbol)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/comments.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/enum.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$input$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/input-type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$interface$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/interface.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$scalar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/scalar.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/type.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$union$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/union.js [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const schemaDefSymbol = 'SCHEMA_DEF_SYMBOL';
function isNamedDefinitionNode(definitionNode) {
    return 'name' in definitionNode;
}
function mergeGraphQLNodes(nodes, config, directives = {}) {
    const mergedResultMap = directives;
    for (const nodeDefinition of nodes){
        if (isNamedDefinitionNode(nodeDefinition)) {
            const name = nodeDefinition.name?.value;
            if (config?.commentDescriptions) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["collectComment"])(nodeDefinition);
            }
            if (name == null) {
                continue;
            }
            if (config?.exclusions?.includes(name + '.*') || config?.exclusions?.includes(name)) {
                delete mergedResultMap[name];
            } else {
                switch(nodeDefinition.kind){
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OBJECT_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeType"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].ENUM_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$enum$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeEnum"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].UNION_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$union$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeUnion"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCALAR_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$scalar$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeScalar"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INPUT_OBJECT_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$input$2d$type$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeInputType"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_DEFINITION:
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].INTERFACE_TYPE_EXTENSION:
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$interface$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeInterface"])(nodeDefinition, mergedResultMap[name], config, directives);
                        break;
                    case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE_DEFINITION:
                        if (mergedResultMap[name]) {
                            const isInheritedFromPrototype = name in {}; // i.e. toString
                            if (isInheritedFromPrototype) {
                                if (!isASTNode(mergedResultMap[name])) {
                                    mergedResultMap[name] = undefined;
                                }
                            }
                        }
                        mergedResultMap[name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeDirective"])(nodeDefinition, mergedResultMap[name]);
                        break;
                }
            }
        } else if (nodeDefinition.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION || nodeDefinition.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_EXTENSION) {
            mergedResultMap[schemaDefSymbol] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeSchemaDefs"])(nodeDefinition, mergedResultMap[schemaDefSymbol], config);
        }
    }
    return mergedResultMap;
}
function isASTNode(node) {
    return node != null && typeof node === 'object' && 'kind' in node && typeof node.kind === 'string';
}
}}),
"[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-typedefs.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "mergeGraphQLTypes": (()=>mergeGraphQLTypes),
    "mergeTypeDefs": (()=>mergeTypeDefs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$predicates$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/predicates.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/type/schema.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/kinds.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/graphql/language/parser.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$print$2d$schema$2d$with$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/print-schema-with-directives.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$isDocumentNode$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/isDocumentNode.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/utils/esm/comments.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/merge-nodes.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/schema-def.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@graphql-tools/merge/esm/typedefs-mergers/utils.js [app-route] (ecmascript)");
;
;
;
;
;
function mergeTypeDefs(typeSource, config) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    const doc = {
        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DOCUMENT,
        definitions: mergeGraphQLTypes(typeSource, {
            useSchemaDefinition: true,
            forceSchemaDefinition: false,
            throwOnConflict: false,
            commentDescriptions: false,
            ...config
        })
    };
    let result;
    if (config?.commentDescriptions) {
        result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["printWithComments"])(doc);
    } else {
        result = doc;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    return result;
}
function visitTypeSources(typeSource, options, allDirectives = [], allNodes = [], visitedTypeSources = new Set()) {
    if (typeSource && !visitedTypeSources.has(typeSource)) {
        visitedTypeSources.add(typeSource);
        if (typeof typeSource === 'function') {
            visitTypeSources(typeSource(), options, allDirectives, allNodes, visitedTypeSources);
        } else if (Array.isArray(typeSource)) {
            for (const type of typeSource){
                visitTypeSources(type, options, allDirectives, allNodes, visitedTypeSources);
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$type$2f$schema$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSchema"])(typeSource)) {
            const documentNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$print$2d$schema$2d$with$2d$directives$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getDocumentNodeFromSchema"])(typeSource, options);
            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isStringTypes"])(typeSource) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isSourceTypes"])(typeSource)) {
            const documentNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$parser$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(typeSource, options);
            visitTypeSources(documentNode.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else if (typeof typeSource === 'object' && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$predicates$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDefinitionNode"])(typeSource)) {
            if (typeSource.kind === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].DIRECTIVE_DEFINITION) {
                allDirectives.push(typeSource);
            } else {
                allNodes.push(typeSource);
            }
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$isDocumentNode$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isDocumentNode"])(typeSource)) {
            visitTypeSources(typeSource.definitions, options, allDirectives, allNodes, visitedTypeSources);
        } else {
            throw new Error(`typeDefs must contain only strings, documents, schemas, or functions, got ${typeof typeSource}`);
        }
    }
    return {
        allDirectives,
        allNodes
    };
}
function mergeGraphQLTypes(typeSource, config) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$utils$2f$esm$2f$comments$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["resetComments"])();
    const { allDirectives, allNodes } = visitTypeSources(typeSource, config);
    const mergedDirectives = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeGraphQLNodes"])(allDirectives, config);
    const mergedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mergeGraphQLNodes"])(allNodes, config, mergedDirectives);
    if (config?.useSchemaDefinition) {
        // XXX: right now we don't handle multiple schema definitions
        const schemaDef = mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] || {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION,
            operationTypes: []
        };
        const operationTypes = schemaDef.operationTypes;
        for(const opTypeDefNodeType in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_OPERATION_TYPE_NAME_MAP"]){
            const opTypeDefNode = operationTypes.find((operationType)=>operationType.operation === opTypeDefNodeType);
            if (!opTypeDefNode) {
                const possibleRootTypeName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$schema$2d$def$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DEFAULT_OPERATION_TYPE_NAME_MAP"][opTypeDefNodeType];
                const existingPossibleRootType = mergedNodes[possibleRootTypeName];
                if (existingPossibleRootType != null && existingPossibleRootType.name != null) {
                    operationTypes.push({
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OPERATION_TYPE_DEFINITION,
                        type: {
                            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE,
                            name: existingPossibleRootType.name
                        },
                        operation: opTypeDefNodeType
                    });
                }
            }
        }
        if (schemaDef?.operationTypes?.length != null && schemaDef.operationTypes.length > 0) {
            mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] = schemaDef;
        }
    }
    if (config?.forceSchemaDefinition && !mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]]?.operationTypes?.length) {
        mergedNodes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$merge$2d$nodes$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["schemaDefSymbol"]] = {
            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].SCHEMA_DEFINITION,
            operationTypes: [
                {
                    kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].OPERATION_TYPE_DEFINITION,
                    operation: 'query',
                    type: {
                        kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAMED_TYPE,
                        name: {
                            kind: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$graphql$2f$language$2f$kinds$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Kind"].NAME,
                            value: 'Query'
                        }
                    }
                }
            ]
        };
    }
    const mergedNodeDefinitions = Object.values(mergedNodes);
    if (config?.sort) {
        const sortFn = typeof config.sort === 'function' ? config.sort : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$graphql$2d$tools$2f$merge$2f$esm$2f$typedefs$2d$mergers$2f$utils$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultStringComparator"];
        mergedNodeDefinitions.sort((a, b)=>sortFn(a.name?.value, b.name?.value));
    }
    return mergedNodeDefinitions;
}
}}),

};

//# sourceMappingURL=node_modules_292cdb59._.js.map