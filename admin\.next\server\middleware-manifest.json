{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "93q2+ThywVMICBZGrjqWrlW2FJpdwcjpUekbGqXbIAA=", "__NEXT_PREVIEW_MODE_ID": "b932360a9454526d3f43a083d5f2d932", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cb3f9795e26961bc5ff1089d8de08e11d9b1274e5f8515d07e1ab03d0ea995f3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a5a28f1e24ff2467de7788fbf5468ea3792c39bcf01fc4112dd73c3395ea1b7a"}}}, "sortedMiddleware": ["/"], "functions": {}}