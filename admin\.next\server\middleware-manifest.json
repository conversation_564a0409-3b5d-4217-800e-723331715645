{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_185a879a._.js", "server/edge/chunks/[root-of-the-server]__7adad445._.js", "server/edge/chunks/edge-wrapper_6c593f21.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tsMyrVI2Tt0Mru0I+urydOpTAE7ooWnUdN6SUm3T0ys=", "__NEXT_PREVIEW_MODE_ID": "76fb6d45d3b29816d8459f6248ec1d6a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1d4e2c7531849fb56c201c9894cdfe89a9c1a4fc140c8a85c21f65b856fc424b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "02db64d40f0af371d0e6cb8330375d15567a8926d66056e9730a0e541c15e658"}}}, "sortedMiddleware": ["/"], "functions": {}}